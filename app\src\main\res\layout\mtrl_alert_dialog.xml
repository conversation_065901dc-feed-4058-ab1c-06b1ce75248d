<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.AlertDialogLayout xmlns:android="http://schemas.android.com/apk/res/android" android:gravity="start|top" android:orientation="vertical" android:id="@+id/parentPanel" android:layout_width="match_parent" android:layout_height="wrap_content">
    <include layout="@layout/mtrl_alert_dialog_title"/>
    <FrameLayout android:id="@+id/contentPanel" android:layout_width="match_parent" android:layout_height="wrap_content" android:minHeight="48dp">
        <View android:layout_gravity="top" android:id="@+id/scrollIndicatorUp" android:background="?attr/colorControlHighlight" android:visibility="gone" android:layout_width="match_parent" android:layout_height="1dp"/>
        <androidx.core.widget.NestedScrollView android:id="@+id/scrollView" android:clipToPadding="false" android:layout_width="match_parent" android:layout_height="wrap_content">
            <LinearLayout android:orientation="vertical" android:layout_width="match_parent" android:layout_height="wrap_content">
                <android.widget.Space android:id="@+id/textSpacerNoTitle" android:visibility="gone" android:layout_width="match_parent" android:layout_height="@dimen/abc_dialog_padding_top_material"/>
                <TextView android:id="@android:id/message" android:paddingLeft="?attr/dialogPreferredPadding" android:paddingRight="?attr/dialogPreferredPadding" android:layout_width="match_parent" android:layout_height="wrap_content" style="?attr/materialAlertDialogBodyTextStyle"/>
                <android.widget.Space android:id="@+id/textSpacerNoButtons" android:visibility="gone" android:layout_width="match_parent" android:layout_height="@dimen/abc_dialog_padding_top_material"/>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
        <View android:layout_gravity="bottom" android:id="@+id/scrollIndicatorDown" android:background="?attr/colorControlHighlight" android:visibility="gone" android:layout_width="match_parent" android:layout_height="1dp"/>
    </FrameLayout>
    <FrameLayout android:id="@+id/customPanel" android:layout_width="match_parent" android:layout_height="wrap_content" android:minHeight="48dp">
        <FrameLayout android:id="@+id/custom" android:layout_width="match_parent" android:layout_height="wrap_content"/>
    </FrameLayout>
    <include android:layout_width="match_parent" android:layout_height="wrap_content" layout="@layout/mtrl_alert_dialog_actions"/>
</androidx.appcompat.widget.AlertDialogLayout>
