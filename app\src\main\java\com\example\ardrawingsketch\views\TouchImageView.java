package com.example.ardrawingsketch.views;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.PointF;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.OverScroller;
import androidx.appcompat.widget.AppCompatImageView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.NotificationCompat;
import com.example.ardrawingsketch.R;
import com.google.android.gms.common.internal.ServiceSpecificExtraArgs;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: TouchImageView.kt */
//@Metadata(d1 = {"\u0000ê\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u0014\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0018\u0002\n\u0002\b,\n\u0002\u0010\u0006\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\t\b\u0016\u0018\u0000 Ï\u00012\u00020\u0001:\u0010È\u0001É\u0001Ê\u0001Ë\u0001Ì\u0001Í\u0001Î\u0001Ï\u0001B'\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007¢\u0006\u0004\b\b\u0010\tJ\u000e\u0010P\u001a\u00020Q2\u0006\u0010R\u001a\u00020\u0013J\u0012\u0010S\u001a\u00020Q2\b\u0010T\u001a\u0004\u0018\u00010MH\u0016J\u000e\u0010U\u001a\u00020Q2\u0006\u0010V\u001a\u00020OJ\u000e\u0010W\u001a\u00020Q2\u0006\u0010X\u001a\u00020KJ\u000e\u0010Y\u001a\u00020Q2\u0006\u0010Z\u001a\u00020IJ\u0010\u0010[\u001a\u00020Q2\u0006\u0010\\\u001a\u00020\u0007H\u0016J\u0012\u0010]\u001a\u00020Q2\b\u0010^\u001a\u0004\u0018\u00010_H\u0016J\u0012\u0010`\u001a\u00020Q2\b\u0010a\u001a\u0004\u0018\u00010bH\u0016J\u0012\u0010c\u001a\u00020Q2\b\u0010d\u001a\u0004\u0018\u00010eH\u0016J\u0010\u0010f\u001a\u00020Q2\u0006\u0010g\u001a\u000207H\u0016J\b\u0010h\u001a\u000207H\u0016J\u0006\u0010n\u001a\u00020QJ\n\u0010o\u001a\u0004\u0018\u00010pH\u0016J\u0010\u0010q\u001a\u00020Q2\u0006\u0010r\u001a\u00020pH\u0016J\u0010\u0010s\u001a\u00020Q2\u0006\u0010t\u001a\u00020uH\u0014J\u0010\u0010v\u001a\u00020Q2\u0006\u0010w\u001a\u00020xH\u0016J\u000e\u0010}\u001a\u00020Q2\u0006\u0010y\u001a\u00020\u000bJ\u0007\u0010\u0082\u0001\u001a\u00020QJ\u0007\u0010\u0083\u0001\u001a\u00020QJ\u0010\u0010\u0084\u0001\u001a\u00020Q2\u0007\u0010\u0085\u0001\u001a\u00020\u000bJ\"\u0010\u0084\u0001\u001a\u00020Q2\u0007\u0010\u0085\u0001\u001a\u00020\u000b2\u0007\u0010\u0086\u0001\u001a\u00020\u000b2\u0007\u0010\u0087\u0001\u001a\u00020\u000bJ-\u0010\u0084\u0001\u001a\u00020Q2\u0007\u0010\u0085\u0001\u001a\u00020\u000b2\u0007\u0010\u0086\u0001\u001a\u00020\u000b2\u0007\u0010\u0087\u0001\u001a\u00020\u000b2\t\u0010\u0088\u0001\u001a\u0004\u0018\u000107J\u0010\u0010\u0084\u0001\u001a\u00020Q2\u0007\u0010\u0089\u0001\u001a\u00020\u0000J\u0013\u0010\u008e\u0001\u001a\u00020\u00132\b\u0010a\u001a\u0004\u0018\u00010bH\u0002J\u0013\u0010\u008f\u0001\u001a\u00020\u00072\b\u0010a\u001a\u0004\u0018\u00010bH\u0002J\u0013\u0010\u0090\u0001\u001a\u00020\u00072\b\u0010a\u001a\u0004\u0018\u00010bH\u0002J\u0019\u0010\u0091\u0001\u001a\u00020Q2\u0007\u0010\u0086\u0001\u001a\u00020\u000b2\u0007\u0010\u0087\u0001\u001a\u00020\u000bJ\t\u0010\u0092\u0001\u001a\u00020QH\u0002J\t\u0010\u0093\u0001\u001a\u00020QH\u0002J-\u0010\u0094\u0001\u001a\u00020\u000b2\u0007\u0010\u0095\u0001\u001a\u00020\u000b2\u0007\u0010\u0096\u0001\u001a\u00020\u000b2\u0007\u0010\u0097\u0001\u001a\u00020\u000b2\u0007\u0010\u0098\u0001\u001a\u00020\u000bH\u0002J$\u0010\u0099\u0001\u001a\u00020\u000b2\u0007\u0010\u009a\u0001\u001a\u00020\u000b2\u0007\u0010\u0096\u0001\u001a\u00020\u000b2\u0007\u0010\u0097\u0001\u001a\u00020\u000bH\u0002J\u001b\u0010\u009f\u0001\u001a\u00020Q2\u0007\u0010 \u0001\u001a\u00020\u00072\u0007\u0010¡\u0001\u001a\u00020\u0007H\u0014J-\u0010¢\u0001\u001a\u00020Q2\u0007\u0010£\u0001\u001a\u00020\u00072\u0007\u0010¤\u0001\u001a\u00020\u00072\u0007\u0010¥\u0001\u001a\u00020\u00072\u0007\u0010¦\u0001\u001a\u00020\u0007H\u0014J\t\u0010§\u0001\u001a\u00020QH\u0002J$\u0010¨\u0001\u001a\u00020\u00072\u0007\u0010©\u0001\u001a\u00020\u00072\u0007\u0010ª\u0001\u001a\u00020\u00072\u0007\u0010«\u0001\u001a\u00020\u0007H\u0002JJ\u0010¬\u0001\u001a\u00020\u000b2\u0007\u0010\u0095\u0001\u001a\u00020\u000b2\u0007\u0010\u00ad\u0001\u001a\u00020\u000b2\u0007\u0010®\u0001\u001a\u00020\u000b2\u0007\u0010¯\u0001\u001a\u00020\u00072\u0007\u0010\u0096\u0001\u001a\u00020\u00072\u0007\u0010°\u0001\u001a\u00020\u00072\t\u0010±\u0001\u001a\u0004\u0018\u00010\u001bH\u0002J\u0011\u0010²\u0001\u001a\u00020Q2\u0006\u0010$\u001a\u00020%H\u0002J\u0012\u0010³\u0001\u001a\u00020\u00132\u0007\u0010´\u0001\u001a\u00020\u0007H\u0016J\u0012\u0010µ\u0001\u001a\u00020\u00132\u0007\u0010´\u0001\u001a\u00020\u0007H\u0016J.\u0010¶\u0001\u001a\u00020Q2\b\u0010·\u0001\u001a\u00030¸\u00012\u0007\u0010\u0086\u0001\u001a\u00020\u000b2\u0007\u0010\u0087\u0001\u001a\u00020\u000b2\u0007\u0010¹\u0001\u001a\u00020\u0013H\u0002J%\u0010º\u0001\u001a\u00030\u008b\u00012\u0007\u0010»\u0001\u001a\u00020\u000b2\u0007\u0010¼\u0001\u001a\u00020\u000b2\u0007\u0010½\u0001\u001a\u00020\u0013H\u0004J\u001c\u0010¾\u0001\u001a\u00030\u008b\u00012\u0007\u0010¿\u0001\u001a\u00020\u000b2\u0007\u0010À\u0001\u001a\u00020\u000bH\u0004J\u0013\u0010Á\u0001\u001a\u00020Q2\b\u0010Â\u0001\u001a\u00030Ã\u0001H\u0002J\"\u0010Ä\u0001\u001a\u00020Q2\u0007\u0010\u0085\u0001\u001a\u00020\u000b2\u0007\u0010\u0086\u0001\u001a\u00020\u000b2\u0007\u0010\u0087\u0001\u001a\u00020\u000bJ+\u0010Ä\u0001\u001a\u00020Q2\u0007\u0010\u0085\u0001\u001a\u00020\u000b2\u0007\u0010\u0086\u0001\u001a\u00020\u000b2\u0007\u0010\u0087\u0001\u001a\u00020\u000b2\u0007\u0010Å\u0001\u001a\u00020\u0007J7\u0010Ä\u0001\u001a\u00020Q2\u0007\u0010\u0085\u0001\u001a\u00020\u000b2\u0007\u0010\u0086\u0001\u001a\u00020\u000b2\u0007\u0010\u0087\u0001\u001a\u00020\u000b2\u0007\u0010Å\u0001\u001a\u00020\u00072\n\u0010Æ\u0001\u001a\u0005\u0018\u00010Ç\u0001J.\u0010Ä\u0001\u001a\u00020Q2\u0007\u0010\u0085\u0001\u001a\u00020\u000b2\u0007\u0010\u0086\u0001\u001a\u00020\u000b2\u0007\u0010\u0087\u0001\u001a\u00020\u000b2\n\u0010Æ\u0001\u001a\u0005\u0018\u00010Ç\u0001R\u001e\u0010\f\u001a\u00020\u000b2\u0006\u0010\n\u001a\u00020\u000b@BX\u0086\u000e¢\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0010X\u0082\u000e¢\u0006\u0002\n\u0000R\u001a\u0010\u0012\u001a\u00020\u0013X\u0086\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\u0012\u0010\u0014\"\u0004\b\u0015\u0010\u0016R\u001a\u0010\u0017\u001a\u00020\u0013X\u0086\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\u0017\u0010\u0014\"\u0004\b\u0018\u0010\u0016R\u000e\u0010\u0019\u001a\u00020\u0013X\u0082\u000e¢\u0006\u0002\n\u0000R\u001c\u0010\u001a\u001a\u0004\u0018\u00010\u001bX\u0086\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\u001c\u0010\u001d\"\u0004\b\u001e\u0010\u001fR\u001c\u0010 \u001a\u0004\u0018\u00010\u001bX\u0086\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b!\u0010\u001d\"\u0004\b\"\u0010\u001fR\u000e\u0010#\u001a\u00020\u0013X\u0082\u000e¢\u0006\u0002\n\u0000R\u0010\u0010$\u001a\u0004\u0018\u00010%X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\u000bX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010'\u001a\u00020\u000bX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020\u0013X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010)\u001a\u00020\u000bX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020\u000bX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020\u000bX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010,\u001a\u00020\u000bX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020.X\u0082\u000e¢\u0006\u0002\n\u0000R\u001a\u0010/\u001a\u00020\u000bX\u0086\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b0\u0010\u000e\"\u0004\b1\u00102R\u0014\u00103\u001a\b\u0018\u000104R\u00020\u0000X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u00105\u001a\u00020\u0007X\u0082\u000e¢\u0006\u0002\n\u0000R\u0010\u00106\u001a\u0004\u0018\u000107X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u00108\u001a\u00020\u0013X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u00109\u001a\u00020\u0013X\u0082\u000e¢\u0006\u0002\n\u0000R\u0010\u0010:\u001a\u0004\u0018\u00010;X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010<\u001a\u00020\u0007X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010=\u001a\u00020\u0007X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010>\u001a\u00020\u0007X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010?\u001a\u00020\u0007X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010@\u001a\u00020\u000bX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010A\u001a\u00020\u000bX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010B\u001a\u00020\u000bX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010C\u001a\u00020\u000bX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010D\u001a\u00020EX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010F\u001a\u00020GX\u0082\u000e¢\u0006\u0002\n\u0000R\u0010\u0010H\u001a\u0004\u0018\u00010IX\u0082\u000e¢\u0006\u0002\n\u0000R\u0010\u0010J\u001a\u0004\u0018\u00010KX\u0082\u000e¢\u0006\u0002\n\u0000R\u0010\u0010L\u001a\u0004\u0018\u00010MX\u0082\u000e¢\u0006\u0002\n\u0000R\u0010\u0010N\u001a\u0004\u0018\u00010OX\u0082\u000e¢\u0006\u0002\n\u0000R\u0011\u0010i\u001a\u00020\u00138F¢\u0006\u0006\u001a\u0004\bi\u0010\u0014R\u0011\u0010j\u001a\u00020k8F¢\u0006\u0006\u001a\u0004\bl\u0010mR$\u0010z\u001a\u00020\u000b2\u0006\u0010y\u001a\u00020\u000b8F@FX\u0086\u000e¢\u0006\f\u001a\u0004\b{\u0010\u000e\"\u0004\b|\u00102R&\u0010\u007f\u001a\u00020\u000b2\u0006\u0010~\u001a\u00020\u000b8F@FX\u0086\u000e¢\u0006\u000e\u001a\u0005\b\u0080\u0001\u0010\u000e\"\u0005\b\u0081\u0001\u00102R\u0015\u0010\u008a\u0001\u001a\u00030\u008b\u00018F¢\u0006\b\u001a\u0006\b\u008c\u0001\u0010\u008d\u0001R\u0016\u0010\u009b\u0001\u001a\u00020\u000b8BX\u0082\u0004¢\u0006\u0007\u001a\u0005\b\u009c\u0001\u0010\u000eR\u0016\u0010\u009d\u0001\u001a\u00020\u000b8BX\u0082\u0004¢\u0006\u0007\u001a\u0005\b\u009e\u0001\u0010\u000e¨\u0006Ð\u0001"}, d2 = {"Lcom/example/ardrawsketch/sketch/views/TouchImageView;", "Landroidx/appcompat/widget/AppCompatImageView;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "defStyle", "", "<init>", "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "value", "", "currentZoom", "getCurrentZoom", "()F", "touchMatrix", "Landroid/graphics/Matrix;", "prevMatrix", "isZoomEnabled", "", "()Z", "setZoomEnabled", "(Z)V", "isSuperZoomEnabled", "setSuperZoomEnabled", "isRotateImageToFitScreen", "orientationChangeFixedPixel", "Lcom/example/ardrawsketch/sketch/views/FixedPixel;", "getOrientationChangeFixedPixel", "()Lcom/example/ardrawsketch/sketch/views/FixedPixel;", "setOrientationChangeFixedPixel", "(Lcom/example/ardrawsketch/sketch/views/FixedPixel;)V", "viewSizeChangeFixedPixel", "getViewSizeChangeFixedPixel", "setViewSizeChangeFixedPixel", "orientationJustChanged", "imageActionState", "Lcom/example/ardrawsketch/sketch/views/ImageActionState;", "userSpecifiedMinScale", "minScale", "maxScaleIsSetByMultiplier", "maxScaleMultiplier", "maxScale", "superMinScale", "superMaxScale", "floatMatrix", "", "doubleTapScale", "getDoubleTapScale", "setDoubleTapScale", "(F)V", "fling", "Lcom/example/ardrawsketch/sketch/views/TouchImageView$Fling;", "orientation", "touchScaleType", "Landroid/widget/ImageView$ScaleType;", "imageRenderedAtLeastOnce", "onDrawReady", "delayedZoomVariables", "Lcom/example/ardrawsketch/sketch/views/ZoomVariables;", "viewWidth", "viewHeight", "prevViewWidth", "prevViewHeight", "matchViewWidth", "matchViewHeight", "prevMatchViewWidth", "prevMatchViewHeight", "scaleDetector", "Landroid/view/ScaleGestureDetector;", "gestureDetector", "Landroid/view/GestureDetector;", "touchCoordinatesListener", "Lcom/example/ardrawsketch/sketch/views/OnTouchCoordinatesListener;", "doubleTapListener", "Landroid/view/GestureDetector$OnDoubleTapListener;", "userTouchListener", "Landroid/view/View$OnTouchListener;", "touchImageViewListener", "Lcom/example/ardrawsketch/sketch/views/OnTouchImageViewListener;", "setRotateImageToFitScreen", "", "rotateImageToFitScreen", "setOnTouchListener", "onTouchListener", "setOnTouchImageViewListener", "onTouchImageViewListener", "setOnDoubleTapListener", "onDoubleTapListener", "setOnTouchCoordinatesListener", "onTouchCoordinatesListener", "setImageResource", "resId", "setImageBitmap", "bm", "Landroid/graphics/Bitmap;", "setImageDrawable", "drawable", "Landroid/graphics/drawable/Drawable;", "setImageURI", "uri", "Landroid/net/Uri;", "setScaleType", "type", "getScaleType", "isZoomed", "zoomedRect", "Landroid/graphics/RectF;", "getZoomedRect", "()Landroid/graphics/RectF;", "savePreviousImageValues", "onSaveInstanceState", "Landroid/os/Parcelable;", "onRestoreInstanceState", "state", "onDraw", "canvas", "Landroid/graphics/Canvas;", "onConfigurationChanged", "newConfig", "Landroid/content/res/Configuration;", "max", "maxZoom", "getMaxZoom", "setMaxZoom", "setMaxZoomRatio", "min", "minZoom", "getMinZoom", "setMinZoom", "resetZoom", "resetZoomAnimated", "setZoom", "scale", "focusX", "focusY", "scaleType", "imageSource", "scrollPosition", "Landroid/graphics/PointF;", "getScrollPosition", "()Landroid/graphics/PointF;", "orientationMismatch", "getDrawableWidth", "getDrawableHeight", "setScrollPosition", "fixTrans", "fixScaleTrans", "getFixTrans", "trans", "viewSize", "contentSize", TypedValues.CycleType.S_WAVE_OFFSET, "getFixDragTrans", "delta", "imageWidth", "getImageWidth", "imageHeight", "getImageHeight", "onMeasure", "widthMeasureSpec", "heightMeasureSpec", "onSizeChanged", "w", "h", "oldw", "oldh", "fitImageToView", "setViewSize", "mode", "size", "drawableWidth", "newTranslationAfterChange", "prevImageSize", "imageSize", "prevViewSize", "drawableSize", "sizeChangeFixedPixel", "setState", "canScrollHorizontally", "direction", "canScrollVertically", "scaleImage", "deltaScale", "", "stretchImageToSuper", "transformCoordTouchToBitmap", "x", "y", "clipToBitmap", "transformCoordBitmapToTouch", "bx", "by", "compatPostOnAnimation", "runnable", "Ljava/lang/Runnable;", "setZoomAnimated", "zoomTimeMs", ServiceSpecificExtraArgs.CastExtraArgs.LISTENER, "Lcom/example/ardrawsketch/sketch/views/OnZoomFinishedListener;", "GestureListener", "PrivateOnTouchListener", "ScaleListener", "DoubleTapZoom", "Fling", "CompatScroller", "AnimatedZoom", "Companion", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes4.dex */
public class TouchImageView extends AppCompatImageView {
    public static final float AUTOMATIC_MIN_ZOOM = -1.0f;
    public static final Companion Companion = new Companion(null);
    private static final int DEFAULT_ZOOM_TIME = 500;
    private static final float SUPER_MAX_MULTIPLIER = 1.25f;
    private static final float SUPER_MIN_MULTIPLIER = 0.75f;
    private float currentZoom;
    private ZoomVariables delayedZoomVariables;
    private GestureDetector.OnDoubleTapListener doubleTapListener;
    private float doubleTapScale;
    private Fling fling;
    private float[] floatMatrix;
    private GestureDetector gestureDetector;
    private ImageActionState imageActionState;
    private boolean imageRenderedAtLeastOnce;
    private boolean isRotateImageToFitScreen;
    private boolean isSuperZoomEnabled;
    private boolean isZoomEnabled;
    private float matchViewHeight;
    private float matchViewWidth;
    private float maxScale;
    private boolean maxScaleIsSetByMultiplier;
    private float maxScaleMultiplier;
    private float minScale;
    private boolean onDrawReady;
    private int orientation;
    private FixedPixel orientationChangeFixedPixel;
    private boolean orientationJustChanged;
    private float prevMatchViewHeight;
    private float prevMatchViewWidth;
    private Matrix prevMatrix;
    private int prevViewHeight;
    private int prevViewWidth;
    private ScaleGestureDetector scaleDetector;
    private float superMaxScale;
    private float superMinScale;
    private OnTouchCoordinatesListener touchCoordinatesListener;
    private OnTouchImageViewListener touchImageViewListener;
    private Matrix touchMatrix;
    private ScaleType touchScaleType;
    private float userSpecifiedMinScale;
    private OnTouchListener userTouchListener;
    private int viewHeight;
    private FixedPixel viewSizeChangeFixedPixel;
    private int viewWidth;

    /* compiled from: TouchImageView.kt */
//    @Metadata(k = 3, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* loaded from: classes4.dex */
    public /* synthetic */ class WhenMappings {
        public static final /* synthetic */ int[] $EnumSwitchMapping$0;

        static {
            int[] iArr = new int[ScaleType.values().length];
            try {
                iArr[ScaleType.CENTER.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                iArr[ScaleType.CENTER_CROP.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                iArr[ScaleType.CENTER_INSIDE.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
            try {
                iArr[ScaleType.FIT_CENTER.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
            try {
                iArr[ScaleType.FIT_START.ordinal()] = 5;
            } catch (NoSuchFieldError e5) {
            }
            try {
                iArr[ScaleType.FIT_END.ordinal()] = 6;
            } catch (NoSuchFieldError e6) {
            }
            try {
                iArr[ScaleType.FIT_XY.ordinal()] = 7;
            } catch (NoSuchFieldError e7) {
            }
            $EnumSwitchMapping$0 = iArr;
        }
    }

    /* JADX WARN: 'this' call moved to the top of the method (can break code semantics) */
    public TouchImageView(Context context) {
        this(context, null, 0, 6, null);
        Intrinsics.checkNotNullParameter(context, "context");
    }

    /* JADX WARN: 'this' call moved to the top of the method (can break code semantics) */
    public TouchImageView(Context context, AttributeSet attrs) {
        this(context, attrs, 0, 4, null);
        Intrinsics.checkNotNullParameter(context, "context");
    }

    public /* synthetic */ TouchImageView(Context context, AttributeSet attributeSet, int i, int i2, DefaultConstructorMarker defaultConstructorMarker) {
        this(context, (i2 & 2) != 0 ? null : attributeSet, (i2 & 4) != 0 ? 0 : i);
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public TouchImageView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        Intrinsics.checkNotNullParameter(context, "context");
        this.isSuperZoomEnabled = true;
        this.orientationChangeFixedPixel = FixedPixel.CENTER;
        this.viewSizeChangeFixedPixel = FixedPixel.CENTER;
        super.setClickable(true);
        this.orientation = getResources().getConfiguration().orientation;
        this.scaleDetector = new ScaleGestureDetector(context, new ScaleListener());
        this.gestureDetector = new GestureDetector(context, new GestureListener());
        this.touchMatrix = new Matrix();
        this.prevMatrix = new Matrix();
        this.floatMatrix = new float[9];
        this.currentZoom = 1.0f;
        if (this.touchScaleType == null) {
            this.touchScaleType = ScaleType.FIT_CENTER;
        }
        this.minScale = 1.0f;
        this.maxScale = 3.0f;
        this.superMinScale = this.minScale * 0.75f;
        this.superMaxScale = this.maxScale * SUPER_MAX_MULTIPLIER;
        setImageMatrix(this.touchMatrix);
        setScaleType(ScaleType.MATRIX);
        setState(ImageActionState.NONE);
        this.onDrawReady = false;
        super.setOnTouchListener(new PrivateOnTouchListener());
        TypedArray attributes = context.getTheme().obtainStyledAttributes(attrs, R.styleable.TouchImageView, defStyle, 0);
        Intrinsics.checkNotNullExpressionValue(attributes, "obtainStyledAttributes(...)");
        try {
            if (!isInEditMode()) {
                this.isZoomEnabled = attributes.getBoolean(R.styleable.TouchImageView_zoom_enabled, true);
            }
        } finally {
            attributes.recycle();
        }
    }

    public final float getCurrentZoom() {
        return this.currentZoom;
    }

    public final boolean isZoomEnabled() {
        return this.isZoomEnabled;
    }

    public final void setZoomEnabled(boolean z) {
        this.isZoomEnabled = z;
    }

    public final boolean isSuperZoomEnabled() {
        return this.isSuperZoomEnabled;
    }

    public final void setSuperZoomEnabled(boolean z) {
        this.isSuperZoomEnabled = z;
    }

    public final FixedPixel getOrientationChangeFixedPixel() {
        return this.orientationChangeFixedPixel;
    }

    public final void setOrientationChangeFixedPixel(FixedPixel fixedPixel) {
        this.orientationChangeFixedPixel = fixedPixel;
    }

    public final FixedPixel getViewSizeChangeFixedPixel() {
        return this.viewSizeChangeFixedPixel;
    }

    public final void setViewSizeChangeFixedPixel(FixedPixel fixedPixel) {
        this.viewSizeChangeFixedPixel = fixedPixel;
    }

    public final float getDoubleTapScale() {
        return this.doubleTapScale;
    }

    public final void setDoubleTapScale(float f) {
        this.doubleTapScale = f;
    }

    public final void setRotateImageToFitScreen(boolean rotateImageToFitScreen) {
        this.isRotateImageToFitScreen = rotateImageToFitScreen;
    }

    @Override // android.view.View
    public void setOnTouchListener(OnTouchListener onTouchListener) {
        this.userTouchListener = onTouchListener;
    }

    public final void setOnTouchImageViewListener(OnTouchImageViewListener onTouchImageViewListener) {
        Intrinsics.checkNotNullParameter(onTouchImageViewListener, "onTouchImageViewListener");
        this.touchImageViewListener = onTouchImageViewListener;
    }

    public final void setOnDoubleTapListener(GestureDetector.OnDoubleTapListener onDoubleTapListener) {
        Intrinsics.checkNotNullParameter(onDoubleTapListener, "onDoubleTapListener");
        this.doubleTapListener = onDoubleTapListener;
    }

    public final void setOnTouchCoordinatesListener(OnTouchCoordinatesListener onTouchCoordinatesListener) {
        Intrinsics.checkNotNullParameter(onTouchCoordinatesListener, "onTouchCoordinatesListener");
        this.touchCoordinatesListener = onTouchCoordinatesListener;
    }

    @Override // androidx.appcompat.widget.AppCompatImageView, android.widget.ImageView
    public void setImageResource(int resId) {
        this.imageRenderedAtLeastOnce = false;
        super.setImageResource(resId);
        savePreviousImageValues();
        fitImageToView();
    }

    @Override // androidx.appcompat.widget.AppCompatImageView, android.widget.ImageView
    public void setImageBitmap(Bitmap bm) {
        this.imageRenderedAtLeastOnce = false;
        super.setImageBitmap(bm);
        savePreviousImageValues();
        fitImageToView();
    }

    @Override // androidx.appcompat.widget.AppCompatImageView, android.widget.ImageView
    public void setImageDrawable(Drawable drawable) {
        this.imageRenderedAtLeastOnce = false;
        super.setImageDrawable(drawable);
        savePreviousImageValues();
        fitImageToView();
    }

    @Override // androidx.appcompat.widget.AppCompatImageView, android.widget.ImageView
    public void setImageURI(Uri uri) {
        this.imageRenderedAtLeastOnce = false;
        super.setImageURI(uri);
        savePreviousImageValues();
        fitImageToView();
    }

    @Override // android.widget.ImageView
    public void setScaleType(ScaleType type) {
        Intrinsics.checkNotNullParameter(type, "type");
        if (type == ScaleType.MATRIX) {
            super.setScaleType(ScaleType.MATRIX);
            return;
        }
        this.touchScaleType = type;
        if (this.onDrawReady) {
            setZoom(this);
        }
    }

    @Override // android.widget.ImageView
    public ScaleType getScaleType() {
        ScaleType scaleType = this.touchScaleType;
        Intrinsics.checkNotNull(scaleType);
        return scaleType;
    }

    public final boolean isZoomed() {
        return !(this.currentZoom == 1.0f);
    }

    public final RectF getZoomedRect() {
        if (this.touchScaleType == ScaleType.FIT_XY) {
            throw new UnsupportedOperationException("getZoomedRect() not supported with FIT_XY");
        }
        PointF topLeft = transformCoordTouchToBitmap(0.0f, 0.0f, true);
        PointF bottomRight = transformCoordTouchToBitmap(this.viewWidth, this.viewHeight, true);
        float w = getDrawableWidth(getDrawable());
        float h = getDrawableHeight(getDrawable());
        return new RectF(topLeft.x / w, topLeft.y / h, bottomRight.x / w, bottomRight.y / h);
    }

    public final void savePreviousImageValues() {
        if (this.viewHeight != 0 && this.viewWidth != 0) {
            this.touchMatrix.getValues(this.floatMatrix);
            this.prevMatrix.setValues(this.floatMatrix);
            this.prevMatchViewHeight = this.matchViewHeight;
            this.prevMatchViewWidth = this.matchViewWidth;
            this.prevViewHeight = this.viewHeight;
            this.prevViewWidth = this.viewWidth;
        }
    }

    @Override // android.view.View
    public Parcelable onSaveInstanceState() {
        super.onSaveInstanceState();
        Bundle bundle = new Bundle();
        bundle.putInt("orientation", this.orientation);
        bundle.putFloat("saveScale", this.currentZoom);
        bundle.putFloat("matchViewHeight", this.matchViewHeight);
        bundle.putFloat("matchViewWidth", this.matchViewWidth);
        bundle.putInt("viewWidth", this.viewWidth);
        bundle.putInt("viewHeight", this.viewHeight);
        this.touchMatrix.getValues(this.floatMatrix);
        bundle.putFloatArray("matrix", this.floatMatrix);
        bundle.putBoolean("imageRendered", this.imageRenderedAtLeastOnce);
        bundle.putSerializable("viewSizeChangeFixedPixel", this.viewSizeChangeFixedPixel);
        bundle.putSerializable("orientationChangeFixedPixel", this.orientationChangeFixedPixel);
        return bundle;
    }

    @Override // android.view.View
    public void onRestoreInstanceState(Parcelable state) {
        Intrinsics.checkNotNullParameter(state, "state");
        if (state instanceof Bundle) {
            this.currentZoom = ((Bundle) state).getFloat("saveScale");
            float[] floatArray = ((Bundle) state).getFloatArray("matrix");
            Intrinsics.checkNotNull(floatArray);
            this.floatMatrix = floatArray;
            this.prevMatrix.setValues(this.floatMatrix);
            this.prevMatchViewHeight = ((Bundle) state).getFloat("matchViewHeight");
            this.prevMatchViewWidth = ((Bundle) state).getFloat("matchViewWidth");
            this.prevViewHeight = ((Bundle) state).getInt("viewHeight");
            this.prevViewWidth = ((Bundle) state).getInt("viewWidth");
            this.imageRenderedAtLeastOnce = ((Bundle) state).getBoolean("imageRendered");
            if (Build.VERSION.SDK_INT >= 33) {
                this.viewSizeChangeFixedPixel = (FixedPixel) ((Bundle) state).getSerializable("viewSizeChangeFixedPixel", FixedPixel.class);
                this.orientationChangeFixedPixel = (FixedPixel) ((Bundle) state).getSerializable("orientationChangeFixedPixel", FixedPixel.class);
            } else {
                this.viewSizeChangeFixedPixel = (FixedPixel) ((Bundle) state).getSerializable("viewSizeChangeFixedPixel");
                this.orientationChangeFixedPixel = (FixedPixel) ((Bundle) state).getSerializable("orientationChangeFixedPixel");
            }
            int oldOrientation = ((Bundle) state).getInt("orientation");
            if (this.orientation != oldOrientation) {
                this.orientationJustChanged = true;
                return;
            }
            return;
        }
        super.onRestoreInstanceState(state);
    }

    @Override // android.widget.ImageView, android.view.View
    protected void onDraw(Canvas canvas) {
        Intrinsics.checkNotNullParameter(canvas, "canvas");
        this.onDrawReady = true;
        this.imageRenderedAtLeastOnce = true;
        if (this.delayedZoomVariables != null) {
            ZoomVariables zoomVariables = this.delayedZoomVariables;
            Intrinsics.checkNotNull(zoomVariables);
            float scale = zoomVariables.getScale();
            ZoomVariables zoomVariables2 = this.delayedZoomVariables;
            Intrinsics.checkNotNull(zoomVariables2);
            float focusX = zoomVariables2.getFocusX();
            ZoomVariables zoomVariables3 = this.delayedZoomVariables;
            Intrinsics.checkNotNull(zoomVariables3);
            float focusY = zoomVariables3.getFocusY();
            ZoomVariables zoomVariables4 = this.delayedZoomVariables;
            Intrinsics.checkNotNull(zoomVariables4);
            setZoom(scale, focusX, focusY, zoomVariables4.getScaleType());
            this.delayedZoomVariables = null;
        }
        super.onDraw(canvas);
    }

    @Override // android.view.View
    public void onConfigurationChanged(Configuration newConfig) {
        Intrinsics.checkNotNullParameter(newConfig, "newConfig");
        super.onConfigurationChanged(newConfig);
        int newOrientation = getResources().getConfiguration().orientation;
        if (newOrientation != this.orientation) {
            this.orientationJustChanged = true;
            this.orientation = newOrientation;
        }
        savePreviousImageValues();
    }

    public final float getMaxZoom() {
        return this.maxScale;
    }

    public final void setMaxZoom(float max) {
        this.maxScale = max;
        this.superMaxScale = this.maxScale * SUPER_MAX_MULTIPLIER;
        this.maxScaleIsSetByMultiplier = false;
    }

    public final void setMaxZoomRatio(float max) {
        this.maxScaleMultiplier = max;
        this.maxScale = this.minScale * this.maxScaleMultiplier;
        this.superMaxScale = this.maxScale * SUPER_MAX_MULTIPLIER;
        this.maxScaleIsSetByMultiplier = true;
    }

    public final float getMinZoom() {
        return this.minScale;
    }

    public final void setMinZoom(float min) {
        float min2;
        this.userSpecifiedMinScale = min;
        if (min == -1.0f) {
            if (this.touchScaleType == ScaleType.CENTER || this.touchScaleType == ScaleType.CENTER_CROP) {
                Drawable drawable = getDrawable();
                int drawableWidth = getDrawableWidth(drawable);
                int drawableHeight = getDrawableHeight(drawable);
                if (drawable != null && drawableWidth > 0 && drawableHeight > 0) {
                    float widthRatio = this.viewWidth / drawableWidth;
                    float heightRatio = this.viewHeight / drawableHeight;
                    if (this.touchScaleType == ScaleType.CENTER) {
                        min2 = Math.min(widthRatio, heightRatio);
                    } else {
                        min2 = Math.min(widthRatio, heightRatio) / Math.max(widthRatio, heightRatio);
                    }
                    this.minScale = min2;
                }
            } else {
                this.minScale = 1.0f;
            }
        } else {
            this.minScale = this.userSpecifiedMinScale;
        }
        if (this.maxScaleIsSetByMultiplier) {
            setMaxZoomRatio(this.maxScaleMultiplier);
        }
        this.superMinScale = this.minScale * 0.75f;
    }

    public final void resetZoom() {
        this.currentZoom = 1.0f;
        fitImageToView();
    }

    public final void resetZoomAnimated() {
        setZoomAnimated(1.0f, 0.5f, 0.5f);
    }

    public final void setZoom(float scale) {
        setZoom(scale, 0.5f, 0.5f);
    }

    public final void setZoom(float scale, float focusX, float focusY) {
        setZoom(scale, focusX, focusY, this.touchScaleType);
    }

    public final void setZoom(float scale, float focusX, float focusY, ScaleType scaleType) {
        if (!this.onDrawReady) {
            this.delayedZoomVariables = new ZoomVariables(scale, focusX, focusY, scaleType);
            return;
        }
        if (this.userSpecifiedMinScale == -1.0f) {
            setMinZoom(-1.0f);
            if (this.currentZoom < this.minScale) {
                this.currentZoom = this.minScale;
            }
        }
        if (scaleType != this.touchScaleType) {
            Intrinsics.checkNotNull(scaleType);
            setScaleType(scaleType);
        }
        resetZoom();
        scaleImage(scale, this.viewWidth / 2.0f, this.viewHeight / 2.0f, this.isSuperZoomEnabled);
        this.touchMatrix.getValues(this.floatMatrix);
        this.floatMatrix[2] = -((getImageWidth() * focusX) - (this.viewWidth * 0.5f));
        this.floatMatrix[5] = -((getImageHeight() * focusY) - (this.viewHeight * 0.5f));
        this.touchMatrix.setValues(this.floatMatrix);
        fixTrans();
        savePreviousImageValues();
        setImageMatrix(this.touchMatrix);
    }

    public final void setZoom(TouchImageView imageSource) {
        Intrinsics.checkNotNullParameter(imageSource, "imageSource");
        PointF center = imageSource.getScrollPosition();
        setZoom(imageSource.currentZoom, center.x, center.y, imageSource.getScaleType());
    }

    public final PointF getScrollPosition() {
        Drawable drawable = getDrawable();
        if (drawable == null) {
            return new PointF(0.5f, 0.5f);
        }
        int drawableWidth = getDrawableWidth(drawable);
        int drawableHeight = getDrawableHeight(drawable);
        PointF point = transformCoordTouchToBitmap(this.viewWidth / 2.0f, this.viewHeight / 2.0f, true);
        point.x /= drawableWidth;
        point.y /= drawableHeight;
        return point;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final boolean orientationMismatch(Drawable drawable) {
        boolean z = this.viewWidth > this.viewHeight;
        Intrinsics.checkNotNull(drawable);
        return z != (drawable.getIntrinsicWidth() > drawable.getIntrinsicHeight());
    }

    private final int getDrawableWidth(Drawable drawable) {
        if (orientationMismatch(drawable) && this.isRotateImageToFitScreen) {
            Intrinsics.checkNotNull(drawable);
            return drawable.getIntrinsicHeight();
        }
        Intrinsics.checkNotNull(drawable);
        return drawable.getIntrinsicWidth();
    }

    private final int getDrawableHeight(Drawable drawable) {
        if (orientationMismatch(drawable) && this.isRotateImageToFitScreen) {
            Intrinsics.checkNotNull(drawable);
            return drawable.getIntrinsicWidth();
        }
        Intrinsics.checkNotNull(drawable);
        return drawable.getIntrinsicHeight();
    }

    public final void setScrollPosition(float focusX, float focusY) {
        setZoom(this.currentZoom, focusX, focusY);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final void fixTrans() {
        this.touchMatrix.getValues(this.floatMatrix);
        float transX = this.floatMatrix[2];
        float transY = this.floatMatrix[5];
        float offset = 0.0f;
        if (this.isRotateImageToFitScreen && orientationMismatch(getDrawable())) {
            offset = getImageWidth();
        }
        float fixTransX = getFixTrans(transX, this.viewWidth, getImageWidth(), offset);
        float fixTransY = getFixTrans(transY, this.viewHeight, getImageHeight(), 0.0f);
        this.touchMatrix.postTranslate(fixTransX, fixTransY);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final void fixScaleTrans() {
        fixTrans();
        this.touchMatrix.getValues(this.floatMatrix);
        if (getImageWidth() < this.viewWidth) {
            float xOffset = (this.viewWidth - getImageWidth()) / 2;
            if (this.isRotateImageToFitScreen && orientationMismatch(getDrawable())) {
                xOffset += getImageWidth();
            }
            this.floatMatrix[2] = xOffset;
        }
        if (getImageHeight() < this.viewHeight) {
            this.floatMatrix[5] = (this.viewHeight - getImageHeight()) / 2;
        }
        this.touchMatrix.setValues(this.floatMatrix);
    }

    private final float getFixTrans(float trans, float viewSize, float contentSize, float offset) {
        float minTrans;
        float maxTrans;
        if (contentSize <= viewSize) {
            minTrans = offset;
            maxTrans = (offset + viewSize) - contentSize;
        } else {
            float maxTrans2 = offset + viewSize;
            minTrans = maxTrans2 - contentSize;
            maxTrans = offset;
        }
        if (trans < minTrans) {
            return (-trans) + minTrans;
        }
        if (trans > maxTrans) {
            return (-trans) + maxTrans;
        }
        return 0.0f;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final float getFixDragTrans(float delta, float viewSize, float contentSize) {
        if (contentSize <= viewSize) {
            return 0.0f;
        }
        return delta;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final float getImageWidth() {
        return this.matchViewWidth * this.currentZoom;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final float getImageHeight() {
        return this.matchViewHeight * this.currentZoom;
    }

    @Override // android.widget.ImageView, android.view.View
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        Drawable drawable = getDrawable();
        if (drawable == null || drawable.getIntrinsicWidth() == 0 || drawable.getIntrinsicHeight() == 0) {
            setMeasuredDimension(0, 0);
            return;
        }
        int drawableWidth = getDrawableWidth(drawable);
        int drawableHeight = getDrawableHeight(drawable);
        int widthSize = MeasureSpec.getSize(widthMeasureSpec);
        int widthMode = MeasureSpec.getMode(widthMeasureSpec);
        int heightSize = MeasureSpec.getSize(heightMeasureSpec);
        int heightMode = MeasureSpec.getMode(heightMeasureSpec);
        int totalViewWidth = setViewSize(widthMode, widthSize, drawableWidth);
        int totalViewHeight = setViewSize(heightMode, heightSize, drawableHeight);
        if (!this.orientationJustChanged) {
            savePreviousImageValues();
        }
        int width = (totalViewWidth - getPaddingLeft()) - getPaddingRight();
        int height = (totalViewHeight - getPaddingTop()) - getPaddingBottom();
        setMeasuredDimension(width, height);
    }

    @Override // android.view.View
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        this.viewWidth = w;
        this.viewHeight = h;
        fitImageToView();
    }

    /* JADX WARN: Code restructure failed: missing block: B:67:0x0130, code lost:
        if ((r22.prevMatchViewHeight == 0.0f) != false) goto L57;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private final void fitImageToView() {
        /*
            Method dump skipped, instructions count: 450
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.example.ardrawingsketch.views.TouchImageView.fitImageToView():void");
    }

    private final int setViewSize(int mode, int size, int drawableWidth) {
        switch (mode) {
            case Integer.MIN_VALUE:
                return Math.min(drawableWidth, size);
            case 0:
                return drawableWidth;
            case BasicMeasure.EXACTLY /* 1073741824 */:
            default:
                return size;
        }
    }

    private final float newTranslationAfterChange(float trans, float prevImageSize, float imageSize, int prevViewSize, int viewSize, int drawableSize, FixedPixel sizeChangeFixedPixel) {
        if (imageSize < viewSize) {
            float fixedPixelPositionInView = (viewSize - (drawableSize * this.floatMatrix[0])) * 0.5f;
            return fixedPixelPositionInView;
        } else if (trans > 0.0f) {
            float fixedPixelPositionInView2 = -((imageSize - viewSize) * 0.5f);
            return fixedPixelPositionInView2;
        } else {
            float fixedPixelPositionInView3 = 0.5f;
            if (sizeChangeFixedPixel == FixedPixel.BOTTOM_RIGHT) {
                fixedPixelPositionInView3 = 1.0f;
            } else if (sizeChangeFixedPixel == FixedPixel.TOP_LEFT) {
                fixedPixelPositionInView3 = 0.0f;
            }
            float fixedPixelPositionInImage = ((-trans) + (prevViewSize * fixedPixelPositionInView3)) / prevImageSize;
            return -((fixedPixelPositionInImage * imageSize) - (viewSize * fixedPixelPositionInView3));
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final void setState(ImageActionState imageActionState) {
        this.imageActionState = imageActionState;
    }

    @Override // android.view.View
    public boolean canScrollHorizontally(int direction) {
        this.touchMatrix.getValues(this.floatMatrix);
        float x = this.floatMatrix[2];
        if (getImageWidth() < this.viewWidth) {
            return false;
        }
        if (x < -1.0f || direction >= 0) {
            return (Math.abs(x) + ((float) this.viewWidth)) + ((float) 1) < getImageWidth() || direction <= 0;
        }
        return false;
    }

    @Override // android.view.View
    public boolean canScrollVertically(int direction) {
        this.touchMatrix.getValues(this.floatMatrix);
        float y = this.floatMatrix[5];
        if (getImageHeight() < this.viewHeight) {
            return false;
        }
        if (y < -1.0f || direction >= 0) {
            return (Math.abs(y) + ((float) this.viewHeight)) + ((float) 1) < getImageHeight() || direction <= 0;
        }
        return false;
    }

    /* compiled from: TouchImageView.kt */
    @Metadata(d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u0007\n\u0002\b\u0004\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0007¢\u0006\u0004\b\u0002\u0010\u0003J\u0010\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u0016J\u0010\u0010\b\u001a\u00020\t2\u0006\u0010\u0006\u001a\u00020\u0007H\u0016J*\u0010\n\u001a\u00020\u00052\b\u0010\u000b\u001a\u0004\u0018\u00010\u00072\u0006\u0010\f\u001a\u00020\u00072\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000eH\u0016J\u0010\u0010\u0010\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u0016J\u0010\u0010\u0011\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u0016¨\u0006\u0012"}, d2 = {"Lcom/example/ardrawsketch/sketch/views/TouchImageView$GestureListener;", "Landroid/view/GestureDetector$SimpleOnGestureListener;", "<init>", "(Lcom/example/ardrawsketch/sketch/views/TouchImageView;)V", "onSingleTapConfirmed", "", "e", "Landroid/view/MotionEvent;", "onLongPress", "", "onFling", "e1", "e2", "velocityX", "", "velocityY", "onDoubleTap", "onDoubleTapEvent", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* loaded from: classes4.dex */
    private final class GestureListener extends GestureDetector.SimpleOnGestureListener {
        public GestureListener() {
        }

        @Override // android.view.GestureDetector.SimpleOnGestureListener, android.view.GestureDetector.OnDoubleTapListener
        public boolean onSingleTapConfirmed(MotionEvent e) {
            Intrinsics.checkNotNullParameter(e, "e");
            GestureDetector.OnDoubleTapListener onDoubleTapListener = TouchImageView.this.doubleTapListener;
            return onDoubleTapListener != null ? onDoubleTapListener.onSingleTapConfirmed(e) : TouchImageView.this.performClick();
        }

        @Override // android.view.GestureDetector.SimpleOnGestureListener, android.view.GestureDetector.OnGestureListener
        public void onLongPress(MotionEvent e) {
            Intrinsics.checkNotNullParameter(e, "e");
            TouchImageView.this.performLongClick();
        }

        @Override // android.view.GestureDetector.SimpleOnGestureListener, android.view.GestureDetector.OnGestureListener
        public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
            Intrinsics.checkNotNullParameter(e2, "e2");
            Fling fling = TouchImageView.this.fling;
            if (fling != null) {
                fling.cancelFling();
            }
            TouchImageView touchImageView = TouchImageView.this;
            Fling fling2 = new Fling((int) velocityX, (int) velocityY);
            TouchImageView.this.compatPostOnAnimation(fling2);
            touchImageView.fling = fling2;
            return super.onFling(e1, e2, velocityX, velocityY);
        }

        @Override // android.view.GestureDetector.SimpleOnGestureListener, android.view.GestureDetector.OnDoubleTapListener
        public boolean onDoubleTap(MotionEvent e) {
            Intrinsics.checkNotNullParameter(e, "e");
            boolean consumed = false;
            if (TouchImageView.this.isZoomEnabled()) {
                GestureDetector.OnDoubleTapListener onDoubleTapListener = TouchImageView.this.doubleTapListener;
                if (onDoubleTapListener != null) {
                    consumed = onDoubleTapListener.onDoubleTap(e);
                }
                if (TouchImageView.this.imageActionState == ImageActionState.NONE) {
                    float maxZoomScale = (TouchImageView.this.getDoubleTapScale() > 0.0f ? 1 : (TouchImageView.this.getDoubleTapScale() == 0.0f ? 0 : -1)) == 0 ? TouchImageView.this.maxScale : TouchImageView.this.getDoubleTapScale();
                    float targetZoom = TouchImageView.this.getCurrentZoom() == TouchImageView.this.minScale ? maxZoomScale : TouchImageView.this.minScale;
                    DoubleTapZoom doubleTap = new DoubleTapZoom(targetZoom, e.getX(), e.getY(), false);
                    TouchImageView.this.compatPostOnAnimation(doubleTap);
                    return true;
                }
                return consumed;
            }
            return false;
        }

        @Override // android.view.GestureDetector.SimpleOnGestureListener, android.view.GestureDetector.OnDoubleTapListener
        public boolean onDoubleTapEvent(MotionEvent e) {
            Intrinsics.checkNotNullParameter(e, "e");
            GestureDetector.OnDoubleTapListener onDoubleTapListener = TouchImageView.this.doubleTapListener;
            if (onDoubleTapListener != null) {
                return onDoubleTapListener.onDoubleTapEvent(e);
            }
            return false;
        }
    }

    /* compiled from: TouchImageView.kt */
//    @Metadata(d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0007¢\u0006\u0004\b\u0002\u0010\u0003J\u0018\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\u0016R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004¢\u0006\u0002\n\u0000¨\u0006\f"}, d2 = {"Lcom/example/ardrawsketch/sketch/views/TouchImageView$PrivateOnTouchListener;", "Landroid/view/View$OnTouchListener;", "<init>", "(Lcom/example/ardrawsketch/sketch/views/TouchImageView;)V", "last", "Landroid/graphics/PointF;", "onTouch", "", "v", "Landroid/view/View;", NotificationCompat.CATEGORY_EVENT, "Landroid/view/MotionEvent;", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* loaded from: classes4.dex */
    private final class PrivateOnTouchListener implements OnTouchListener {
        private final PointF last = new PointF();

        public PrivateOnTouchListener() {
        }

        @Override // android.view.View.OnTouchListener
        public boolean onTouch(View v, MotionEvent event) {
            Intrinsics.checkNotNullParameter(v, "v");
            Intrinsics.checkNotNullParameter(event, "event");
            if (TouchImageView.this.getDrawable() == null) {
                TouchImageView.this.setState(ImageActionState.NONE);
                return false;
            }
            if (TouchImageView.this.isZoomEnabled()) {
                TouchImageView.this.scaleDetector.onTouchEvent(event);
            }
            TouchImageView.this.gestureDetector.onTouchEvent(event);
            PointF curr = new PointF(event.getX(), event.getY());
            if (TouchImageView.this.imageActionState == ImageActionState.NONE || TouchImageView.this.imageActionState == ImageActionState.DRAG || TouchImageView.this.imageActionState == ImageActionState.FLING) {
                switch (event.getAction()) {
                    case 0:
                        this.last.set(curr);
                        Fling fling = TouchImageView.this.fling;
                        if (fling != null) {
                            fling.cancelFling();
                        }
                        TouchImageView.this.setState(ImageActionState.DRAG);
                        break;
                    case 1:
                    case 6:
                        TouchImageView.this.setState(ImageActionState.NONE);
                        break;
                    case 2:
                        if (TouchImageView.this.imageActionState == ImageActionState.DRAG) {
                            float deltaX = curr.x - this.last.x;
                            float deltaY = curr.y - this.last.y;
                            float fixTransX = TouchImageView.this.getFixDragTrans(deltaX, TouchImageView.this.viewWidth, TouchImageView.this.getImageWidth());
                            float fixTransY = TouchImageView.this.getFixDragTrans(deltaY, TouchImageView.this.viewHeight, TouchImageView.this.getImageHeight());
                            TouchImageView.this.touchMatrix.postTranslate(fixTransX, fixTransY);
                            TouchImageView.this.fixTrans();
                            this.last.set(curr.x, curr.y);
                            break;
                        }
                        break;
                }
            }
            OnTouchCoordinatesListener onTouchCoordinatesListener = TouchImageView.this.touchCoordinatesListener;
            if (onTouchCoordinatesListener != null) {
                onTouchCoordinatesListener.onTouchCoordinate(v, event, TouchImageView.this.transformCoordTouchToBitmap(event.getX(), event.getY(), true));
            }
            TouchImageView.this.setImageMatrix(TouchImageView.this.touchMatrix);
            OnTouchListener onTouchListener = TouchImageView.this.userTouchListener;
            if (onTouchListener != null) {
                onTouchListener.onTouch(v, event);
            }
            OnTouchImageViewListener onTouchImageViewListener = TouchImageView.this.touchImageViewListener;
            if (onTouchImageViewListener != null) {
                onTouchImageViewListener.onMove();
            }
            return true;
        }
    }

    /* compiled from: TouchImageView.kt */
    @Metadata(d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0007¢\u0006\u0004\b\u0002\u0010\u0003J\u0010\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u0016J\u0010\u0010\b\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u0016J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u0006\u001a\u00020\u0007H\u0016¨\u0006\u000b"}, d2 = {"Lcom/example/ardrawsketch/sketch/views/TouchImageView$ScaleListener;", "Landroid/view/ScaleGestureDetector$SimpleOnScaleGestureListener;", "<init>", "(Lcom/example/ardrawsketch/sketch/views/TouchImageView;)V", "onScaleBegin", "", "detector", "Landroid/view/ScaleGestureDetector;", "onScale", "onScaleEnd", "", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* loaded from: classes4.dex */
    private final class ScaleListener extends ScaleGestureDetector.SimpleOnScaleGestureListener {
        public ScaleListener() {
        }

        @Override // android.view.ScaleGestureDetector.SimpleOnScaleGestureListener, android.view.ScaleGestureDetector.OnScaleGestureListener
        public boolean onScaleBegin(ScaleGestureDetector detector) {
            Intrinsics.checkNotNullParameter(detector, "detector");
            TouchImageView.this.setState(ImageActionState.ZOOM);
            return true;
        }

        @Override // android.view.ScaleGestureDetector.SimpleOnScaleGestureListener, android.view.ScaleGestureDetector.OnScaleGestureListener
        public boolean onScale(ScaleGestureDetector detector) {
            Intrinsics.checkNotNullParameter(detector, "detector");
            TouchImageView.this.scaleImage(detector.getScaleFactor(), detector.getFocusX(), detector.getFocusY(), TouchImageView.this.isSuperZoomEnabled());
            OnTouchImageViewListener onTouchImageViewListener = TouchImageView.this.touchImageViewListener;
            if (onTouchImageViewListener != null) {
                onTouchImageViewListener.onMove();
                return true;
            }
            return true;
        }

        @Override // android.view.ScaleGestureDetector.SimpleOnScaleGestureListener, android.view.ScaleGestureDetector.OnScaleGestureListener
        public void onScaleEnd(ScaleGestureDetector detector) {
            float targetZoom;
            Intrinsics.checkNotNullParameter(detector, "detector");
            super.onScaleEnd(detector);
            TouchImageView.this.setState(ImageActionState.NONE);
            boolean animateToZoomBoundary = false;
            float targetZoom2 = TouchImageView.this.getCurrentZoom();
            if (TouchImageView.this.getCurrentZoom() > TouchImageView.this.maxScale) {
                float targetZoom3 = TouchImageView.this.maxScale;
                animateToZoomBoundary = true;
                targetZoom = targetZoom3;
            } else if (TouchImageView.this.getCurrentZoom() < TouchImageView.this.minScale) {
                float targetZoom4 = TouchImageView.this.minScale;
                animateToZoomBoundary = true;
                targetZoom = targetZoom4;
            } else {
                targetZoom = targetZoom2;
            }
            if (animateToZoomBoundary) {
                DoubleTapZoom doubleTap = new DoubleTapZoom(targetZoom, TouchImageView.this.viewWidth / 2, TouchImageView.this.viewHeight / 2, TouchImageView.this.isSuperZoomEnabled());
                TouchImageView.this.compatPostOnAnimation(doubleTap);
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final void scaleImage(double deltaScale, float focusX, float focusY, boolean stretchImageToSuper) {
        float lowerScale;
        float upperScale;
        double deltaScaleLocal = deltaScale;
        if (stretchImageToSuper) {
            lowerScale = this.superMinScale;
            upperScale = this.superMaxScale;
        } else {
            lowerScale = this.minScale;
            upperScale = this.maxScale;
        }
        float origScale = this.currentZoom;
        this.currentZoom *= (float) deltaScaleLocal;
        if (this.currentZoom > upperScale) {
            this.currentZoom = upperScale;
            deltaScaleLocal = upperScale / origScale;
        } else if (this.currentZoom < lowerScale) {
            this.currentZoom = lowerScale;
            deltaScaleLocal = lowerScale / origScale;
        }
        this.touchMatrix.postScale((float) deltaScaleLocal, (float) deltaScaleLocal, focusX, focusY);
        fixScaleTrans();
    }

    /* compiled from: TouchImageView.kt */
    @Metadata(d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u0006\n\u0000\b\u0082\u0004\u0018\u00002\u00020\u0001B'\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0007¢\u0006\u0004\b\b\u0010\tJ\b\u0010\u0014\u001a\u00020\u0015H\u0016J\u0010\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0017\u001a\u00020\u0003H\u0002J\b\u0010\u0018\u001a\u00020\u0003H\u0002J\u0010\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u0017\u001a\u00020\u0003H\u0002R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0003X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0003X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0003X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0012X\u0082\u0004¢\u0006\u0002\n\u0000¨\u0006\u001b"}, d2 = {"Lcom/example/ardrawsketch/sketch/views/TouchImageView$DoubleTapZoom;", "Ljava/lang/Runnable;", "targetZoom", "", "focusX", "focusY", "stretchImageToSuper", "", "<init>", "(Lcom/example/ardrawsketch/sketch/views/TouchImageView;FFFZ)V", "startTime", "", "startZoom", "bitmapX", "bitmapY", "interpolator", "Landroid/view/animation/AccelerateDecelerateInterpolator;", "startTouch", "Landroid/graphics/PointF;", "endTouch", "run", "", "translateImageToCenterTouchPosition", "t", "interpolate", "calculateDeltaScale", "", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* loaded from: classes4.dex */
    private final class DoubleTapZoom implements Runnable {
        private final float bitmapX;
        private final float bitmapY;
        private final PointF endTouch;
        private final AccelerateDecelerateInterpolator interpolator = new AccelerateDecelerateInterpolator();
        private final long startTime;
        private final PointF startTouch;
        private final float startZoom;
        private final boolean stretchImageToSuper;
        private final float targetZoom;

        public DoubleTapZoom(float targetZoom, float focusX, float focusY, boolean stretchImageToSuper) {
            TouchImageView.this.setState(ImageActionState.ANIMATE_ZOOM);
            this.startTime = System.currentTimeMillis();
            this.startZoom = TouchImageView.this.getCurrentZoom();
            this.targetZoom = targetZoom;
            this.stretchImageToSuper = stretchImageToSuper;
            PointF bitmapPoint = TouchImageView.this.transformCoordTouchToBitmap(focusX, focusY, false);
            this.bitmapX = bitmapPoint.x;
            this.bitmapY = bitmapPoint.y;
            this.startTouch = TouchImageView.this.transformCoordBitmapToTouch(this.bitmapX, this.bitmapY);
            this.endTouch = new PointF(TouchImageView.this.viewWidth / 2, TouchImageView.this.viewHeight / 2);
        }

        @Override // java.lang.Runnable
        public void run() {
            if (TouchImageView.this.getDrawable() == null) {
                TouchImageView.this.setState(ImageActionState.NONE);
                return;
            }
            float t = interpolate();
            double deltaScale = calculateDeltaScale(t);
            TouchImageView.this.scaleImage(deltaScale, this.bitmapX, this.bitmapY, this.stretchImageToSuper);
            translateImageToCenterTouchPosition(t);
            TouchImageView.this.fixScaleTrans();
            TouchImageView.this.setImageMatrix(TouchImageView.this.touchMatrix);
            OnTouchImageViewListener onTouchImageViewListener = TouchImageView.this.touchImageViewListener;
            if (onTouchImageViewListener != null) {
                onTouchImageViewListener.onMove();
            }
            if (t < 1.0f) {
                TouchImageView.this.compatPostOnAnimation(this);
            } else {
                TouchImageView.this.setState(ImageActionState.NONE);
            }
        }

        private final void translateImageToCenterTouchPosition(float t) {
            float targetX = this.startTouch.x + ((this.endTouch.x - this.startTouch.x) * t);
            float targetY = this.startTouch.y + ((this.endTouch.y - this.startTouch.y) * t);
            PointF curr = TouchImageView.this.transformCoordBitmapToTouch(this.bitmapX, this.bitmapY);
            TouchImageView.this.touchMatrix.postTranslate(targetX - curr.x, targetY - curr.y);
        }

        private final float interpolate() {
            long currTime = System.currentTimeMillis();
            float elapsed = ((float) (currTime - this.startTime)) / 500.0f;
            return this.interpolator.getInterpolation(Math.min(1.0f, elapsed));
        }

        private final double calculateDeltaScale(float t) {
            double zoom = this.startZoom + (t * (this.targetZoom - this.startZoom));
            return zoom / TouchImageView.this.getCurrentZoom();
        }
    }

    protected final PointF transformCoordTouchToBitmap(float x, float y, boolean clipToBitmap) {
        this.touchMatrix.getValues(this.floatMatrix);
        float origW = getDrawable().getIntrinsicWidth();
        float origH = getDrawable().getIntrinsicHeight();
        float transX = this.floatMatrix[2];
        float transY = this.floatMatrix[5];
        float finalX = ((x - transX) * origW) / getImageWidth();
        float finalY = ((y - transY) * origH) / getImageHeight();
        if (clipToBitmap) {
            finalX = Math.min(Math.max(finalX, 0.0f), origW);
            finalY = Math.min(Math.max(finalY, 0.0f), origH);
        }
        return new PointF(finalX, finalY);
    }

    protected final PointF transformCoordBitmapToTouch(float bx, float by) {
        this.touchMatrix.getValues(this.floatMatrix);
        float origW = getDrawable().getIntrinsicWidth();
        float origH = getDrawable().getIntrinsicHeight();
        float px = bx / origW;
        float py = by / origH;
        float finalX = this.floatMatrix[2] + (getImageWidth() * px);
        float finalY = this.floatMatrix[5] + (getImageHeight() * py);
        return new PointF(finalX, finalY);
    }

    /* compiled from: TouchImageView.kt */
    @Metadata(d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u0002\n\u0002\b\u0002\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003¢\u0006\u0004\b\u0005\u0010\u0006J\u0006\u0010\u0016\u001a\u00020\u0017J\b\u0010\u0018\u001a\u00020\u0017H\u0016R\u001e\u0010\u0007\u001a\u00060\bR\u00020\tX\u0086\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010\u000b\"\u0004\b\f\u0010\rR\u001a\u0010\u000e\u001a\u00020\u0003X\u0086\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\u000f\u0010\u0010\"\u0004\b\u0011\u0010\u0012R\u001a\u0010\u0013\u001a\u00020\u0003X\u0086\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\u0014\u0010\u0010\"\u0004\b\u0015\u0010\u0012¨\u0006\u0019"}, d2 = {"Lcom/example/ardrawsketch/sketch/views/TouchImageView$Fling;", "Ljava/lang/Runnable;", "velocityX", "", "velocityY", "<init>", "(Lcom/example/ardrawsketch/sketch/views/TouchImageView;II)V", "scroller", "Lcom/example/ardrawsketch/sketch/views/TouchImageView$CompatScroller;", "Lcom/example/ardrawsketch/sketch/views/TouchImageView;", "getScroller", "()Lcom/example/ardrawsketch/sketch/views/TouchImageView$CompatScroller;", "setScroller", "(Lcom/example/ardrawsketch/sketch/views/TouchImageView$CompatScroller;)V", "currX", "getCurrX", "()I", "setCurrX", "(I)V", "currY", "getCurrY", "setCurrY", "cancelFling", "", "run", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* loaded from: classes4.dex */
    private final class Fling implements Runnable {
        private int currX;
        private int currY;
        private CompatScroller scroller;

        public Fling(int velocityX, int velocityY) {
            int maxX;
            int minX;
            int minY;
            int maxY;
            TouchImageView.this.setState(ImageActionState.FLING);
            this.scroller = new CompatScroller(TouchImageView.this.getContext());
            TouchImageView.this.touchMatrix.getValues(TouchImageView.this.floatMatrix);
            int startX = (int) TouchImageView.this.floatMatrix[2];
            int startY = (int) TouchImageView.this.floatMatrix[5];
            if (TouchImageView.this.isRotateImageToFitScreen && TouchImageView.this.orientationMismatch(TouchImageView.this.getDrawable())) {
                startX -= (int) TouchImageView.this.getImageWidth();
            }
            if (TouchImageView.this.getImageWidth() > TouchImageView.this.viewWidth) {
                int minX2 = TouchImageView.this.viewWidth - ((int) TouchImageView.this.getImageWidth());
                maxX = 0;
                minX = minX2;
            } else {
                int maxX2 = startX;
                maxX = maxX2;
                minX = maxX2;
            }
            if (TouchImageView.this.getImageHeight() > TouchImageView.this.viewHeight) {
                int minY2 = TouchImageView.this.viewHeight - ((int) TouchImageView.this.getImageHeight());
                minY = minY2;
                maxY = 0;
            } else {
                minY = startY;
                maxY = startY;
            }
            int startX2 = startX;
            this.scroller.fling(startX2, startY, velocityX, velocityY, minX, maxX, minY, maxY);
            this.currX = startX2;
            this.currY = startY;
        }

        public final CompatScroller getScroller() {
            return this.scroller;
        }

        public final void setScroller(CompatScroller compatScroller) {
            Intrinsics.checkNotNullParameter(compatScroller, "<set-?>");
            this.scroller = compatScroller;
        }

        public final int getCurrX() {
            return this.currX;
        }

        public final void setCurrX(int i) {
            this.currX = i;
        }

        public final int getCurrY() {
            return this.currY;
        }

        public final void setCurrY(int i) {
            this.currY = i;
        }

        public final void cancelFling() {
            TouchImageView.this.setState(ImageActionState.NONE);
            this.scroller.forceFinished(true);
        }

        @Override // java.lang.Runnable
        public void run() {
            OnTouchImageViewListener onTouchImageViewListener = TouchImageView.this.touchImageViewListener;
            if (onTouchImageViewListener != null) {
                onTouchImageViewListener.onMove();
            }
            if (!this.scroller.isFinished() && this.scroller.computeScrollOffset()) {
                int newX = this.scroller.getCurrX();
                int newY = this.scroller.getCurrY();
                int transX = newX - this.currX;
                int transY = newY - this.currY;
                this.currX = newX;
                this.currY = newY;
                TouchImageView.this.touchMatrix.postTranslate(transX, transY);
                TouchImageView.this.fixTrans();
                TouchImageView.this.setImageMatrix(TouchImageView.this.touchMatrix);
                TouchImageView.this.compatPostOnAnimation(this);
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* compiled from: TouchImageView.kt */
    @Metadata(d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\t\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0011\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003¢\u0006\u0004\b\u0004\u0010\u0005JF\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0011\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\u000f2\u0006\u0010\u0013\u001a\u00020\u000f2\u0006\u0010\u0014\u001a\u00020\u000f2\u0006\u0010\u0015\u001a\u00020\u000f2\u0006\u0010\u0016\u001a\u00020\u000fJ\u000e\u0010\u0017\u001a\u00020\r2\u0006\u0010\u0018\u001a\u00020\u0019J\u0006\u0010\u001c\u001a\u00020\u0019R\u001a\u0010\u0006\u001a\u00020\u0007X\u0086\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\b\u0010\t\"\u0004\b\n\u0010\u000bR\u0011\u0010\u001a\u001a\u00020\u00198F¢\u0006\u0006\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u001d\u001a\u00020\u000f8F¢\u0006\u0006\u001a\u0004\b\u001e\u0010\u001fR\u0011\u0010 \u001a\u00020\u000f8F¢\u0006\u0006\u001a\u0004\b!\u0010\u001f¨\u0006\""}, d2 = {"Lcom/example/ardrawsketch/sketch/views/TouchImageView$CompatScroller;", "", "context", "Landroid/content/Context;", "<init>", "(Lcom/example/ardrawsketch/sketch/views/TouchImageView;Landroid/content/Context;)V", "overScroller", "Landroid/widget/OverScroller;", "getOverScroller", "()Landroid/widget/OverScroller;", "setOverScroller", "(Landroid/widget/OverScroller;)V", "fling", "", "startX", "", "startY", "velocityX", "velocityY", "minX", "maxX", "minY", "maxY", "forceFinished", "finished", "", "isFinished", "()Z", "computeScrollOffset", "currX", "getCurrX", "()I", "currY", "getCurrY", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* loaded from: classes4.dex */
    public final class CompatScroller {
        private OverScroller overScroller;

        public CompatScroller(Context context) {
            this.overScroller = new OverScroller(context);
        }

        public final OverScroller getOverScroller() {
            return this.overScroller;
        }

        public final void setOverScroller(OverScroller overScroller) {
            Intrinsics.checkNotNullParameter(overScroller, "<set-?>");
            this.overScroller = overScroller;
        }

        public final void fling(int startX, int startY, int velocityX, int velocityY, int minX, int maxX, int minY, int maxY) {
            this.overScroller.fling(startX, startY, velocityX, velocityY, minX, maxX, minY, maxY);
        }

        public final void forceFinished(boolean finished) {
            this.overScroller.forceFinished(finished);
        }

        public final boolean isFinished() {
            return this.overScroller.isFinished();
        }

        public final boolean computeScrollOffset() {
            this.overScroller.computeScrollOffset();
            return this.overScroller.computeScrollOffset();
        }

        public final int getCurrX() {
            return this.overScroller.getCurrX();
        }

        public final int getCurrY() {
            return this.overScroller.getCurrY();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final void compatPostOnAnimation(Runnable runnable) {
        postOnAnimation(runnable);
    }

    public final void setZoomAnimated(float scale, float focusX, float focusY) {
        setZoomAnimated(scale, focusX, focusY, 500);
    }

    public final void setZoomAnimated(float scale, float focusX, float focusY, int zoomTimeMs) {
        AnimatedZoom animation = new AnimatedZoom(this, scale, new PointF(focusX, focusY), zoomTimeMs);
        compatPostOnAnimation(animation);
    }

    public final void setZoomAnimated(float scale, float focusX, float focusY, int zoomTimeMs, OnZoomFinishedListener listener) {
        AnimatedZoom animation = new AnimatedZoom(this, scale, new PointF(focusX, focusY), zoomTimeMs);
        animation.setListener(listener);
        compatPostOnAnimation(animation);
    }

    public final void setZoomAnimated(float scale, float focusX, float focusY, OnZoomFinishedListener listener) {
        AnimatedZoom animation = new AnimatedZoom(this, scale, new PointF(focusX, focusY), 500);
        animation.setListener(listener);
        compatPostOnAnimation(animation);
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* compiled from: TouchImageView.kt */
    @Metadata(d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\b\u0082\u0004\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007¢\u0006\u0004\b\b\u0010\tJ\b\u0010\u0013\u001a\u00020\u0014H\u0016J\b\u0010\u0015\u001a\u00020\u0003H\u0002J\u0010\u0010\u0016\u001a\u00020\u00142\b\u0010\u0017\u001a\u0004\u0018\u00010\u0012R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0003X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0005X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0005X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u0004¢\u0006\u0002\n\u0000R\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0012X\u0082\u000e¢\u0006\u0002\n\u0000¨\u0006\u0018"}, d2 = {"Lcom/example/ardrawsketch/sketch/views/TouchImageView$AnimatedZoom;", "Ljava/lang/Runnable;", "targetZoom", "", "focus", "Landroid/graphics/PointF;", "zoomTimeMillis", "", "<init>", "(Lcom/example/ardrawsketch/sketch/views/TouchImageView;FLandroid/graphics/PointF;I)V", "startTime", "", "startZoom", "startFocus", "targetFocus", "interpolator", "Landroid/view/animation/LinearInterpolator;", "zoomFinishedListener", "Lcom/example/ardrawsketch/sketch/views/OnZoomFinishedListener;", "run", "", "interpolate", "setListener", ServiceSpecificExtraArgs.CastExtraArgs.LISTENER, "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* loaded from: classes4.dex */
    public final class AnimatedZoom implements Runnable {
        private final LinearInterpolator interpolator;
        private final PointF startFocus;
        private final long startTime;
        private final float startZoom;
        private final PointF targetFocus;
        private final float targetZoom;
        final /* synthetic */ TouchImageView this$0;
        private OnZoomFinishedListener zoomFinishedListener;
        private final int zoomTimeMillis;

        public AnimatedZoom(TouchImageView this$0, float targetZoom, PointF focus, int zoomTimeMillis) {
            Intrinsics.checkNotNullParameter(focus, "focus");
            this.this$0 = this$0;
            this.interpolator = new LinearInterpolator();
            this.this$0.setState(ImageActionState.ANIMATE_ZOOM);
            this.startTime = System.currentTimeMillis();
            this.startZoom = this.this$0.getCurrentZoom();
            this.targetZoom = targetZoom;
            this.zoomTimeMillis = zoomTimeMillis;
            this.startFocus = this.this$0.getScrollPosition();
            this.targetFocus = focus;
        }

        @Override // java.lang.Runnable
        public void run() {
            float t = interpolate();
            float nextZoom = this.startZoom + ((this.targetZoom - this.startZoom) * t);
            float nextX = this.startFocus.x + ((this.targetFocus.x - this.startFocus.x) * t);
            float nextY = this.startFocus.y + ((this.targetFocus.y - this.startFocus.y) * t);
            this.this$0.setZoom(nextZoom, nextX, nextY);
            if (t < 1.0f) {
                this.this$0.compatPostOnAnimation(this);
                return;
            }
            this.this$0.setState(ImageActionState.NONE);
            OnZoomFinishedListener onZoomFinishedListener = this.zoomFinishedListener;
            if (onZoomFinishedListener != null) {
                onZoomFinishedListener.onZoomFinished();
            }
        }

        private final float interpolate() {
            float elapsed = ((float) (System.currentTimeMillis() - this.startTime)) / this.zoomTimeMillis;
            return this.interpolator.getInterpolation(Math.min(1.0f, elapsed));
        }

        public final void setListener(OnZoomFinishedListener listener) {
            this.zoomFinishedListener = listener;
        }
    }

    /* compiled from: TouchImageView.kt */
    @Metadata(d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082T¢\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0082T¢\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T¢\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0005X\u0086T¢\u0006\u0002\n\u0000¨\u0006\n"}, d2 = {"Lcom/example/ardrawsketch/sketch/views/TouchImageView$Companion;", "", "<init>", "()V", "SUPER_MIN_MULTIPLIER", "", "SUPER_MAX_MULTIPLIER", "DEFAULT_ZOOM_TIME", "", "AUTOMATIC_MIN_ZOOM", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* loaded from: classes4.dex */
    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        private Companion() {
        }
    }
}
