package com.example.ardrawingsketch.imagepicker.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Build;
import android.os.ParcelFileDescriptor;
import android.os.StatFs;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.documentfile.provider.DocumentFile;
import java.io.File;
import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.channels.FileChannel;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import kotlin.Metadata;
import kotlin.Pair;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.StringsKt;


public final class FileUtil {
    public static final FileUtil INSTANCE = new FileUtil();

    private FileUtil() {
    }

    public static /* synthetic */ File getImageFile$default(FileUtil fileUtil, File file, String str, int i, Object obj) {
        if ((i & 2) != 0) {
            str = null;
        }
        return fileUtil.getImageFile(file, str);
    }

    public final File getImageFile(File fileDir, String extension) {
        Intrinsics.checkNotNullParameter(fileDir, "fileDir");
        String ext = extension == null ? ".jpg" : extension;
        try {
            String fileName = getFileName();
            String imageFileName = fileName + ext;
            if (!fileDir.exists()) {
                fileDir.mkdirs();
            }
            File file = new File(fileDir, imageFileName);
            file.createNewFile();
            return file;
        } catch (IOException ex) {
            ex.printStackTrace();
            return null;
        }
    }

    private final String getFileName() {
        return "IMG_" + getTimestamp();
    }

    private final String getTimestamp() {
        String format = new SimpleDateFormat("yyyyMMdd_HHmmssSSS", Locale.getDefault()).format(new Date());
        Intrinsics.checkNotNullExpressionValue(format, "format(...)");
        return format;
    }

    public final long getFreeSpace(File file) {
        Intrinsics.checkNotNullParameter(file, "file");
        StatFs stat = new StatFs(file.getPath());
        long availBlocks = stat.getAvailableBlocksLong();
        long blockSize = stat.getBlockSizeLong();
        return availBlocks * blockSize;
    }

    public final Pair<Integer, Integer> getImageResolution(Context context, Uri uri) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(uri, "uri");
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        InputStream stream = context.getContentResolver().openInputStream(uri);
        BitmapFactory.decodeStream(stream, null, options);
        return new Pair<>(Integer.valueOf(options.outWidth), Integer.valueOf(options.outHeight));
    }

    public final Pair<Integer, Integer> getImageResolution(File file) {
        Intrinsics.checkNotNullParameter(file, "file");
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(file.getAbsolutePath(), options);
        return new Pair<>(Integer.valueOf(options.outWidth), Integer.valueOf(options.outHeight));
    }

    public final long getImageSize(Context context, Uri uri) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(uri, "uri");
        DocumentFile documentFile = getDocumentFile(context, uri);
        if (documentFile != null) {
            return documentFile.length();
        }
        return 0L;
    }

    public final File getTempFile(Context context, Uri uri) {
        FileDescriptor fileDescriptor;
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(uri, "uri");
        try {
            File destination = new File(context.getCacheDir(), "image_picker.png");
            ParcelFileDescriptor parcelFileDescriptor = context.getContentResolver().openFileDescriptor(uri, "r");
            if (parcelFileDescriptor != null && (fileDescriptor = parcelFileDescriptor.getFileDescriptor()) != null) {
                FileChannel src = new FileInputStream(fileDescriptor).getChannel();
                FileChannel dst = new FileOutputStream(destination).getChannel();
                dst.transferFrom(src, 0L, src.size());
                src.close();
                dst.close();
                return destination;
            }
            return null;
        } catch (IOException ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public final DocumentFile getDocumentFile(Context context, Uri uri) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(uri, "uri");
        if (isFileUri(uri)) {
            String path = FileUriUtils.INSTANCE.getRealPath(context, uri);
            if (path == null) {
                return null;
            }
            DocumentFile file = DocumentFile.fromFile(new File(path));
            return file;
        }
        DocumentFile file2 = DocumentFile.fromSingleUri(context, uri);
        return file2;
    }

    public final Bitmap.CompressFormat getCompressFormat(String extension) {
        Intrinsics.checkNotNullParameter(extension, "extension");
        if (StringsKt.contains((CharSequence) extension, (CharSequence) "png", true)) {
            return Bitmap.CompressFormat.PNG;
        }
        if (StringsKt.contains((CharSequence) extension, (CharSequence) "webp", true)) {
            if (Build.VERSION.SDK_INT >= 30) {
                return Bitmap.CompressFormat.WEBP_LOSSLESS;
            }
            return Bitmap.CompressFormat.WEBP;
        }
        return Bitmap.CompressFormat.JPEG;
    }

    public final String getImageExtension(File file) {
        Intrinsics.checkNotNullParameter(file, "file");
        Uri fromFile = Uri.fromFile(file);
        Intrinsics.checkNotNullExpressionValue(fromFile, "fromFile(...)");
        return getImageExtension(fromFile);
    }

    /* JADX WARN: Code restructure failed: missing block: B:38:0x0046, code lost:
        if ((r1.length() == 0) != false) goto L18;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final String getImageExtension(Uri r12) {
        /*
            r11 = this;
            java.lang.String r0 = "uriImage"
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r12, r0)
            r1 = 0
            r2 = 1
            java.lang.String r0 = r12.getPath()     // Catch: java.lang.Exception -> L37
            if (r0 == 0) goto L39
            r3 = r0
            java.lang.CharSequence r3 = (java.lang.CharSequence) r3     // Catch: java.lang.Exception -> L37
            java.lang.String r4 = "."
            r7 = 6
            r8 = 0
            r5 = 0
            r6 = 0
            int r3 = kotlin.text.StringsKt.lastIndexOf$default(r3, r4, r5, r6, r7, r8)     // Catch: java.lang.Exception -> L37
            r4 = -1
            if (r3 == r4) goto L39
            r5 = r0
            java.lang.CharSequence r5 = (java.lang.CharSequence) r5     // Catch: java.lang.Exception -> L37
            java.lang.String r6 = "."
            r9 = 6
            r10 = 0
            r7 = 0
            r8 = 0
            int r3 = kotlin.text.StringsKt.lastIndexOf$default(r5, r6, r7, r8, r9, r10)     // Catch: java.lang.Exception -> L37
            int r3 = r3 + r2
            java.lang.String r3 = r0.substring(r3)     // Catch: java.lang.Exception -> L37
            java.lang.String r4 = "substring(...)"
            kotlin.jvm.internal.Intrinsics.checkNotNullExpressionValue(r3, r4)     // Catch: java.lang.Exception -> L37
            r1 = r3
            goto L39
        L37:
            r0 = move-exception
            r1 = 0
        L39:
            if (r1 == 0) goto L48
            r0 = r1
            java.lang.CharSequence r0 = (java.lang.CharSequence) r0
            int r0 = r0.length()
            if (r0 != 0) goto L45
            goto L46
        L45:
            r2 = 0
        L46:
            if (r2 == 0) goto L4a
        L48:
            java.lang.String r1 = "jpg"
        L4a:
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            r0.<init>()
            java.lang.String r2 = "."
            java.lang.StringBuilder r0 = r0.append(r2)
            java.lang.StringBuilder r0 = r0.append(r1)
            java.lang.String r0 = r0.toString()
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: com.example.ardrawingsketch.imagepicker.util.FileUtil.getImageExtension(android.net.Uri):java.lang.String");
    }

    private final boolean isFileUri(Uri uri) {
        return StringsKt.equals("file", uri.getScheme(), true);
    }
}
