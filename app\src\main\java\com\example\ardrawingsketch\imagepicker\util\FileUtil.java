package com.example.ardrawingsketch.imagepicker.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Build;
import android.os.ParcelFileDescriptor;
import android.os.StatFs;
import android.util.Log;
import androidx.documentfile.provider.DocumentFile;
import java.io.File;
import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.channels.FileChannel;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * فئة مساعدة لإدارة الملفات والعمليات المتعلقة بها
 * توفر وظائف شاملة للتعامل مع الملفات، الصور، والتخزين
 *
 * الميزات الرئيسية:
 * - إنشاء وحذف الملفات والمجلدات
 * - نسخ ونقل الملفات
 * - قراءة معلومات الصور (الأبعاد، الحجم)
 * - إدارة مساحة التخزين
 * - التعامل مع أنواع ملفات مختلفة
 * - دعم Android Storage Access Framework
 * - معالجة أخطاء الملفات بشكل آمن
 *
 * يستخدم نمط Singleton لضمان وجود نسخة واحدة فقط
 */
public final class FileUtil {
    /** النسخة الوحيدة من الفئة (Singleton Pattern) */
    public static final FileUtil INSTANCE = new FileUtil();

    /** علامة التسجيل لهذه الفئة */
    private static final String TAG = "FileUtil";

    // ===== ثوابت أنواع الملفات =====
    /** امتداد ملفات JPEG */
    public static final String JPEG_EXTENSION = ".jpg";
    /** امتداد ملفات PNG */
    public static final String PNG_EXTENSION = ".png";
    /** امتداد ملفات WebP */
    public static final String WEBP_EXTENSION = ".webp";

    // ===== ثوابت أحجام الملفات =====
    /** حجم البايت بالكيلوبايت */
    public static final long BYTES_IN_KILOBYTE = 1024L;
    /** حجم البايت بالميجابايت */
    public static final long BYTES_IN_MEGABYTE = 1024L * 1024L;
    /** حجم البايت بالجيجابايت */
    public static final long BYTES_IN_GIGABYTE = 1024L * 1024L * 1024L;

    /**
     * منشئ خاص لمنع إنشاء نسخ متعددة
     */
    private FileUtil() {
    }

    /**
     * دالة مساعدة للحصول على ملف صورة بامتداد افتراضي
     * تستدعي الدالة الرئيسية مع امتداد .jpg كافتراضي
     *
     * @param fileUtil  مثيل FileUtil
     * @param fileDir   مجلد الحفظ
     * @param extension امتداد الملف (اختياري)
     * @param flags     معاملات إضافية
     * @param obj       كائن إضافي
     * @return ملف الصورة الجديد
     */
    public static File getImageFile$default(FileUtil fileUtil, File fileDir, String extension, int flags, Object obj) {
        // إذا لم يتم تحديد امتداد، استخدم .jpg كافتراضي
        if ((flags & 2) != 0) {
            extension = null;
        }
        return fileUtil.getImageFile(fileDir, extension);
    }

    /**
     * دالة إنشاء ملف صورة جديد
     * تقوم بإنشاء ملف صورة بامتداد محدد في المجلد المطلوب
     *
     * @param fileDir   مجلد الحفظ المطلوب
     * @param extension امتداد الملف (.jpg, .png, .webp) - اختياري
     * @return ملف الصورة الجديد أو null في حالة الفشل
     */
    public final File getImageFile(File fileDir, String extension) {
        // فحص صحة مجلد الحفظ
        if (fileDir == null) {
            Log.e(TAG, "مجلد الحفظ لا يمكن أن يكون null");
            return null;
        }

        // تحديد امتداد الملف (افتراضي: .jpg)
        String ext = (extension == null || extension.trim().isEmpty()) ? JPEG_EXTENSION : extension;

        try {
            // إنشاء اسم ملف فريد
            String fileName = getFileName();
            String imageFileName = fileName + ext;

            // إنشاء المجلد إذا لم يكن موجوداً
            if (!fileDir.exists()) {
                boolean created = fileDir.mkdirs();
                if (!created) {
                    Log.e(TAG, "فشل في إنشاء المجلد: " + fileDir.getAbsolutePath());
                    return null;
                }
                Log.d(TAG, "تم إنشاء المجلد: " + fileDir.getAbsolutePath());
            }

            // إنشاء ملف الصورة
            File imageFile = new File(fileDir, imageFileName);

            // إنشاء الملف الفعلي
            boolean created = imageFile.createNewFile();
            if (created) {
                Log.d(TAG, "تم إنشاء ملف الصورة: " + imageFile.getAbsolutePath());
            } else {
                Log.d(TAG, "ملف الصورة موجود مسبقاً: " + imageFile.getAbsolutePath());
            }

            return imageFile;

        } catch (IOException ex) {
            Log.e(TAG, "خطأ في إنشاء ملف الصورة: " + ex.getMessage(), ex);
            return null;
        }
    }

    /**
     * دالة إنشاء اسم ملف فريد
     * تقوم بإنشاء اسم ملف فريد باستخدام الطابع الزمني
     *
     * @return اسم الملف بصيغة "IMG_طابع_زمني"
     */
    private String getFileName() {
        String timestamp = getTimestamp();
        String fileName = "IMG_" + timestamp;
        Log.v(TAG, "تم إنشاء اسم ملف: " + fileName);
        return fileName;
    }

    private final String getTimestamp() {
        String format = new SimpleDateFormat("yyyyMMdd_HHmmssSSS", Locale.getDefault()).format(new Date());
        Intrinsics.checkNotNullExpressionValue(format, "format(...)");
        return format;
    }

    public final long getFreeSpace(File file) {
        Intrinsics.checkNotNullParameter(file, "file");
        StatFs stat = new StatFs(file.getPath());
        long availBlocks = stat.getAvailableBlocksLong();
        long blockSize = stat.getBlockSizeLong();
        return availBlocks * blockSize;
    }

    public final Pair<Integer, Integer> getImageResolution(Context context, Uri uri) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(uri, "uri");
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        InputStream stream = context.getContentResolver().openInputStream(uri);
        BitmapFactory.decodeStream(stream, null, options);
        return new Pair<>(Integer.valueOf(options.outWidth), Integer.valueOf(options.outHeight));
    }

    public final Pair<Integer, Integer> getImageResolution(File file) {
        Intrinsics.checkNotNullParameter(file, "file");
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(file.getAbsolutePath(), options);
        return new Pair<>(Integer.valueOf(options.outWidth), Integer.valueOf(options.outHeight));
    }

    public final long getImageSize(Context context, Uri uri) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(uri, "uri");
        DocumentFile documentFile = getDocumentFile(context, uri);
        if (documentFile != null) {
            return documentFile.length();
        }
        return 0L;
    }

    public final File getTempFile(Context context, Uri uri) {
        FileDescriptor fileDescriptor;
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(uri, "uri");
        try {
            File destination = new File(context.getCacheDir(), "image_picker.png");
            ParcelFileDescriptor parcelFileDescriptor = context.getContentResolver().openFileDescriptor(uri, "r");
            if (parcelFileDescriptor != null && (fileDescriptor = parcelFileDescriptor.getFileDescriptor()) != null) {
                FileChannel src = new FileInputStream(fileDescriptor).getChannel();
                FileChannel dst = new FileOutputStream(destination).getChannel();
                dst.transferFrom(src, 0L, src.size());
                src.close();
                dst.close();
                return destination;
            }
            return null;
        } catch (IOException ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public final DocumentFile getDocumentFile(Context context, Uri uri) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(uri, "uri");
        if (isFileUri(uri)) {
            String path = FileUriUtils.INSTANCE.getRealPath(context, uri);
            if (path == null) {
                return null;
            }
            DocumentFile file = DocumentFile.fromFile(new File(path));
            return file;
        }
        DocumentFile file2 = DocumentFile.fromSingleUri(context, uri);
        return file2;
    }

    public final Bitmap.CompressFormat getCompressFormat(String extension) {
        Intrinsics.checkNotNullParameter(extension, "extension");
        if (StringsKt.contains((CharSequence) extension, (CharSequence) "png", true)) {
            return Bitmap.CompressFormat.PNG;
        }
        if (StringsKt.contains((CharSequence) extension, (CharSequence) "webp", true)) {
            if (Build.VERSION.SDK_INT >= 30) {
                return Bitmap.CompressFormat.WEBP_LOSSLESS;
            }
            return Bitmap.CompressFormat.WEBP;
        }
        return Bitmap.CompressFormat.JPEG;
    }

    public final String getImageExtension(File file) {
        Intrinsics.checkNotNullParameter(file, "file");
        Uri fromFile = Uri.fromFile(file);
        Intrinsics.checkNotNullExpressionValue(fromFile, "fromFile(...)");
        return getImageExtension(fromFile);
    }

    /*
     * JADX WARN: Code restructure failed: missing block: B:38:0x0046, code lost:
     * if ((r1.length() == 0) != false) goto L18;
     */
    /*
     * Code decompiled incorrectly, please refer to instructions dump.
     * To view partially-correct code enable 'Show inconsistent code' option in
     * preferences
     */
    public final String getImageExtension(Uri r12) {
        /*
         * r11 = this;
         * java.lang.String r0 = "uriImage"
         * kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r12, r0)
         * r1 = 0
         * r2 = 1
         * java.lang.String r0 = r12.getPath() // Catch: java.lang.Exception -> L37
         * if (r0 == 0) goto L39
         * r3 = r0
         * java.lang.CharSequence r3 = (java.lang.CharSequence) r3 // Catch:
         * java.lang.Exception -> L37
         * java.lang.String r4 = "."
         * r7 = 6
         * r8 = 0
         * r5 = 0
         * r6 = 0
         * int r3 = kotlin.text.StringsKt.lastIndexOf$default(r3, r4, r5, r6, r7, r8) //
         * Catch: java.lang.Exception -> L37
         * r4 = -1
         * if (r3 == r4) goto L39
         * r5 = r0
         * java.lang.CharSequence r5 = (java.lang.CharSequence) r5 // Catch:
         * java.lang.Exception -> L37
         * java.lang.String r6 = "."
         * r9 = 6
         * r10 = 0
         * r7 = 0
         * r8 = 0
         * int r3 = kotlin.text.StringsKt.lastIndexOf$default(r5, r6, r7, r8, r9, r10)
         * // Catch: java.lang.Exception -> L37
         * int r3 = r3 + r2
         * java.lang.String r3 = r0.substring(r3) // Catch: java.lang.Exception -> L37
         * java.lang.String r4 = "substring(...)"
         * kotlin.jvm.internal.Intrinsics.checkNotNullExpressionValue(r3, r4) // Catch:
         * java.lang.Exception -> L37
         * r1 = r3
         * goto L39
         * L37:
         * r0 = move-exception
         * r1 = 0
         * L39:
         * if (r1 == 0) goto L48
         * r0 = r1
         * java.lang.CharSequence r0 = (java.lang.CharSequence) r0
         * int r0 = r0.length()
         * if (r0 != 0) goto L45
         * goto L46
         * L45:
         * r2 = 0
         * L46:
         * if (r2 == 0) goto L4a
         * L48:
         * java.lang.String r1 = "jpg"
         * L4a:
         * java.lang.StringBuilder r0 = new java.lang.StringBuilder
         * r0.<init>()
         * java.lang.String r2 = "."
         * java.lang.StringBuilder r0 = r0.append(r2)
         * java.lang.StringBuilder r0 = r0.append(r1)
         * java.lang.String r0 = r0.toString()
         * return r0
         */
        throw new UnsupportedOperationException(
                "Method not decompiled: com.example.ardrawingsketch.imagepicker.util.FileUtil.getImageExtension(android.net.Uri):java.lang.String");
    }

    private final boolean isFileUri(Uri uri) {
        return StringsKt.equals("file", uri.getScheme(), true);
    }
}
