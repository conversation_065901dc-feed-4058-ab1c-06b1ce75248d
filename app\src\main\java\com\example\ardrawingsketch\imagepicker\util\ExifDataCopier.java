package com.example.ardrawingsketch.imagepicker.util;

import android.util.Log;
import androidx.exifinterface.media.ExifInterface;
import java.io.File;
import java.util.Arrays;
import java.util.List;

/**
 * فئة نسخ بيانات EXIF من الصور
 * تقوم بنسخ بيانات EXIF المهمة من الصورة الأصلية إلى الصورة المعالجة
 *
 * بيانات EXIF تتضمن معلومات مهمة مثل:
 * - معلومات الكاميرا (الطراز، الشركة المصنعة)
 * - إعدادات التصوير (فتحة العدسة، زمن التعريض، حساسية ISO)
 * - معلومات الموقع الجغرافي (GPS)
 * - تاريخ ووقت التقاط الصورة
 *
 * يستخدم نمط Singleton لضمان وجود نسخة واحدة فقط
 */
public final class ExifDataCopier {
    /** النسخة الوحيدة من الفئة (Singleton Pattern) */
    public static final ExifDataCopier INSTANCE = new ExifDataCopier();

    /** علامة التسجيل لهذه الفئة */
    private static final String TAG = "ExifDataCopier";

    /**
     * منشئ خاص لمنع إنشاء نسخ متعددة
     */
    private ExifDataCopier() {
    }

    /**
     * دالة نسخ بيانات EXIF من ملف إلى آخر
     * تقوم بنسخ جميع بيانات EXIF المهمة من الصورة الأصلية إلى الصورة الجديدة
     *
     * @param originalFile    ملف الصورة الأصلية الذي يحتوي على بيانات EXIF
     * @param destinationFile ملف الصورة المقصود الذي سيتم نسخ بيانات EXIF إليه
     */
    public final void copyExif(File originalFile, File destinationFile) {
        // فحص صحة المعاملات المدخلة
        if (originalFile == null) {
            throw new IllegalArgumentException("ملف الصورة الأصلية لا يمكن أن يكون null");
        }
        if (destinationFile == null) {
            throw new IllegalArgumentException("ملف الصورة المقصود لا يمكن أن يكون null");
        }

        try {
            // إنشاء واجهات EXIF للملفين
            ExifInterface originalExif = new ExifInterface(originalFile);
            ExifInterface destinationExif = new ExifInterface(destinationFile);

            // قائمة بجميع خصائص EXIF المهمة التي يجب نسخها
            List<String> importantAttributes = Arrays.asList(
                    // معلومات الكاميرا والعدسة
                    ExifInterface.TAG_F_NUMBER, // فتحة العدسة
                    ExifInterface.TAG_EXPOSURE_TIME, // زمن التعريض
                    ExifInterface.TAG_ISO_SPEED_RATINGS, // حساسية ISO
                    ExifInterface.TAG_FOCAL_LENGTH, // البعد البؤري
                    ExifInterface.TAG_WHITE_BALANCE, // توازن اللون الأبيض
                    ExifInterface.TAG_FLASH, // استخدام الفلاش
                    ExifInterface.TAG_MAKE, // شركة صنع الكاميرا
                    ExifInterface.TAG_MODEL, // طراز الكاميرا
                    ExifInterface.TAG_ORIENTATION, // اتجاه الصورة

                    // معلومات الموقع الجغرافي (GPS)
                    ExifInterface.TAG_GPS_LATITUDE, // خط العرض
                    ExifInterface.TAG_GPS_LATITUDE_REF, // مرجع خط العرض
                    ExifInterface.TAG_GPS_LONGITUDE, // خط الطول
                    ExifInterface.TAG_GPS_LONGITUDE_REF, // مرجع خط الطول
                    ExifInterface.TAG_GPS_ALTITUDE, // الارتفاع
                    ExifInterface.TAG_GPS_ALTITUDE_REF, // مرجع الارتفاع
                    ExifInterface.TAG_GPS_DATESTAMP, // تاريخ GPS
                    ExifInterface.TAG_GPS_TIMESTAMP, // وقت GPS
                    ExifInterface.TAG_GPS_PROCESSING_METHOD, // طريقة معالجة GPS

                    // معلومات التاريخ والوقت
                    ExifInterface.TAG_DATETIME // تاريخ ووقت التقاط الصورة
            );

            // نسخ كل خاصية من القائمة
            for (String attribute : importantAttributes) {
                copyAttributeIfExists(originalExif, destinationExif, attribute);
            }

            // حفظ التغييرات في الملف المقصود
            destinationExif.saveAttributes();

            Log.d(TAG, "تم نسخ بيانات EXIF بنجاح من " + originalFile.getName() + " إلى " + destinationFile.getName());

        } catch (Exception ex) {
            // تسجيل الخطأ في حالة فشل عملية النسخ
            Log.e(TAG, "خطأ في نسخ بيانات EXIF: " + ex.getMessage(), ex);
        }
    }

    /**
     * دالة مساعدة لنسخ خاصية EXIF محددة إذا كانت موجودة
     * تتحقق من وجود الخاصية في الملف الأصلي قبل نسخها
     *
     * @param sourceExif    واجهة EXIF للملف المصدر
     * @param targetExif    واجهة EXIF للملف المقصود
     * @param attributeName اسم الخاصية المراد نسخها
     */
    private void copyAttributeIfExists(ExifInterface sourceExif, ExifInterface targetExif, String attributeName) {
        // الحصول على قيمة الخاصية من الملف المصدر
        String attributeValue = sourceExif.getAttribute(attributeName);

        // نسخ الخاصية فقط إذا كانت موجودة وغير فارغة
        if (attributeValue != null && !attributeValue.trim().isEmpty()) {
            targetExif.setAttribute(attributeName, attributeValue);
            Log.v(TAG, "تم نسخ الخاصية: " + attributeName + " = " + attributeValue);
        }
    }
}
