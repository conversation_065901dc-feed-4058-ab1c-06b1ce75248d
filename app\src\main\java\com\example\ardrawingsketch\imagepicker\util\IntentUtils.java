package com.example.ardrawingsketch.imagepicker.util;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.FileProvider;
import com.example.ardrawingsketch.R;
import java.io.File;
import kotlin.Metadata;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.Intrinsics;


public final class IntentUtils {
    public static final IntentUtils INSTANCE = new IntentUtils();

    private IntentUtils() {
    }

    @JvmStatic
    public static final Intent getGalleryIntent(Context context, String[] mimeTypes) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(mimeTypes, "mimeTypes");
        Intent intent = INSTANCE.getGalleryDocumentIntent(mimeTypes);
        if (intent.resolveActivity(context.getPackageManager()) != null) {
            return intent;
        }
        return INSTANCE.getLegacyGalleryPickIntent(mimeTypes);
    }

    private final Intent getGalleryDocumentIntent(String[] mimeTypes) {
        Intent intent = applyImageTypes(new Intent("android.intent.action.OPEN_DOCUMENT"), mimeTypes);
        intent.addCategory("android.intent.category.OPENABLE");
        intent.addFlags(Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION);
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        intent.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION);
        return intent;
    }

    private final Intent getLegacyGalleryPickIntent(String[] mimeTypes) {
        return applyImageTypes(new Intent("android.intent.action.PICK"), mimeTypes);
    }

    private final Intent applyImageTypes(Intent $this$applyImageTypes, String[] mimeTypes) {
        $this$applyImageTypes.setType("image/*");
        if (!(mimeTypes.length == 0)) {
            $this$applyImageTypes.putExtra("android.intent.extra.MIME_TYPES", mimeTypes);
        }
        return $this$applyImageTypes;
    }

    @JvmStatic
    public static final Intent getCameraIntent(Context context, File file) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(file, "file");
        Intent intent = new Intent("android.media.action.IMAGE_CAPTURE");
        String authority = context.getPackageName() + context.getString(R.string.image_picker_provider_authority_suffix);
        Uri photoURI = FileProvider.getUriForFile(context, authority, file);
        intent.putExtra("output", photoURI);
        return intent;
    }

    @JvmStatic
    public static final boolean isCameraAppAvailable(Context context) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intent intent = new Intent("android.media.action.IMAGE_CAPTURE");
        return intent.resolveActivity(context.getPackageManager()) != null;
    }
}
