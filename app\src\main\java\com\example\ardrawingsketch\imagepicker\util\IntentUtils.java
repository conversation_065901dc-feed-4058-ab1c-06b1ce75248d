package com.example.ardrawingsketch.imagepicker.util;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.FileProvider;
import com.example.ardrawingsketch.R;
import java.io.File;
import kotlin.Metadata;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: IntentUtils.kt */
@Metadata(d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\bÆ\u0002\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J#\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\tH\u0007¢\u0006\u0002\u0010\u000bJ\u001b\u0010\f\u001a\u00020\u00052\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\tH\u0002¢\u0006\u0002\u0010\rJ\u001b\u0010\u000e\u001a\u00020\u00052\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\tH\u0002¢\u0006\u0002\u0010\rJ\u001f\u0010\u000f\u001a\u00020\u0005*\u00020\u00052\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\tH\u0002¢\u0006\u0002\u0010\u0010J\u001a\u0010\u0011\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\u0012\u001a\u00020\u0013H\u0007J\u0010\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0006\u001a\u00020\u0007H\u0007¨\u0006\u0016"}, d2 = {"Lcom/example/ardrawsketch/sketch/imagepicker/util/IntentUtils;", "", "<init>", "()V", "getGalleryIntent", "Landroid/content/Intent;", "context", "Landroid/content/Context;", "mimeTypes", "", "", "(Landroid/content/Context;[Ljava/lang/String;)Landroid/content/Intent;", "getGalleryDocumentIntent", "([Ljava/lang/String;)Landroid/content/Intent;", "getLegacyGalleryPickIntent", "applyImageTypes", "(Landroid/content/Intent;[Ljava/lang/String;)Landroid/content/Intent;", "getCameraIntent", "file", "Ljava/io/File;", "isCameraAppAvailable", "", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes4.dex */
public final class IntentUtils {
    public static final IntentUtils INSTANCE = new IntentUtils();

    private IntentUtils() {
    }

    @JvmStatic
    public static final Intent getGalleryIntent(Context context, String[] mimeTypes) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(mimeTypes, "mimeTypes");
        Intent intent = INSTANCE.getGalleryDocumentIntent(mimeTypes);
        if (intent.resolveActivity(context.getPackageManager()) != null) {
            return intent;
        }
        return INSTANCE.getLegacyGalleryPickIntent(mimeTypes);
    }

    private final Intent getGalleryDocumentIntent(String[] mimeTypes) {
        Intent intent = applyImageTypes(new Intent("android.intent.action.OPEN_DOCUMENT"), mimeTypes);
        intent.addCategory("android.intent.category.OPENABLE");
        intent.addFlags(64);
        intent.addFlags(1);
        intent.addFlags(2);
        return intent;
    }

    private final Intent getLegacyGalleryPickIntent(String[] mimeTypes) {
        return applyImageTypes(new Intent("android.intent.action.PICK"), mimeTypes);
    }

    private final Intent applyImageTypes(Intent $this$applyImageTypes, String[] mimeTypes) {
        $this$applyImageTypes.setType("image/*");
        if (!(mimeTypes.length == 0)) {
            $this$applyImageTypes.putExtra("android.intent.extra.MIME_TYPES", mimeTypes);
        }
        return $this$applyImageTypes;
    }

    @JvmStatic
    public static final Intent getCameraIntent(Context context, File file) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(file, "file");
        Intent intent = new Intent("android.media.action.IMAGE_CAPTURE");
        String authority = context.getPackageName() + context.getString(R.string.image_picker_provider_authority_suffix);
        Uri photoURI = FileProvider.getUriForFile(context, authority, file);
        intent.putExtra("output", photoURI);
        return intent;
    }

    @JvmStatic
    public static final boolean isCameraAppAvailable(Context context) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intent intent = new Intent("android.media.action.IMAGE_CAPTURE");
        return intent.resolveActivity(context.getPackageManager()) != null;
    }
}
