package com.example.ardrawingsketch.views;

import android.widget.ImageView;
import androidx.constraintlayout.widget.ConstraintLayout;
import kotlin.Metadata;


public final class ZoomVariables {
    private float focusX;
    private float focusY;
    private float scale;
    private ImageView.ScaleType scaleType;

    public static /* synthetic */ ZoomVariables copy$default(ZoomVariables zoomVariables, float f, float f2, float f3, ImageView.ScaleType scaleType, int i, Object obj) {
        if ((i & 1) != 0) {
            f = zoomVariables.scale;
        }
        if ((i & 2) != 0) {
            f2 = zoomVariables.focusX;
        }
        if ((i & 4) != 0) {
            f3 = zoomVariables.focusY;
        }
        if ((i & 8) != 0) {
            scaleType = zoomVariables.scaleType;
        }
        return zoomVariables.copy(f, f2, f3, scaleType);
    }

    public final float component1() {
        return this.scale;
    }

    public final float component2() {
        return this.focusX;
    }

    public final float component3() {
        return this.focusY;
    }

    public final ImageView.ScaleType component4() {
        return this.scaleType;
    }

    public final ZoomVariables copy(float f, float f2, float f3, ImageView.ScaleType scaleType) {
        return new ZoomVariables(f, f2, f3, scaleType);
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj instanceof ZoomVariables) {
            ZoomVariables zoomVariables = (ZoomVariables) obj;
            return Float.compare(this.scale, zoomVariables.scale) == 0 && Float.compare(this.focusX, zoomVariables.focusX) == 0 && Float.compare(this.focusY, zoomVariables.focusY) == 0 && this.scaleType == zoomVariables.scaleType;
        }
        return false;
    }

    public int hashCode() {
        return (((((Float.hashCode(this.scale) * 31) + Float.hashCode(this.focusX)) * 31) + Float.hashCode(this.focusY)) * 31) + (this.scaleType == null ? 0 : this.scaleType.hashCode());
    }

    public String toString() {
        float f = this.scale;
        float f2 = this.focusX;
        float f3 = this.focusY;
        return "ZoomVariables(scale=" + f + ", focusX=" + f2 + ", focusY=" + f3 + ", scaleType=" + this.scaleType + ")";
    }

    public ZoomVariables(float scale, float focusX, float focusY, ImageView.ScaleType scaleType) {
        this.scale = scale;
        this.focusX = focusX;
        this.focusY = focusY;
        this.scaleType = scaleType;
    }

    public final float getScale() {
        return this.scale;
    }

    public final void setScale(float f) {
        this.scale = f;
    }

    public final float getFocusX() {
        return this.focusX;
    }

    public final void setFocusX(float f) {
        this.focusX = f;
    }

    public final float getFocusY() {
        return this.focusY;
    }

    public final void setFocusY(float f) {
        this.focusY = f;
    }

    public final ImageView.ScaleType getScaleType() {
        return this.scaleType;
    }

    public final void setScaleType(ImageView.ScaleType scaleType) {
        this.scaleType = scaleType;
    }
}
