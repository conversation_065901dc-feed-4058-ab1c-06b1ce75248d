package com.example.ardrawingsketch.imagepicker;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import com.example.ardrawingsketch.imagepicker.ImagePicker;
import com.example.ardrawingsketch.imagepicker.constant.ImageProvider;
import com.example.ardrawingsketch.imagepicker.listener.DismissListener;
import com.example.ardrawingsketch.imagepicker.listener.ResultListener;
import com.example.ardrawingsketch.imagepicker.util.DialogHelper;
import java.io.File;
import kotlin.Metadata;
import kotlin.Unit;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;


public class ImagePicker {
    public static final Companion Companion = new Companion(null);
    public static final String EXTRA_CROP = "extra.crop";
    public static final String EXTRA_CROP_X = "extra.crop_x";
    public static final String EXTRA_CROP_Y = "extra.crop_y";
    public static final String EXTRA_ERROR = "extra.error";
    public static final String EXTRA_FILE_PATH = "extra.file_path";
    public static final String EXTRA_IMAGE_MAX_SIZE = "extra.image_max_size";
    public static final String EXTRA_IMAGE_PROVIDER = "extra.image_provider";
    public static final String EXTRA_MAX_HEIGHT = "extra.max_height";
    public static final String EXTRA_MAX_WIDTH = "extra.max_width";
    public static final String EXTRA_MIME_TYPES = "extra.mime_types";
    public static final String EXTRA_SAVE_DIRECTORY = "extra.save_directory";
    public static final int RESULT_ERROR = 64;


    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        private Companion() {
        }

        @JvmStatic
        public final Builder with(Activity activity) {
            Intrinsics.checkNotNullParameter(activity, "activity");
            return new Builder(activity);
        }

        @JvmStatic
        public final Builder with(Fragment fragment) {
            Intrinsics.checkNotNullParameter(fragment, "fragment");
            return new Builder(fragment);
        }
    }

    @JvmStatic
    public static final Builder with(Activity activity) {
        return Companion.with(activity);
    }

    @JvmStatic
    public static final Builder with(Fragment fragment) {
        return Companion.with(fragment);
    }


    public static final class Builder {
        private final Activity activity;
        private boolean crop;
        private float cropX;
        private float cropY;
        private DismissListener dismissListener;
        private Fragment fragment;
        private ImageProvider imageProvider;
        private Function1<? super ImageProvider, Unit> imageProviderInterceptor;
        private int maxHeight;
        private long maxSize;
        private int maxWidth;
        private String[] mimeTypes;
        private String saveDir;

        public Builder(Activity activity) {
            Intrinsics.checkNotNullParameter(activity, "activity");
            this.activity = activity;
            this.imageProvider = ImageProvider.BOTH;
            this.mimeTypes = new String[0];
        }

        /* JADX WARN: Illegal instructions before constructor call */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public Builder(Fragment r3) {
            /*
                r2 = this;
                java.lang.String r0 = "fragment"
                kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r3, r0)
                androidx.fragment.app.FragmentActivity r0 = r3.requireActivity()
                java.lang.String r1 = "requireActivity(...)"
                kotlin.jvm.internal.Intrinsics.checkNotNullExpressionValue(r0, r1)
                android.app.Activity r0 = (android.app.Activity) r0
                r2.<init>(r0)
                r2.fragment = r3
                return
            */
            throw new UnsupportedOperationException("Method not decompiled: com.example.ardrawingsketch.imagepicker.ImagePicker.Builder.<init>(androidx.fragment.app.Fragment):void");
        }

        public final Builder provider(ImageProvider imageProvider) {
            Intrinsics.checkNotNullParameter(imageProvider, "imageProvider");
            this.imageProvider = imageProvider;
            return this;
        }

        public final Builder cameraOnly() {
            this.imageProvider = ImageProvider.CAMERA;
            return this;
        }

        public final Builder galleryOnly() {
            this.imageProvider = ImageProvider.GALLERY;
            return this;
        }

        public final Builder crop() {
            this.crop = true;
            return this;
        }

        public final Builder saveDir(File file) {
            Intrinsics.checkNotNullParameter(file, "file");
            this.saveDir = file.getAbsolutePath();
            return this;
        }

        public final void start(int reqCode) {
            if (this.imageProvider == ImageProvider.BOTH) {
                showImageProviderDialog(reqCode);
            } else {
                startActivity(reqCode);
            }
        }

        private final void showImageProviderDialog(final int reqCode) {
            DialogHelper.INSTANCE.showChooseAppDialog(this.activity, new ResultListener<ImageProvider>() { // from class: com.example.ardrawingsketch.imagepicker.ImagePicker$Builder$showImageProviderDialog$1
                @Override // com.example.ardrawingsketch.imagepicker.listener.ResultListener
                public void onResult(ImageProvider t) {
                    Function1 function1;
                    ImageProvider imageProvider;
                    if (t != null) {
                        Builder builder = Builder.this;
                        int i = reqCode;
                        builder.imageProvider = t;
                        function1 = builder.imageProviderInterceptor;
                        if (function1 != null) {
                            imageProvider = builder.imageProvider;
                            function1.invoke(imageProvider);
                        }
                        builder.startActivity(i);
                    }
                }
            }, this.dismissListener);
        }

        private final Bundle getBundle() {
            Bundle bundle = new Bundle();
            bundle.putSerializable(ImagePicker.EXTRA_IMAGE_PROVIDER, this.imageProvider);
            bundle.putStringArray(ImagePicker.EXTRA_MIME_TYPES, this.mimeTypes);
            bundle.putBoolean(ImagePicker.EXTRA_CROP, this.crop);
            bundle.putFloat(ImagePicker.EXTRA_CROP_X, this.cropX);
            bundle.putFloat(ImagePicker.EXTRA_CROP_Y, this.cropY);
            bundle.putInt(ImagePicker.EXTRA_MAX_WIDTH, this.maxWidth);
            bundle.putInt(ImagePicker.EXTRA_MAX_HEIGHT, this.maxHeight);
            bundle.putLong(ImagePicker.EXTRA_IMAGE_MAX_SIZE, this.maxSize);
            bundle.putString(ImagePicker.EXTRA_SAVE_DIRECTORY, this.saveDir);
            return bundle;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public final void startActivity(int reqCode) {
            Intent intent = new Intent(this.activity, ImagePickerActivity.class);
            intent.putExtras(getBundle());
            if (this.fragment != null) {
                Fragment fragment = this.fragment;
                if (fragment != null) {
                    fragment.startActivityForResult(intent, reqCode);
                    return;
                }
                return;
            }
            this.activity.startActivityForResult(intent, reqCode);
        }
    }
}
