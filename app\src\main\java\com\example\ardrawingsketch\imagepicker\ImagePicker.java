package com.example.ardrawingsketch.imagepicker;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import com.example.ardrawingsketch.imagepicker.ImagePicker;
import com.example.ardrawingsketch.imagepicker.constant.ImageProvider;
import com.example.ardrawingsketch.imagepicker.listener.DismissListener;
import com.example.ardrawingsketch.imagepicker.listener.ResultListener;
import com.example.ardrawingsketch.imagepicker.util.DialogHelper;
import java.io.File;
import kotlin.Metadata;
import kotlin.Unit;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: ImagePicker.kt */
@Metadata(d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0005\b\u0016\u0018\u0000 \u00042\u00020\u0001:\u0002\u0004\u0005B\u0007¢\u0006\u0004\b\u0002\u0010\u0003¨\u0006\u0006"}, d2 = {"Lcom/example/ardrawsketch/sketch/imagepicker/ImagePicker;", "", "<init>", "()V", "Companion", "Builder", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes9.dex */
public class ImagePicker {
    public static final Companion Companion = new Companion(null);
    public static final String EXTRA_CROP = "extra.crop";
    public static final String EXTRA_CROP_X = "extra.crop_x";
    public static final String EXTRA_CROP_Y = "extra.crop_y";
    public static final String EXTRA_ERROR = "extra.error";
    public static final String EXTRA_FILE_PATH = "extra.file_path";
    public static final String EXTRA_IMAGE_MAX_SIZE = "extra.image_max_size";
    public static final String EXTRA_IMAGE_PROVIDER = "extra.image_provider";
    public static final String EXTRA_MAX_HEIGHT = "extra.max_height";
    public static final String EXTRA_MAX_WIDTH = "extra.max_width";
    public static final String EXTRA_MIME_TYPES = "extra.mime_types";
    public static final String EXTRA_SAVE_DIRECTORY = "extra.save_directory";
    public static final int RESULT_ERROR = 64;

    /* compiled from: ImagePicker.kt */
    @Metadata(d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J\u0010\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0015H\u0007J\u0010\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0016\u001a\u00020\u0017H\u0007R\u000e\u0010\u0004\u001a\u00020\u0005X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0080T¢\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0080T¢\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0007X\u0080T¢\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0007X\u0080T¢\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0007X\u0080T¢\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0007X\u0080T¢\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0007X\u0080T¢\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0007X\u0080T¢\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0007X\u0080T¢\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0007X\u0080T¢\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0007X\u0080T¢\u0006\u0002\n\u0000¨\u0006\u0018"}, d2 = {"Lcom/example/ardrawsketch/sketch/imagepicker/ImagePicker$Companion;", "", "<init>", "()V", "RESULT_ERROR", "", "EXTRA_IMAGE_PROVIDER", "", "EXTRA_IMAGE_MAX_SIZE", "EXTRA_CROP", "EXTRA_CROP_X", "EXTRA_CROP_Y", "EXTRA_MAX_WIDTH", "EXTRA_MAX_HEIGHT", "EXTRA_SAVE_DIRECTORY", "EXTRA_ERROR", "EXTRA_FILE_PATH", "EXTRA_MIME_TYPES", "with", "Lcom/example/ardrawsketch/sketch/imagepicker/ImagePicker$Builder;", "activity", "Landroid/app/Activity;", "fragment", "Landroidx/fragment/app/Fragment;", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* loaded from: classes9.dex */
    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        private Companion() {
        }

        @JvmStatic
        public final Builder with(Activity activity) {
            Intrinsics.checkNotNullParameter(activity, "activity");
            return new Builder(activity);
        }

        @JvmStatic
        public final Builder with(Fragment fragment) {
            Intrinsics.checkNotNullParameter(fragment, "fragment");
            return new Builder(fragment);
        }
    }

    @JvmStatic
    public static final Builder with(Activity activity) {
        return Companion.with(activity);
    }

    @JvmStatic
    public static final Builder with(Fragment fragment) {
        return Companion.with(fragment);
    }

    /* compiled from: ImagePicker.kt */
    @Metadata(d1 = {"\u0000j\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003¢\u0006\u0004\b\u0004\u0010\u0005B\u0011\b\u0016\u0012\u0006\u0010\u0006\u001a\u00020\u0007¢\u0006\u0004\b\u0004\u0010\bJ\u000e\u0010\u001f\u001a\u00020\u00002\u0006\u0010\t\u001a\u00020\nJ\u0006\u0010 \u001a\u00020\u0000J\u0006\u0010!\u001a\u00020\u0000J\u0006\u0010\u0012\u001a\u00020\u0000J\u000e\u0010\u001e\u001a\u00020\u00002\u0006\u0010\"\u001a\u00020#J\u000e\u0010$\u001a\u00020\u001b2\u0006\u0010%\u001a\u00020\u0015J\u0010\u0010&\u001a\u00020\u001b2\u0006\u0010%\u001a\u00020\u0015H\u0002J\b\u0010'\u001a\u00020(H\u0002J\u0010\u0010)\u001a\u00020\u001b2\u0006\u0010%\u001a\u00020\u0015H\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004¢\u0006\u0002\n\u0000R\u0010\u0010\u0006\u001a\u0004\u0018\u00010\u0007X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e¢\u0006\u0002\n\u0000R\u0016\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fX\u0082\u000e¢\u0006\u0004\n\u0002\u0010\u000eR\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0010X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0015X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u000e¢\u0006\u0002\n\u0000R\u001c\u0010\u0019\u001a\u0010\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u001b\u0018\u00010\u001aX\u0082\u000e¢\u0006\u0002\n\u0000R\u0010\u0010\u001c\u001a\u0004\u0018\u00010\u001dX\u0082\u000e¢\u0006\u0002\n\u0000R\u0010\u0010\u001e\u001a\u0004\u0018\u00010\rX\u0082\u000e¢\u0006\u0002\n\u0000¨\u0006*"}, d2 = {"Lcom/example/ardrawsketch/sketch/imagepicker/ImagePicker$Builder;", "", "activity", "Landroid/app/Activity;", "<init>", "(Landroid/app/Activity;)V", "fragment", "Landroidx/fragment/app/Fragment;", "(Landroidx/fragment/app/Fragment;)V", "imageProvider", "Lcom/example/ardrawsketch/sketch/imagepicker/constant/ImageProvider;", "mimeTypes", "", "", "[Ljava/lang/String;", "cropX", "", "cropY", "crop", "", "maxWidth", "", "maxHeight", "maxSize", "", "imageProviderInterceptor", "Lkotlin/Function1;", "", "dismissListener", "Lcom/example/ardrawsketch/sketch/imagepicker/listener/DismissListener;", "saveDir", "provider", "cameraOnly", "galleryOnly", "file", "Ljava/io/File;", "start", "reqCode", "showImageProviderDialog", "getBundle", "Landroid/os/Bundle;", "startActivity", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* loaded from: classes9.dex */
    public static final class Builder {
        private final Activity activity;
        private boolean crop;
        private float cropX;
        private float cropY;
        private DismissListener dismissListener;
        private Fragment fragment;
        private ImageProvider imageProvider;
        private Function1<? super ImageProvider, Unit> imageProviderInterceptor;
        private int maxHeight;
        private long maxSize;
        private int maxWidth;
        private String[] mimeTypes;
        private String saveDir;

        public Builder(Activity activity) {
            Intrinsics.checkNotNullParameter(activity, "activity");
            this.activity = activity;
            this.imageProvider = ImageProvider.BOTH;
            this.mimeTypes = new String[0];
        }

        /* JADX WARN: Illegal instructions before constructor call */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public Builder(Fragment r3) {
            /*
                r2 = this;
                java.lang.String r0 = "fragment"
                kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r3, r0)
                androidx.fragment.app.FragmentActivity r0 = r3.requireActivity()
                java.lang.String r1 = "requireActivity(...)"
                kotlin.jvm.internal.Intrinsics.checkNotNullExpressionValue(r0, r1)
                android.app.Activity r0 = (android.app.Activity) r0
                r2.<init>(r0)
                r2.fragment = r3
                return
            */
            throw new UnsupportedOperationException("Method not decompiled: com.example.ardrawingsketch.imagepicker.ImagePicker.Builder.<init>(androidx.fragment.app.Fragment):void");
        }

        public final Builder provider(ImageProvider imageProvider) {
            Intrinsics.checkNotNullParameter(imageProvider, "imageProvider");
            this.imageProvider = imageProvider;
            return this;
        }

        public final Builder cameraOnly() {
            this.imageProvider = ImageProvider.CAMERA;
            return this;
        }

        public final Builder galleryOnly() {
            this.imageProvider = ImageProvider.GALLERY;
            return this;
        }

        public final Builder crop() {
            this.crop = true;
            return this;
        }

        public final Builder saveDir(File file) {
            Intrinsics.checkNotNullParameter(file, "file");
            this.saveDir = file.getAbsolutePath();
            return this;
        }

        public final void start(int reqCode) {
            if (this.imageProvider == ImageProvider.BOTH) {
                showImageProviderDialog(reqCode);
            } else {
                startActivity(reqCode);
            }
        }

        private final void showImageProviderDialog(final int reqCode) {
            DialogHelper.INSTANCE.showChooseAppDialog(this.activity, new ResultListener<ImageProvider>() { // from class: com.example.ardrawingsketch.imagepicker.ImagePicker$Builder$showImageProviderDialog$1
                @Override // com.example.ardrawingsketch.imagepicker.listener.ResultListener
                public void onResult(ImageProvider t) {
                    Function1 function1;
                    ImageProvider imageProvider;
                    if (t != null) {
                        Builder builder = Builder.this;
                        int i = reqCode;
                        builder.imageProvider = t;
                        function1 = builder.imageProviderInterceptor;
                        if (function1 != null) {
                            imageProvider = builder.imageProvider;
                            function1.invoke(imageProvider);
                        }
                        builder.startActivity(i);
                    }
                }
            }, this.dismissListener);
        }

        private final Bundle getBundle() {
            Bundle bundle = new Bundle();
            bundle.putSerializable(ImagePicker.EXTRA_IMAGE_PROVIDER, this.imageProvider);
            bundle.putStringArray(ImagePicker.EXTRA_MIME_TYPES, this.mimeTypes);
            bundle.putBoolean(ImagePicker.EXTRA_CROP, this.crop);
            bundle.putFloat(ImagePicker.EXTRA_CROP_X, this.cropX);
            bundle.putFloat(ImagePicker.EXTRA_CROP_Y, this.cropY);
            bundle.putInt(ImagePicker.EXTRA_MAX_WIDTH, this.maxWidth);
            bundle.putInt(ImagePicker.EXTRA_MAX_HEIGHT, this.maxHeight);
            bundle.putLong(ImagePicker.EXTRA_IMAGE_MAX_SIZE, this.maxSize);
            bundle.putString(ImagePicker.EXTRA_SAVE_DIRECTORY, this.saveDir);
            return bundle;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public final void startActivity(int reqCode) {
            Intent intent = new Intent(this.activity, ImagePickerActivity.class);
            intent.putExtras(getBundle());
            if (this.fragment != null) {
                Fragment fragment = this.fragment;
                if (fragment != null) {
                    fragment.startActivityForResult(intent, reqCode);
                    return;
                }
                return;
            }
            this.activity.startActivityForResult(intent, reqCode);
        }
    }
}
