package com.example.ardrawingsketch.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airbnb.lottie.LottieAnimationView;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.views.TouchImageView;
import com.otaliastudios.cameraview.CameraView;

/* loaded from: classes3.dex */
public final class ActivityCameraBinding implements ViewBinding {
    public final ConstraintLayout actionBarCamera;
    public final SeekBar alphaSeek;
    public final LottieAnimationView animationView;
    public final AppCompatImageView backCamera;
    public final ConstraintLayout bottomLayer;
    public final ConstraintLayout cameraLayout;
    public final CameraView cameraView;
    public final TextView labelCamera;
    public final ConstraintLayout main;
    public final TouchImageView objImage;
    public final AppCompatImageView opacityHundred;
    public final TextView opacityText;
    public final AppCompatImageView opacityZero;
    public final AppCompatImageView relCamera;
    public final AppCompatImageView relEditRound;
    public final AppCompatImageView relFlash;
    public final AppCompatImageView relFlip;
    public final AppCompatImageView relGallery;
    public final AppCompatImageView relLock;
    private final ConstraintLayout rootView;

    private ActivityCameraBinding(ConstraintLayout rootView, ConstraintLayout actionBarCamera, SeekBar alphaSeek, LottieAnimationView animationView, AppCompatImageView backCamera, ConstraintLayout bottomLayer, ConstraintLayout cameraLayout, CameraView cameraView, TextView labelCamera, ConstraintLayout main, TouchImageView objImage, AppCompatImageView opacityHundred, TextView opacityText, AppCompatImageView opacityZero, AppCompatImageView relCamera, AppCompatImageView relEditRound, AppCompatImageView relFlash, AppCompatImageView relFlip, AppCompatImageView relGallery, AppCompatImageView relLock) {
        this.rootView = rootView;
        this.actionBarCamera = actionBarCamera;
        this.alphaSeek = alphaSeek;
        this.animationView = animationView;
        this.backCamera = backCamera;
        this.bottomLayer = bottomLayer;
        this.cameraLayout = cameraLayout;
        this.cameraView = cameraView;
        this.labelCamera = labelCamera;
        this.main = main;
        this.objImage = objImage;
        this.opacityHundred = opacityHundred;
        this.opacityText = opacityText;
        this.opacityZero = opacityZero;
        this.relCamera = relCamera;
        this.relEditRound = relEditRound;
        this.relFlash = relFlash;
        this.relFlip = relFlip;
        this.relGallery = relGallery;
        this.relLock = relLock;
    }

    @Override // androidx.viewbinding.ViewBinding
    public ConstraintLayout getRoot() {
        return this.rootView;
    }

    public static ActivityCameraBinding inflate(LayoutInflater inflater) {
        return inflate(inflater, null, false);
    }

    public static ActivityCameraBinding inflate(LayoutInflater inflater, ViewGroup parent, boolean attachToParent) {
        View root = inflater.inflate(R.layout.activity_camera, parent, false);
        if (attachToParent) {
            parent.addView(root);
        }
        return bind(root);
    }

    public static ActivityCameraBinding bind(View rootView) {
        int id = R.id.action_bar_camera;
        ConstraintLayout actionBarCamera = (ConstraintLayout) ViewBindings.findChildViewById(rootView, id);
        if (actionBarCamera != null) {
            id = R.id.alpha_seek;
            SeekBar alphaSeek = (SeekBar) ViewBindings.findChildViewById(rootView, id);
            if (alphaSeek != null) {
                id = R.id.animation_view;
                LottieAnimationView animationView = (LottieAnimationView) ViewBindings.findChildViewById(rootView, id);
                if (animationView != null) {
                    id = R.id.back_camera;
                    AppCompatImageView backCamera = (AppCompatImageView) ViewBindings.findChildViewById(rootView, id);
                    if (backCamera != null) {
                        id = R.id.bottom_layer;
                        ConstraintLayout bottomLayer = (ConstraintLayout) ViewBindings.findChildViewById(rootView, id);
                        if (bottomLayer != null) {
                            id = R.id.camera_layout;
                            ConstraintLayout cameraLayout = (ConstraintLayout) ViewBindings.findChildViewById(rootView, id);
                            if (cameraLayout != null) {
                                id = R.id.camera_view;
                                CameraView cameraView = (CameraView) ViewBindings.findChildViewById(rootView, id);
                                if (cameraView != null) {
                                    id = R.id.label_camera;
                                    TextView labelCamera = (TextView) ViewBindings.findChildViewById(rootView, id);
                                    if (labelCamera != null) {
                                        ConstraintLayout main = (ConstraintLayout) rootView;
                                        id = R.id.objImage;
                                        TouchImageView objImage = (TouchImageView) ViewBindings.findChildViewById(rootView, id);
                                        if (objImage != null) {
                                            id = R.id.opacity_hundred;
                                            AppCompatImageView opacityHundred = (AppCompatImageView) ViewBindings.findChildViewById(rootView, id);
                                            if (opacityHundred != null) {
                                                id = R.id.opacity_text;
                                                TextView opacityText = (TextView) ViewBindings.findChildViewById(rootView, id);
                                                if (opacityText != null) {
                                                    id = R.id.opacity_zero;
                                                    AppCompatImageView opacityZero = (AppCompatImageView) ViewBindings.findChildViewById(rootView, id);
                                                    if (opacityZero != null) {
                                                        id = R.id.rel_camera;
                                                        AppCompatImageView relCamera = (AppCompatImageView) ViewBindings.findChildViewById(rootView, id);
                                                        if (relCamera != null) {
                                                            id = R.id.rel_edit_round;
                                                            AppCompatImageView relEditRound = (AppCompatImageView) ViewBindings.findChildViewById(rootView, id);
                                                            if (relEditRound != null) {
                                                                id = R.id.rel_flash;
                                                                AppCompatImageView relFlash = (AppCompatImageView) ViewBindings.findChildViewById(rootView, id);
                                                                if (relFlash != null) {
                                                                    id = R.id.rel_flip;
                                                                    AppCompatImageView relFlip = (AppCompatImageView) ViewBindings.findChildViewById(rootView, id);
                                                                    if (relFlip != null) {
                                                                        id = R.id.rel_gallery;
                                                                        AppCompatImageView relGallery = (AppCompatImageView) ViewBindings.findChildViewById(rootView, id);
                                                                        if (relGallery != null) {
                                                                            id = R.id.rel_lock;
                                                                            AppCompatImageView relLock = (AppCompatImageView) ViewBindings.findChildViewById(rootView, id);
                                                                            if (relLock != null) {
                                                                                return new ActivityCameraBinding((ConstraintLayout) rootView, actionBarCamera, alphaSeek, animationView, backCamera, bottomLayer, cameraLayout, cameraView, labelCamera, main, objImage, opacityHundred, opacityText, opacityZero, relCamera, relEditRound, relFlash, relFlip, relGallery, relLock);
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        String missingId = rootView.getResources().getResourceName(id);
        throw new NullPointerException("Missing required view with ID: ".concat(missingId));
    }
}
