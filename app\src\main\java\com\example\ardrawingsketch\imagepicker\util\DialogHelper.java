package com.example.ardrawingsketch.imagepicker.util;

import android.content.Context;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.app.AlertDialog;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.imagepicker.constant.ImageProvider;
import com.example.ardrawingsketch.imagepicker.listener.DismissListener;
import com.example.ardrawingsketch.imagepicker.listener.ResultListener;
import com.google.android.gms.common.internal.ServiceSpecificExtraArgs;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;


public final class DialogHelper {
    public static final DialogHelper INSTANCE = new DialogHelper();

    private DialogHelper() {
    }

    public final void showChooseAppDialog(Context context, final ResultListener<ImageProvider> listener, final DismissListener dismissListener) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(listener, "listener");
        LayoutInflater layoutInflater = LayoutInflater.from(context);
        View customView = layoutInflater.inflate(R.layout.dialog_choose_app, (ViewGroup) null);
        final AlertDialog dialog = new AlertDialog.Builder(context).setTitle(R.string.title_choose_image_provider).setView(customView).setOnCancelListener(new DialogInterface.OnCancelListener() { // from class: com.example.ardrawingsketch.imagepicker.util.DialogHelper$$ExternalSyntheticLambda0
            @Override // android.content.DialogInterface.OnCancelListener
            public final void onCancel(DialogInterface dialogInterface) {
                ResultListener.this.onResult(null);
            }
        }).setNegativeButton(R.string.action_cancel, new DialogInterface.OnClickListener() { // from class: com.example.ardrawingsketch.imagepicker.util.DialogHelper$$ExternalSyntheticLambda1
            @Override // android.content.DialogInterface.OnClickListener
            public final void onClick(DialogInterface dialogInterface, int i) {
                ResultListener.this.onResult(null);
            }
        }).setOnDismissListener(new DialogInterface.OnDismissListener() { // from class: com.example.ardrawingsketch.imagepicker.util.DialogHelper$$ExternalSyntheticLambda2
            @Override // android.content.DialogInterface.OnDismissListener
            public final void onDismiss(DialogInterface dialogInterface) {
                DialogHelper.showChooseAppDialog$lambda$2(DismissListener.this, dialogInterface);
            }
        }).show();
        customView.findViewById(R.id.lytCameraPick).setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.imagepicker.util.DialogHelper$$ExternalSyntheticLambda3
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                DialogHelper.showChooseAppDialog$lambda$3(ResultListener.this, dialog, view);
            }
        });
        customView.findViewById(R.id.lytGalleryPick).setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.imagepicker.util.DialogHelper$$ExternalSyntheticLambda4
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                DialogHelper.showChooseAppDialog$lambda$4(ResultListener.this, dialog, view);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void showChooseAppDialog$lambda$2(DismissListener $dismissListener, DialogInterface it) {
        if ($dismissListener != null) {
            $dismissListener.onDismiss();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void showChooseAppDialog$lambda$3(ResultListener $listener, AlertDialog $dialog, View it) {
        $listener.onResult(ImageProvider.CAMERA);
        $dialog.dismiss();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void showChooseAppDialog$lambda$4(ResultListener $listener, AlertDialog $dialog, View it) {
        $listener.onResult(ImageProvider.GALLERY);
        $dialog.dismiss();
    }
}
