package com.example.ardrawingsketch.imagepicker.util;

import android.content.Context;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.app.AlertDialog;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.imagepicker.constant.ImageProvider;
import com.example.ardrawingsketch.imagepicker.listener.DismissListener;
import com.example.ardrawingsketch.imagepicker.listener.ResultListener;
import com.google.android.gms.common.internal.ServiceSpecificExtraArgs;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: DialogHelper.kt */
@Metadata(d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\bÀ\u0002\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J&\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t2\b\u0010\u000b\u001a\u0004\u0018\u00010\f¨\u0006\r"}, d2 = {"Lcom/example/ardrawsketch/sketch/imagepicker/util/DialogHelper;", "", "<init>", "()V", "showChooseAppDialog", "", "context", "Landroid/content/Context;", ServiceSpecificExtraArgs.CastExtraArgs.LISTENER, "Lcom/example/ardrawsketch/sketch/imagepicker/listener/ResultListener;", "Lcom/example/ardrawsketch/sketch/imagepicker/constant/ImageProvider;", "dismissListener", "Lcom/example/ardrawsketch/sketch/imagepicker/listener/DismissListener;", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes4.dex */
public final class DialogHelper {
    public static final DialogHelper INSTANCE = new DialogHelper();

    private DialogHelper() {
    }

    public final void showChooseAppDialog(Context context, final ResultListener<ImageProvider> listener, final DismissListener dismissListener) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(listener, "listener");
        LayoutInflater layoutInflater = LayoutInflater.from(context);
        View customView = layoutInflater.inflate(R.layout.dialog_choose_app, (ViewGroup) null);
        final AlertDialog dialog = new AlertDialog.Builder(context).setTitle(R.string.title_choose_image_provider).setView(customView).setOnCancelListener(new DialogInterface.OnCancelListener() { // from class: com.example.ardrawingsketch.imagepicker.util.DialogHelper$$ExternalSyntheticLambda0
            @Override // android.content.DialogInterface.OnCancelListener
            public final void onCancel(DialogInterface dialogInterface) {
                ResultListener.this.onResult(null);
            }
        }).setNegativeButton(R.string.action_cancel, new DialogInterface.OnClickListener() { // from class: com.example.ardrawingsketch.imagepicker.util.DialogHelper$$ExternalSyntheticLambda1
            @Override // android.content.DialogInterface.OnClickListener
            public final void onClick(DialogInterface dialogInterface, int i) {
                ResultListener.this.onResult(null);
            }
        }).setOnDismissListener(new DialogInterface.OnDismissListener() { // from class: com.example.ardrawingsketch.imagepicker.util.DialogHelper$$ExternalSyntheticLambda2
            @Override // android.content.DialogInterface.OnDismissListener
            public final void onDismiss(DialogInterface dialogInterface) {
                DialogHelper.showChooseAppDialog$lambda$2(DismissListener.this, dialogInterface);
            }
        }).show();
        customView.findViewById(R.id.lytCameraPick).setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.imagepicker.util.DialogHelper$$ExternalSyntheticLambda3
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                DialogHelper.showChooseAppDialog$lambda$3(ResultListener.this, dialog, view);
            }
        });
        customView.findViewById(R.id.lytGalleryPick).setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.imagepicker.util.DialogHelper$$ExternalSyntheticLambda4
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                DialogHelper.showChooseAppDialog$lambda$4(ResultListener.this, dialog, view);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void showChooseAppDialog$lambda$2(DismissListener $dismissListener, DialogInterface it) {
        if ($dismissListener != null) {
            $dismissListener.onDismiss();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void showChooseAppDialog$lambda$3(ResultListener $listener, AlertDialog $dialog, View it) {
        $listener.onResult(ImageProvider.CAMERA);
        $dialog.dismiss();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void showChooseAppDialog$lambda$4(ResultListener $listener, AlertDialog $dialog, View it) {
        $listener.onResult(ImageProvider.GALLERY);
        $dialog.dismiss();
    }
}
