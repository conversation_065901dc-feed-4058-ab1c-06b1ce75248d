package com.example.ardrawingsketch.imagepicker.util;

import android.content.Context;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.app.AlertDialog;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.imagepicker.constant.ImageProvider;
import com.example.ardrawingsketch.imagepicker.listener.DismissListener;
import com.example.ardrawingsketch.imagepicker.listener.ResultListener;

/**
 * فئة مساعدة لعرض حوارات اختيار مصدر الصورة
 * توفر واجهة موحدة لعرض حوار يسمح للمستخدم بالاختيار بين الكاميرا والمعرض
 *
 * يستخدم نمط Singleton لضمان وجود نسخة واحدة فقط
 */
public final class DialogHelper {
    /** النسخة الوحيدة من الفئة (Singleton Pattern) */
    public static final DialogHelper INSTANCE = new DialogHelper();

    /**
     * منشئ خاص لمنع إنشاء نسخ متعددة
     */
    private DialogHelper() {
    }

    /**
     * دالة عرض حوار اختيار مصدر الصورة
     * تعرض حواراً يحتوي على خيارين: الكاميرا والمعرض
     *
     * @param context         سياق التطبيق المطلوب لعرض الحوار
     * @param listener        مستمع النتائج الذي يتلقى اختيار المستخدم
     * @param dismissListener مستمع إغلاق الحوار (اختياري)
     */
    public final void showChooseAppDialog(Context context, final ResultListener<ImageProvider> listener,
            final DismissListener dismissListener) {
        // فحص صحة المعاملات المدخلة
        if (context == null) {
            throw new IllegalArgumentException("سياق التطبيق لا يمكن أن يكون null");
        }
        if (listener == null) {
            throw new IllegalArgumentException("مستمع النتائج لا يمكن أن يكون null");
        }

        // إنشاء واجهة الحوار المخصصة
        LayoutInflater layoutInflater = LayoutInflater.from(context);
        View customView = layoutInflater.inflate(R.layout.dialog_choose_app, (ViewGroup) null);

        // إنشاء وإعداد الحوار
        final AlertDialog dialog = new AlertDialog.Builder(context)
                .setTitle(R.string.title_choose_image_provider) // عنوان الحوار
                .setView(customView) // واجهة الحوار المخصصة
                .setOnCancelListener(new DialogInterface.OnCancelListener() {
                    @Override
                    public void onCancel(DialogInterface dialogInterface) {
                        // عند إلغاء الحوار، إرسال null للمستمع
                        listener.onResult(null);
                    }
                })
                .setNegativeButton(R.string.action_cancel, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialogInterface, int i) {
                        // عند النقر على زر الإلغاء، إرسال null للمستمع
                        listener.onResult(null);
                    }
                })
                .setOnDismissListener(new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialogInterface) {
                        // عند إغلاق الحوار، استدعاء مستمع الإغلاق إذا كان موجوداً
                        if (dismissListener != null) {
                            dismissListener.onDismiss();
                        }
                    }
                })
                .show();
        // إعداد مستمع النقر على خيار الكاميرا
        customView.findViewById(R.id.lytCameraPick).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // إرسال اختيار الكاميرا للمستمع وإغلاق الحوار
                listener.onResult(ImageProvider.CAMERA);
                dialog.dismiss();
            }
        });
        // إعداد مستمع النقر على خيار المعرض
        customView.findViewById(R.id.lytGalleryPick).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // إرسال اختيار المعرض للمستمع وإغلاق الحوار
                listener.onResult(ImageProvider.GALLERY);
                dialog.dismiss();
            }
        });
    }
}
