package com.example.ardrawingsketch.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ardrawingsketch.R;

/* loaded from: classes3.dex */
public final class ActivityCategoryBinding implements ViewBinding {
    public final ConstraintLayout actionBarMain;
    public final ConstraintLayout bottomBarMain;
    public final AppCompatImageView btnBack;
    public final AppCompatImageView divider;
    public final TextView labelMain;
    public final ConstraintLayout main;
    private final ConstraintLayout rootView;
    public final TextView selectFromCamera;
    public final TextView selectFromGallery;
    public final RecyclerView sketchListRv;

    private ActivityCategoryBinding(ConstraintLayout rootView, ConstraintLayout actionBarMain, ConstraintLayout bottomBarMain, AppCompatImageView btnBack, AppCompatImageView divider, TextView labelMain, ConstraintLayout main, TextView selectFromCamera, TextView selectFromGallery, RecyclerView sketchListRv) {
        this.rootView = rootView;
        this.actionBarMain = actionBarMain;
        this.bottomBarMain = bottomBarMain;
        this.btnBack = btnBack;
        this.divider = divider;
        this.labelMain = labelMain;
        this.main = main;
        this.selectFromCamera = selectFromCamera;
        this.selectFromGallery = selectFromGallery;
        this.sketchListRv = sketchListRv;
    }

    @Override // androidx.viewbinding.ViewBinding
    public ConstraintLayout getRoot() {
        return this.rootView;
    }

    public static ActivityCategoryBinding inflate(LayoutInflater inflater) {
        return inflate(inflater, null, false);
    }

    public static ActivityCategoryBinding inflate(LayoutInflater inflater, ViewGroup parent, boolean attachToParent) {
        View root = inflater.inflate(R.layout.activity_category, parent, false);
        if (attachToParent) {
            parent.addView(root);
        }
        return bind(root);
    }

    public static ActivityCategoryBinding bind(View rootView) {
        int id = R.id.action_bar_main;
        ConstraintLayout actionBarMain = (ConstraintLayout) ViewBindings.findChildViewById(rootView, id);
        if (actionBarMain != null) {
            id = R.id.bottom_bar_main;
            ConstraintLayout bottomBarMain = (ConstraintLayout) ViewBindings.findChildViewById(rootView, id);
            if (bottomBarMain != null) {
                id = R.id.btn_back;
                AppCompatImageView btnBack = (AppCompatImageView) ViewBindings.findChildViewById(rootView, id);
                if (btnBack != null) {
                    id = R.id.divider;
                    AppCompatImageView divider = (AppCompatImageView) ViewBindings.findChildViewById(rootView, id);
                    if (divider != null) {
                        id = R.id.label_main;
                        TextView labelMain = (TextView) ViewBindings.findChildViewById(rootView, id);
                        if (labelMain != null) {
                            ConstraintLayout main = (ConstraintLayout) rootView;
                            id = R.id.select_from_camera;
                            TextView selectFromCamera = (TextView) ViewBindings.findChildViewById(rootView, id);
                            if (selectFromCamera != null) {
                                id = R.id.select_from_gallery;
                                TextView selectFromGallery = (TextView) ViewBindings.findChildViewById(rootView, id);
                                if (selectFromGallery != null) {
                                    id = R.id.sketch_list_rv;
                                    RecyclerView sketchListRv = (RecyclerView) ViewBindings.findChildViewById(rootView, id);
                                    if (sketchListRv != null) {
                                        return new ActivityCategoryBinding((ConstraintLayout) rootView, actionBarMain, bottomBarMain, btnBack, divider, labelMain, main, selectFromCamera, selectFromGallery, sketchListRv);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        String missingId = rootView.getResources().getResourceName(id);
        throw new NullPointerException("Missing required view with ID: ".concat(missingId));
    }
}
