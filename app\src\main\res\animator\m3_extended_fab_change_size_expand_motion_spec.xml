<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android">
    <objectAnimator android:interpolator="?attr/motionEasingEmphasizedInterpolator" android:duration="?attr/motionDurationMedium4" android:startOffset="0" android:propertyName="width"/>
    <objectAnimator android:interpolator="?attr/motionEasingEmphasizedInterpolator" android:duration="?attr/motionDurationMedium4" android:startOffset="0" android:propertyName="height"/>
    <objectAnimator android:interpolator="?attr/motionEasingEmphasizedInterpolator" android:duration="?attr/motionDurationMedium4" android:startOffset="0" android:propertyName="paddingStart"/>
    <objectAnimator android:interpolator="?attr/motionEasingEmphasizedInterpolator" android:duration="?attr/motionDurationMedium4" android:startOffset="0" android:propertyName="paddingEnd"/>
    <objectAnimator android:interpolator="?attr/motionEasingEmphasizedInterpolator" android:duration="?attr/motionDurationShort4" android:startOffset="?attr/motionDurationShort2" android:propertyName="labelOpacity"/>
</set>
