<?xml version="1.0" encoding="utf-8"?>
<!-- ملف البيان الرئيسي للتطبيق - يحتوي على جميع الإعدادات والصلاحيات المطلوبة -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.example.ardrawingsketch">

    <!-- إعدادات إصدار الأندرويد المدعومة -->
    <!-- الحد الأدنى: أندرويد 5.0 (API 21) -->
    <!-- الإصدار المستهدف: أندرويد 12 (API 32) -->
    <uses-sdk android:minSdkVersion="21" android:targetSdkVersion="32" />

    <!-- ===== صلاحيات الكاميرا والوسائط ===== -->
    <!-- صلاحية استخدام الكاميرا لالتقاط الصور -->
    <uses-permission android:name="android.permission.CAMERA" />
    <!-- صلاحية تسجيل الصوت -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <!-- صلاحية استخدام الفلاش -->
    <uses-permission android:name="android.permission.FLASHLIGHT" />

    <!-- ===== صلاحيات التخزين ===== -->
    <!-- صلاحية قراءة الصور للأجهزة الحديثة (أندرويد 13+) -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <!-- صلاحية قراءة التخزين الخارجي للأجهزة القديمة -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <!-- صلاحية الكتابة في التخزين الخارجي للأجهزة القديمة -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />

    <!-- ===== صلاحيات الشبكة ===== -->
    <!-- صلاحية الوصول للإنترنت -->
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- صلاحية فحص حالة الشبكة -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- ===== ميزات الأجهزة المطلوبة ===== -->
    <!-- ميزة الكاميرا (اختيارية - التطبيق يعمل بدونها) -->
    <uses-feature android:name="android.hardware.camera" android:required="false" />
    <!-- ميزة التركيز التلقائي للكاميرا -->
    <uses-feature android:name="android.hardware.camera.autofocus" android:required="false" />
    <!-- ميزة الكاميرا الأمامية -->
    <uses-feature android:name="android.hardware.camera.front" android:required="false" />
    <!-- ميزة الميكروفون -->
    <uses-feature android:name="android.hardware.microphone" android:required="false" />

    <!-- ===== صلاحيات مخصصة للتطبيق ===== -->
    <!-- صلاحية مخصصة لحماية المستقبلات الديناميكية -->
    <permission android:name="com.example.ardrawingsketch.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />
    <uses-permission
        android:name="com.example.ardrawingsketch.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
    <!-- ===== إعدادات التطبيق الرئيسية ===== -->
    <application
        android:name="com.example.ardrawingsketch.MyApplication"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:theme="@style/Theme.ARDrawSketch"
        android:allowBackup="true"
        android:supportsRtl="true"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory">
        <!-- ===== الأنشطة (Activities) ===== -->

        <!-- النشاط الرئيسي - نقطة دخول التطبيق -->
        <activity
            android:name="com.example.ardrawingsketch.activities.MainActivity"
            android:exported="true">
            <!-- مرشح النية لجعل هذا النشاط نقطة البداية -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- نشاط شاشة البداية -->
        <activity
            android:name="com.example.ardrawingsketch.activities.SplashActivity"
            android:exported="false" />

        <!-- نشاط دليل الاستخدام -->
        <activity
            android:name="com.example.ardrawingsketch.activities.GuideActivity"
            android:exported="false" />

        <!-- نشاط الكاميرا -->
        <activity
            android:name="com.example.ardrawingsketch.activities.CameraActivity"
            android:exported="false" />

        <!-- نشاط قائمة الرسومات -->
        <activity
            android:name="com.example.ardrawingsketch.activities.DrawingListActivity"
            android:exported="false" />

        <!-- نشاط الفئات -->
        <activity
            android:name="com.example.ardrawingsketch.activities.CategoryActivity"
            android:exported="false" />

        <!-- نشاط اختيار الصور مع ثيم شفاف -->
        <activity
            android:name="com.example.ardrawingsketch.imagepicker.ImagePickerActivity"
            android:theme="@style/Theme.Transparent.ImagePicker"
            android:screenOrientation="unspecified"
            android:exported="false" />
        <!-- ===== مقدمو المحتوى (Content Providers) ===== -->

        <!-- مقدم ملفات اختيار الصور - للوصول الآمن للملفات -->
        <provider
            android:name="com.example.ardrawingsketch.imagepicker.ImagePickerFileProvider"
            android:authorities="com.example.ardrawingsketch.imagepicker.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <!-- مسارات الملفات المسموح بالوصول إليها -->
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/image_picker_provider_paths" />
        </provider>

        <!-- مقدم تهيئة المكتبات عند بدء التطبيق -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.example.ardrawingsketch.androidx-startup"
            android:exported="false">
            <!-- تهيئة دعم الرموز التعبيرية -->
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <!-- تهيئة دورة حياة العمليات -->
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <!-- تهيئة مثبت الملف الشخصي للأداء -->
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <!-- ===== البيانات الوصفية ===== -->
        <!-- إصدار خدمات جوجل بلاي -->
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <!-- ===== المستقبلات (Broadcast Receivers) ===== -->

        <!-- مستقبل تثبيت الملف الشخصي للأداء -->
        <!-- يستخدم لتحسين أداء التطبيق عبر ملفات التعريف -->
        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:permission="android.permission.DUMP"
            android:enabled="true"
            android:exported="true"
            android:directBootAware="false">

            <!-- مرشح لتثبيت الملف الشخصي -->
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>

            <!-- مرشح لتخطي الملف -->
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>

            <!-- مرشح لحفظ الملف الشخصي -->
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>

            <!-- مرشح لعمليات القياس المعياري -->
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>