package com.example.ardrawingsketch;

import com.example.ardrawingsketch.model.Category;
import java.util.ArrayList;

/* loaded from: classes8.dex */
public class GlobalConstant {
    public static final String CATEGORY_AESTHETICS = "Aesthetics";
    public static final String CATEGORY_ANIMALS = "Animals";
    public static final String CATEGORY_ANIME = "Anime";
    public static final String CATEGORY_CARS = "Cars";
    public static final String CATEGORY_CHOICE = "category_choice";
    public static final String CATEGORY_DRAWING_LESSONS = "Drawing Lessons";
    public static final String CATEGORY_FOR_KIDS = "For Kids";
    public static final String CATEGORY_NAME = "category_name";
    public static final String CATEGORY_NATURE = "Nature";
    public static final String CATEGORY_PEOPLE = "People";
    public static final String CATEGORY_TRENDING = "Trending";
    public static final String CONTENT = "content";
    public static final String FROM_DRAWABLE = "from_drawable";
    public static final String FROM_GALLERY = "from_gallery";
    public static final String IMAGE_PATH = "image_path";
    public static final int PERMISSION_CODE = 2000;
    public static final int PERMISSION_CODE_1 = 2100;
    public static final int PERMISSION_CODE_CAMERA = 2300;
    public static final int PERMISSION_CODE_GALLERY = 2200;
    public static int[] listAesthetics = {R.drawable.aesthetics_1, R.drawable.aesthetics_2, R.drawable.aesthetics_3, R.drawable.aesthetics_4, R.drawable.aesthetics_5, R.drawable.aesthetics_6, R.drawable.aesthetics_7, R.drawable.aesthetics_8, R.drawable.aesthetics_9, R.drawable.aesthetics_10, R.drawable.aesthetics_11, R.drawable.aesthetics_12, R.drawable.aesthetics_13, R.drawable.aesthetics_14, R.drawable.aesthetics_15, R.drawable.aesthetics_16, R.drawable.aesthetics_17, R.drawable.aesthetics_18, R.drawable.aesthetics_19, R.drawable.aesthetics_20};
    public static int[] listAnime = {R.drawable.anime_1, R.drawable.anime_2, R.drawable.anime_3, R.drawable.anime_4, R.drawable.anime_5, R.drawable.anime_6, R.drawable.anime_7, R.drawable.anime_8, R.drawable.anime_9, R.drawable.anime_10, R.drawable.anime_11, R.drawable.anime_12, R.drawable.anime_13, R.drawable.anime_14, R.drawable.anime_15, R.drawable.anime_16, R.drawable.anime_17, R.drawable.anime_18, R.drawable.anime_19, R.drawable.anime_20};
    public static int[] listCars = {R.drawable.car_1, R.drawable.car_2, R.drawable.car_3, R.drawable.car_4, R.drawable.car_5, R.drawable.car_6, R.drawable.car_7, R.drawable.car_8, R.drawable.car_9, R.drawable.car_10, R.drawable.car_11, R.drawable.car_12, R.drawable.car_13, R.drawable.car_14, R.drawable.car_15, R.drawable.car_16, R.drawable.car_17, R.drawable.car_18, R.drawable.car_19, R.drawable.car_20};
    public static int[] listPeople = {R.drawable.people_1, R.drawable.people_2, R.drawable.people_3, R.drawable.people_4, R.drawable.people_5, R.drawable.people_6, R.drawable.people_7, R.drawable.people_8, R.drawable.people_9, R.drawable.people_10, R.drawable.people_11, R.drawable.people_12, R.drawable.people_13, R.drawable.people_14, R.drawable.people_15, R.drawable.people_16, R.drawable.people_17, R.drawable.people_18, R.drawable.people_19, R.drawable.people_20};
    public static int[] listAnimal = {R.drawable.animal_1, R.drawable.animal_2, R.drawable.animal_3, R.drawable.animal_4, R.drawable.animal_5, R.drawable.animal_6, R.drawable.animal_7, R.drawable.animal_8, R.drawable.animal_9, R.drawable.animal_10, R.drawable.animal_11, R.drawable.animal_12, R.drawable.animal_13, R.drawable.animal_14, R.drawable.animal_15, R.drawable.animal_16, R.drawable.animal_17, R.drawable.animal_18, R.drawable.animal_19, R.drawable.animal_20};
    public static int[] listTrending = {R.drawable.trending_1, R.drawable.trending_2, R.drawable.trending_3, R.drawable.trending_4, R.drawable.trending_5, R.drawable.trending_6, R.drawable.trending_7, R.drawable.trending_8, R.drawable.trending_9, R.drawable.trending_10, R.drawable.trending_11, R.drawable.trending_12, R.drawable.trending_13, R.drawable.trending_14, R.drawable.trending_15, R.drawable.trending_16, R.drawable.trending_17, R.drawable.trending_18, R.drawable.trending_19, R.drawable.trending_20};
    public static int[] listNature = {R.drawable.nature_1, R.drawable.nature_2, R.drawable.nature_3, R.drawable.nature_4, R.drawable.nature_5, R.drawable.nature_6, R.drawable.nature_7, R.drawable.nature_8, R.drawable.nature_9, R.drawable.nature_10, R.drawable.nature_11, R.drawable.nature_12, R.drawable.nature_13, R.drawable.nature_14, R.drawable.nature_15, R.drawable.nature_16, R.drawable.nature_17, R.drawable.nature_18, R.drawable.nature_19, R.drawable.nature_20};
    public static int[] listKid = {R.drawable.kid_1, R.drawable.kid_2, R.drawable.kid_3, R.drawable.kid_4, R.drawable.kid_5, R.drawable.kid_6, R.drawable.kid_7, R.drawable.kid_8, R.drawable.kid_9, R.drawable.kid_10, R.drawable.kid_11, R.drawable.kid_12, R.drawable.kid_13, R.drawable.kid_14, R.drawable.kid_15, R.drawable.kid_16, R.drawable.kid_17, R.drawable.kid_18, R.drawable.kid_19, R.drawable.kid_20};
    public static int[] listDrawing = {R.drawable.drawing_1, R.drawable.drawing_2, R.drawable.drawing_3, R.drawable.drawing_4, R.drawable.drawing_5, R.drawable.drawing_6, R.drawable.drawing_7, R.drawable.drawing_8, R.drawable.drawing_9, R.drawable.drawing_10, R.drawable.drawing_11, R.drawable.drawing_12, R.drawable.drawing_13, R.drawable.drawing_14, R.drawable.drawing_15, R.drawable.drawing_16, R.drawable.drawing_17, R.drawable.drawing_18, R.drawable.drawing_19, R.drawable.drawing_20, R.drawable.drawing_21, R.drawable.drawing_22};

    public static ArrayList<Category> createCategoryList() {
        ArrayList<Category> arrayList = new ArrayList<>();
        arrayList.add(new Category(R.string.category_trending, R.drawable.category_trend, 0));
        arrayList.add(new Category(R.string.category_drawing_lessons, R.drawable.category_drawing_lessons, 1));
        arrayList.add(new Category(R.string.category_anime, R.drawable.category_drawing_lessons, 2));
        arrayList.add(new Category(R.string.category_people, R.drawable.category_people, 3));
        arrayList.add(new Category(R.string.category_animals, R.drawable.category_animal, 4));
        arrayList.add(new Category(R.string.category_aesthetics, R.drawable.category_aesthetics, 5));
        arrayList.add(new Category(R.string.category_cars, R.drawable.category_cars, 6));
        arrayList.add(new Category(R.string.category_for_kids, R.drawable.category_for_kids, 7));
        arrayList.add(new Category(R.string.category_nature, R.drawable.category_nature, 8));
        return arrayList;
    }
}
