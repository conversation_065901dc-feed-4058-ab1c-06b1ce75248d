package com.example.ardrawingsketch.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ardrawingsketch.R;

/* loaded from: classes3.dex */
public final class ActivityDrawingListBinding implements ViewBinding {
    public final AppCompatImageView btnBack;
    public final RecyclerView drawingListRv;
    public final TextView labelHelp;
    public final ConstraintLayout main;
    private final ConstraintLayout rootView;
    public final ConstraintLayout toolbar;

    private ActivityDrawingListBinding(ConstraintLayout rootView, AppCompatImageView btnBack, RecyclerView drawingListRv, TextView labelHelp, ConstraintLayout main, ConstraintLayout toolbar) {
        this.rootView = rootView;
        this.btnBack = btnBack;
        this.drawingListRv = drawingListRv;
        this.labelHelp = labelHelp;
        this.main = main;
        this.toolbar = toolbar;
    }

    @Override // androidx.viewbinding.ViewBinding
    public ConstraintLayout getRoot() {
        return this.rootView;
    }

    public static ActivityDrawingListBinding inflate(LayoutInflater inflater) {
        return inflate(inflater, null, false);
    }

    public static ActivityDrawingListBinding inflate(LayoutInflater inflater, ViewGroup parent, boolean attachToParent) {
        View root = inflater.inflate(R.layout.activity_drawing_list, parent, false);
        if (attachToParent) {
            parent.addView(root);
        }
        return bind(root);
    }

    public static ActivityDrawingListBinding bind(View rootView) {
        int id = R.id.btn_back;
        AppCompatImageView btnBack = (AppCompatImageView) ViewBindings.findChildViewById(rootView, id);
        if (btnBack != null) {
            id = R.id.drawing_list_rv;
            RecyclerView drawingListRv = (RecyclerView) ViewBindings.findChildViewById(rootView, id);
            if (drawingListRv != null) {
                id = R.id.label_help;
                TextView labelHelp = (TextView) ViewBindings.findChildViewById(rootView, id);
                if (labelHelp != null) {
                    ConstraintLayout main = (ConstraintLayout) rootView;
                    id = R.id.toolbar;
                    ConstraintLayout toolbar = (ConstraintLayout) ViewBindings.findChildViewById(rootView, id);
                    if (toolbar != null) {
                        return new ActivityDrawingListBinding((ConstraintLayout) rootView, btnBack, drawingListRv, labelHelp, main, toolbar);
                    }
                }
            }
        }
        String missingId = rootView.getResources().getResourceName(id);
        throw new NullPointerException("Missing required view with ID: ".concat(missingId));
    }
}
