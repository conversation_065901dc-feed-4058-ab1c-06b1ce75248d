package com.example.ardrawingsketch.imagepicker.provider;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityCompat;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.imagepicker.ImagePicker;
import com.example.ardrawingsketch.imagepicker.ImagePickerActivity;
import com.example.ardrawingsketch.imagepicker.util.FileUtil;
import com.example.ardrawingsketch.imagepicker.util.IntentUtils;
import com.example.ardrawingsketch.imagepicker.util.PermissionUtil;
import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: CameraProvider.kt */
@Metadata(d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u0000 %2\u00020\u0001:\u0001%B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003¢\u0006\u0004\b\u0004\u0010\u0005J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fH\u0016J\u0012\u0010\r\u001a\u00020\n2\b\u0010\u000e\u001a\u0004\u0018\u00010\fH\u0016J\u0006\u0010\u000f\u001a\u00020\nJ\b\u0010\u0010\u001a\u00020\nH\u0002J\b\u0010\u0011\u001a\u00020\nH\u0002J\b\u0010\u0012\u001a\u00020\nH\u0002J\u0010\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0016H\u0002J\u001b\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00190\u00182\u0006\u0010\u0015\u001a\u00020\u0016H\u0002¢\u0006\u0002\u0010\u001aJ\u000e\u0010\u001b\u001a\u00020\n2\u0006\u0010\u001c\u001a\u00020\u001dJ \u0010\u001e\u001a\u00020\n2\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001f\u001a\u00020\u001d2\b\u0010 \u001a\u0004\u0018\u00010!J\b\u0010\"\u001a\u00020\nH\u0002J\b\u0010#\u001a\u00020\nH\u0014J\u0006\u0010$\u001a\u00020\nR\u0010\u0010\u0006\u001a\u0004\u0018\u00010\u0007X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0082\u0004¢\u0006\u0002\n\u0000¨\u0006&"}, d2 = {"Lcom/example/ardrawsketch/sketch/imagepicker/provider/CameraProvider;", "Lcom/example/ardrawsketch/sketch/imagepicker/provider/BaseProvider;", "activity", "Lcom/example/ardrawsketch/sketch/imagepicker/ImagePickerActivity;", "<init>", "(Lcom/example/ardrawsketch/sketch/imagepicker/ImagePickerActivity;)V", "mCameraFile", "Ljava/io/File;", "mFileDir", "onSaveInstanceState", "", "outState", "Landroid/os/Bundle;", "onRestoreInstanceState", "savedInstanceState", "startIntent", "checkPermission", "startCameraIntent", "requestPermission", "isPermissionGranted", "", "context", "Landroid/content/Context;", "getRequiredPermission", "", "", "(Landroid/content/Context;)[Ljava/lang/String;", "onRequestPermissionsResult", "requestCode", "", "onActivityResult", "resultCode", "data", "Landroid/content/Intent;", "handleResult", "onFailure", "delete", "Companion", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes7.dex */
public final class CameraProvider extends BaseProvider {
    private static final int CAMERA_INTENT_REQ_CODE = 4281;
    private static final int PERMISSION_INTENT_REQ_CODE = 4282;
    private static final String STATE_CAMERA_FILE = "state.camera_file";
    private File mCameraFile;
    private final File mFileDir;
    public static final Companion Companion = new Companion(null);
    private static final String[] REQUIRED_PERMISSIONS = {"android.permission.CAMERA"};

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public CameraProvider(ImagePickerActivity activity) {
        super(activity);
        Intrinsics.checkNotNullParameter(activity, "activity");
        Bundle bundle = activity.getIntent().getExtras();
        String fileDir = (bundle == null ? new Bundle() : bundle).getString(ImagePicker.EXTRA_SAVE_DIRECTORY);
        this.mFileDir = getFileDir(fileDir);
    }

    /* compiled from: CameraProvider.kt */
    @Metadata(d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0011\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082T¢\u0006\u0002\n\u0000R\u0016\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004¢\u0006\u0004\n\u0002\u0010\bR\u000e\u0010\t\u001a\u00020\nX\u0082T¢\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082T¢\u0006\u0002\n\u0000¨\u0006\f"}, d2 = {"Lcom/example/ardrawsketch/sketch/imagepicker/provider/CameraProvider$Companion;", "", "<init>", "()V", "STATE_CAMERA_FILE", "", "REQUIRED_PERMISSIONS", "", "[Ljava/lang/String;", "CAMERA_INTENT_REQ_CODE", "", "PERMISSION_INTENT_REQ_CODE", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* loaded from: classes7.dex */
    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        private Companion() {
        }
    }

    @Override // com.example.ardrawingsketch.imagepicker.provider.BaseProvider
    public void onSaveInstanceState(Bundle outState) {
        Intrinsics.checkNotNullParameter(outState, "outState");
        outState.putSerializable(STATE_CAMERA_FILE, this.mCameraFile);
    }

    @Override // com.example.ardrawingsketch.imagepicker.provider.BaseProvider
    public void onRestoreInstanceState(Bundle savedInstanceState) {
        this.mCameraFile = (File) (savedInstanceState != null ? savedInstanceState.getSerializable(STATE_CAMERA_FILE) : null);
    }

    public final void startIntent() {
        if (!IntentUtils.isCameraAppAvailable(this)) {
            setError(R.string.error_camera_app_not_found);
        } else {
            checkPermission();
        }
    }

    private final void checkPermission() {
        if (isPermissionGranted(this)) {
            startCameraIntent();
        } else {
            requestPermission();
        }
    }

    private final void startCameraIntent() {
        File file = FileUtil.getImageFile$default(FileUtil.INSTANCE, this.mFileDir, null, 2, null);
        this.mCameraFile = file;
        if (file != null && file.exists()) {
            Intent cameraIntent = IntentUtils.getCameraIntent(this, file);
            if (cameraIntent != null) {
                getActivity().startActivityForResult(cameraIntent, CAMERA_INTENT_REQ_CODE);
                return;
            }
            return;
        }
        setError(R.string.error_failed_to_create_camera_image_file);
    }

    private final void requestPermission() {
        ActivityCompat.requestPermissions(getActivity(), getRequiredPermission(getActivity()), PERMISSION_INTENT_REQ_CODE);
    }

    private final boolean isPermissionGranted(Context context) {
        for (String str : getRequiredPermission(context)) {
            if (!PermissionUtil.INSTANCE.isPermissionGranted(context, str)) {
                return false;
            }
        }
        return true;
    }

    private final String[] getRequiredPermission(Context context) {
        String[] strArr = REQUIRED_PERMISSIONS;
        Collection arrayList = new ArrayList();
        for (String str : strArr) {
            if (PermissionUtil.INSTANCE.isPermissionInManifest(context, str)) {
                arrayList.add(str);
            }
        }
        return (String[]) ((List) arrayList).toArray(new String[0]);
    }

    public final void onRequestPermissionsResult(int requestCode) {
        if (requestCode == PERMISSION_INTENT_REQ_CODE) {
            if (isPermissionGranted(this)) {
                startIntent();
                return;
            }
            String error = getString(R.string.permission_camera_denied);
            Intrinsics.checkNotNullExpressionValue(error, "getString(...)");
            setError(error);
        }
    }

    public final void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == CAMERA_INTENT_REQ_CODE) {
            if (resultCode == -1) {
                handleResult();
            } else {
                setResultCancel();
            }
        }
    }

    private final void handleResult() {
        ImagePickerActivity activity = getActivity();
        Uri fromFile = Uri.fromFile(this.mCameraFile);
        Intrinsics.checkNotNullExpressionValue(fromFile, "fromFile(...)");
        activity.setImage(fromFile);
    }

    @Override // com.example.ardrawingsketch.imagepicker.provider.BaseProvider
    protected void onFailure() {
        delete();
    }

    public final void delete() {
        File file = this.mCameraFile;
        if (file != null) {
            file.delete();
        }
        this.mCameraFile = null;
    }
}
