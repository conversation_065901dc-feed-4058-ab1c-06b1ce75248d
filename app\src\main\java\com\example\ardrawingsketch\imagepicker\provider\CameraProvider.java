package com.example.ardrawingsketch.imagepicker.provider;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityCompat;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.imagepicker.ImagePicker;
import com.example.ardrawingsketch.imagepicker.ImagePickerActivity;
import com.example.ardrawingsketch.imagepicker.util.FileUtil;
import com.example.ardrawingsketch.imagepicker.util.IntentUtils;
import com.example.ardrawingsketch.imagepicker.util.PermissionUtil;
import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;


public final class CameraProvider extends BaseProvider {
    private static final int CAMERA_INTENT_REQ_CODE = 4281;
    private static final int PERMISSION_INTENT_REQ_CODE = 4282;
    private static final String STATE_CAMERA_FILE = "state.camera_file";
    private File mCameraFile;
    private final File mFileDir;
    public static final Companion Companion = new Companion(null);
    private static final String[] REQUIRED_PERMISSIONS = {"android.permission.CAMERA"};

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public CameraProvider(ImagePickerActivity activity) {
        super(activity);
        Intrinsics.checkNotNullParameter(activity, "activity");
        Bundle bundle = activity.getIntent().getExtras();
        String fileDir = (bundle == null ? new Bundle() : bundle).getString(ImagePicker.EXTRA_SAVE_DIRECTORY);
        this.mFileDir = getFileDir(fileDir);
    }


    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        private Companion() {
        }
    }

    @Override // com.example.ardrawingsketch.imagepicker.provider.BaseProvider
    public void onSaveInstanceState(Bundle outState) {
        Intrinsics.checkNotNullParameter(outState, "outState");
        outState.putSerializable(STATE_CAMERA_FILE, this.mCameraFile);
    }

    @Override // com.example.ardrawingsketch.imagepicker.provider.BaseProvider
    public void onRestoreInstanceState(Bundle savedInstanceState) {
        this.mCameraFile = (File) (savedInstanceState != null ? savedInstanceState.getSerializable(STATE_CAMERA_FILE) : null);
    }

    public final void startIntent() {
        if (!IntentUtils.isCameraAppAvailable(this)) {
            setError(R.string.error_camera_app_not_found);
        } else {
            checkPermission();
        }
    }

    private final void checkPermission() {
        if (isPermissionGranted(this)) {
            startCameraIntent();
        } else {
            requestPermission();
        }
    }

    private final void startCameraIntent() {
        File file = FileUtil.getImageFile$default(FileUtil.INSTANCE, this.mFileDir, null, 2, null);
        this.mCameraFile = file;
        if (file != null && file.exists()) {
            Intent cameraIntent = IntentUtils.getCameraIntent(this, file);
            if (cameraIntent != null) {
                getActivity().startActivityForResult(cameraIntent, CAMERA_INTENT_REQ_CODE);
                return;
            }
            return;
        }
        setError(R.string.error_failed_to_create_camera_image_file);
    }

    private final void requestPermission() {
        ActivityCompat.requestPermissions(getActivity(), getRequiredPermission(getActivity()), PERMISSION_INTENT_REQ_CODE);
    }

    private final boolean isPermissionGranted(Context context) {
        for (String str : getRequiredPermission(context)) {
            if (!PermissionUtil.INSTANCE.isPermissionGranted(context, str)) {
                return false;
            }
        }
        return true;
    }

    private final String[] getRequiredPermission(Context context) {
        String[] strArr = REQUIRED_PERMISSIONS;
        Collection arrayList = new ArrayList();
        for (String str : strArr) {
            if (PermissionUtil.INSTANCE.isPermissionInManifest(context, str)) {
                arrayList.add(str);
            }
        }
        return (String[]) ((List) arrayList).toArray(new String[0]);
    }

    public final void onRequestPermissionsResult(int requestCode) {
        if (requestCode == PERMISSION_INTENT_REQ_CODE) {
            if (isPermissionGranted(this)) {
                startIntent();
                return;
            }
            String error = getString(R.string.permission_camera_denied);
            Intrinsics.checkNotNullExpressionValue(error, "getString(...)");
            setError(error);
        }
    }

    public final void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == CAMERA_INTENT_REQ_CODE) {
            if (resultCode == -1) {
                handleResult();
            } else {
                setResultCancel();
            }
        }
    }

    private final void handleResult() {
        ImagePickerActivity activity = getActivity();
        Uri fromFile = Uri.fromFile(this.mCameraFile);
        Intrinsics.checkNotNullExpressionValue(fromFile, "fromFile(...)");
        activity.setImage(fromFile);
    }

    @Override // com.example.ardrawingsketch.imagepicker.provider.BaseProvider
    protected void onFailure() {
        delete();
    }

    public final void delete() {
        File file = this.mCameraFile;
        if (file != null) {
            file.delete();
        }
        this.mCameraFile = null;
    }
}
