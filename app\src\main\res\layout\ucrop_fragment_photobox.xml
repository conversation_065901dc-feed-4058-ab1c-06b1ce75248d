<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:id="@+id/ucrop_photobox" android:layout_width="match_parent" android:layout_height="match_parent">
    <FrameLayout android:id="@+id/ucrop_frame" android:background="@color/ucrop_color_crop_background" android:layout_width="match_parent" android:layout_height="match_parent" android:layout_marginBottom="-12dp" android:layout_above="@+id/controls_wrapper">
        <ImageView android:layout_gravity="center" android:id="@+id/image_view_logo" android:layout_width="@dimen/ucrop_default_crop_logo_size" android:layout_height="@dimen/ucrop_default_crop_logo_size" app:srcCompat="@drawable/ucrop_vector_ic_crop"/>
        <com.yalantis.ucrop.view.UCropView android:id="@+id/ucrop" android:layout_width="match_parent" android:layout_height="match_parent" android:alpha="0"/>
    </FrameLayout>
    <FrameLayout android:id="@+id/controls_wrapper" android:visibility="gone" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_alignParentBottom="true"/>
</RelativeLayout>
