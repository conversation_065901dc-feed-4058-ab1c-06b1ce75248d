package com.example.ardrawingsketch.activities;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.SystemClock;
import android.view.View;
import android.widget.Toast;
import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.location.LocationRequestCompat;
import androidx.core.view.OnApplyWindowInsetsListener;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import com.example.ardrawingsketch.GlobalConstant;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.Utils;
import com.example.ardrawingsketch.adapter.CategoriesAdapter;
import com.example.ardrawingsketch.databinding.ActivityCategoryBinding;
import com.example.ardrawingsketch.imagepicker.ImagePicker;
import com.example.ardrawingsketch.listener.CategoryListener;
import com.example.ardrawingsketch.model.Category;

/* loaded from: classes6.dex */
public class CategoryActivity extends AppCompatActivity implements CategoryListener {
    String[] PERMISSIONS;
    CategoriesAdapter adapter;
    private ActivityCategoryBinding binding;
    int categoryPosition = 0;
    long mLastClickTime = 0;

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        this.binding = ActivityCategoryBinding.inflate(getLayoutInflater());
        setContentView(this.binding.getRoot());
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), new OnApplyWindowInsetsListener() { // from class: com.example.ardrawingsketch.activities.CategoryActivity$$ExternalSyntheticLambda3
            @Override // androidx.core.view.OnApplyWindowInsetsListener
            public final WindowInsetsCompat onApplyWindowInsets(View view, WindowInsetsCompat windowInsetsCompat) {
                return CategoryActivity.lambda$onCreate$0(view, windowInsetsCompat);
            }
        });
        checkPermission();
        initListener();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    public static /* synthetic */ WindowInsetsCompat lambda$onCreate$0(View v, WindowInsetsCompat insets) {
        Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
        v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
        return insets;
    }

    private void checkPermission() {
        if (Build.VERSION.SDK_INT >= 33) {
            this.PERMISSIONS = new String[]{"android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.READ_MEDIA_IMAGES"};
        } else {
            this.PERMISSIONS = new String[]{"android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"};
        }
        if (!Utils.checkPermission(this, this.PERMISSIONS)) {
            Utils.getPermissions(this, this.PERMISSIONS, GlobalConstant.PERMISSION_CODE);
        }
    }

    private void initListener() {
        this.binding.btnBack.setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.activities.CategoryActivity$$ExternalSyntheticLambda0
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                CategoryActivity.this.m135x8ff5a793(view);
            }
        });
        this.adapter = new CategoriesAdapter(this, this);
        GridLayoutManager gridLayoutManager = new GridLayoutManager((Context) this, 2, 1, false);
        this.binding.sketchListRv.setLayoutManager(gridLayoutManager);
        this.binding.sketchListRv.setAdapter(this.adapter);
        this.binding.selectFromCamera.setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.activities.CategoryActivity$$ExternalSyntheticLambda1
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                CategoryActivity.this.m136x4a6b4814(view);
            }
        });
        this.binding.selectFromGallery.setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.activities.CategoryActivity$$ExternalSyntheticLambda2
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                CategoryActivity.this.m137x4e0e895(view);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$initListener$1$com-example-ardrawsketch-sketch-activities-CategoryActivity  reason: not valid java name */
    public /* synthetic */ void m135x8ff5a793(View v) {
        finish();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$initListener$2$com-example-ardrawsketch-sketch-activities-CategoryActivity  reason: not valid java name */
    public /* synthetic */ void m136x4a6b4814(View view) {
        choiceCamera();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$initListener$3$com-example-ardrawsketch-sketch-activities-CategoryActivity  reason: not valid java name */
    public /* synthetic */ void m137x4e0e895(View view) {
        choiceGallery();
    }

    public void choiceCamera() {
        if (SystemClock.elapsedRealtime() - this.mLastClickTime >= 1000) {
            this.mLastClickTime = SystemClock.elapsedRealtime();
            if (Utils.checkPermission(this, this.PERMISSIONS)) {
                ImagePicker.with(this).cameraOnly().saveDir(getExternalFilesDir(Environment.DIRECTORY_DCIM)).start(103);
            } else {
                Utils.getPermissions(this, this.PERMISSIONS, GlobalConstant.PERMISSION_CODE_CAMERA);
            }
        }
    }

    public void choiceGallery() {
        if (SystemClock.elapsedRealtime() - this.mLastClickTime >= 1000) {
            this.mLastClickTime = SystemClock.elapsedRealtime();
            if (Utils.checkPermission(this, this.PERMISSIONS)) {
                ImagePicker.with(this).galleryOnly().start(LocationRequestCompat.QUALITY_BALANCED_POWER_ACCURACY);
            } else {
                Utils.getPermissions(this, this.PERMISSIONS, GlobalConstant.PERMISSION_CODE_GALLERY);
            }
        }
    }

    private void goToDrawingScreen(String str) {
        Intent intent = new Intent(this, GuideActivity.class);
        intent.putExtra(GlobalConstant.IMAGE_PATH, str);
        intent.putExtra(GlobalConstant.FROM_DRAWABLE, false);
        intent.putExtra(GlobalConstant.FROM_GALLERY, true);
        startActivity(intent);
    }

    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, android.app.Activity
    public void onRequestPermissionsResult(int i, String[] strArr, int[] iArr) {
        super.onRequestPermissionsResult(i, strArr, iArr);
        if (i == 2200) {
            if (Utils.checkPermission(this, this.PERMISSIONS)) {
                ImagePicker.with(this).galleryOnly().start(LocationRequestCompat.QUALITY_BALANCED_POWER_ACCURACY);
            } else {
                Toast.makeText(this, "Please grant permissions to proceed", 0).show();
            }
        } else if (i == 2300) {
            if (Utils.checkPermission(this, this.PERMISSIONS)) {
                ImagePicker.with(this).cameraOnly().saveDir(getExternalFilesDir(Environment.DIRECTORY_DCIM)).start(103);
            } else {
                Toast.makeText(this, "Please grant permissions to proceed", 0).show();
            }
        }
    }

    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, android.app.Activity
    public void onActivityResult(int i, int i2, Intent intent) {
        super.onActivityResult(i, i2, intent);
        if (i2 != -1) {
            return;
        }
        if (i != 102 && i != 103) {
            return;
        }
        if (intent != null) {
            Uri data = intent.getData();
            String path = Utils.getPath(this, data);
            goToDrawingScreen(path);
            return;
        }
        Toast.makeText(this, "Failed to get Image", 0).show();
    }

    @Override // com.example.ardrawingsketch.listener.CategoryListener
    public void onCategoryClick(Category category) {
        if (Utils.checkPermission(this, this.PERMISSIONS)) {
            Intent intent = new Intent(this, DrawingListActivity.class);
            intent.putExtra(GlobalConstant.CATEGORY_CHOICE, category);
            startActivity(intent);
        }
    }
}
