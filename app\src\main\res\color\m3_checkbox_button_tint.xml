<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <item android:state_enabled="false" android:color="?attr/colorOnSurface" android:alpha="@dimen/m3_comp_checkbox_selected_disabled_container_opacity"/>
    <item android:color="?attr/colorError" app:state_error="true"/>
    <item android:color="?attr/colorPrimary" app:state_indeterminate="true"/>
    <item android:state_checked="true" android:color="?attr/colorPrimary"/>
    <item android:state_checked="false" android:color="?attr/colorOnSurfaceVariant"/>
</selector>
