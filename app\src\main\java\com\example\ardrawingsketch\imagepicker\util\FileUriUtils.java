package com.example.ardrawingsketch.imagepicker.util;

import android.content.ContentUris;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.Environment;
import android.provider.DocumentsContract;
import android.provider.MediaStore;
import android.util.Log;
import com.example.ardrawingsketch.GlobalConstant;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

/**
 * فئة مساعدة للتعامل مع URI واستخراج مسارات الملفات الحقيقية
 * توفر وظائف لتحويل URI إلى مسارات ملفات حقيقية يمكن استخدامها
 *
 * تدعم مصادر مختلفة مثل:
 * - التخزين الخارجي
 * - مجلد التنزيلات
 * - مزود الوسائط
 * - خدمات التخزين السحابي (جوجل درايف، صور جوجل)
 *
 * يستخدم نمط Singleton لضمان وجود نسخة واحدة فقط
 */
public final class FileUriUtils {
    /** النسخة الوحيدة من الفئة (Singleton Pattern) */
    public static final FileUriUtils INSTANCE = new FileUriUtils();

    /** علامة التسجيل لهذه الفئة */
    private static final String TAG = "FileUriUtils";

    /**
     * منشئ خاص لمنع إنشاء نسخ متعددة
     */
    private FileUriUtils() {
    }

    /**
     * دالة استخراج المسار الحقيقي للملف من URI
     * تحاول أولاً استخراج المسار من الملفات المحلية، وإذا فشلت تحاول مع الملفات
     * البعيدة
     *
     * @param context سياق التطبيق
     * @param uri     عنوان URI للملف
     * @return المسار الحقيقي للملف أو null إذا فشل الاستخراج
     */
    public final String getRealPath(Context context, Uri uri) {
        // فحص صحة المعاملات المدخلة
        if (context == null) {
            throw new IllegalArgumentException("سياق التطبيق لا يمكن أن يكون null");
        }
        if (uri == null) {
            throw new IllegalArgumentException("عنوان URI لا يمكن أن يكون null");
        }

        Log.d(TAG, "محاولة استخراج المسار من URI: " + uri.toString());

        // محاولة استخراج المسار من الملفات المحلية أولاً
        String localPath = getPathFromLocalUri(context, uri);
        if (localPath != null) {
            Log.d(TAG, "تم استخراج المسار المحلي: " + localPath);
            return localPath;
        }

        // إذا فشلت المحاولة الأولى، محاولة استخراج المسار من الملفات البعيدة
        String remotePath = getPathFromRemoteUri(context, uri);
        if (remotePath != null) {
            Log.d(TAG, "تم استخراج المسار البعيد: " + remotePath);
        } else {
            Log.w(TAG, "فشل في استخراج المسار من URI: " + uri.toString());
        }

        return remotePath;
    }

    private final String getPathFromLocalUri(Context context, Uri uri) {
        Collection emptyList;
        boolean z;
        if (DocumentsContract.isDocumentUri(context, uri)) {
            if (isExternalStorageDocument(uri)) {
                String docId = DocumentsContract.getDocumentId(uri);
                Intrinsics.checkNotNull(docId);
                List split = new Regex(":").split(docId, 0);
                if (!split.isEmpty()) {
                    ListIterator listIterator = split.listIterator(split.size());
                    while (listIterator.hasPrevious()) {
                        if (listIterator.previous().length() == 0) {
                            z = true;
                            continue;
                        } else {
                            z = false;
                            continue;
                        }
                        if (!z) {
                            emptyList = CollectionsKt.take(split, listIterator.nextIndex() + 1);
                            break;
                        }
                    }
                }
                emptyList = CollectionsKt.emptyList();
                String[] split2 = (String[]) emptyList.toArray(new String[0]);
                String type = split2[0];
                if (StringsKt.equals("primary", type, true)) {
                    if (split2.length <= 1) {
                        return Environment.getExternalStorageDirectory() + "/";
                    }
                    return Environment.getExternalStorageDirectory() + "/" + split2[1];
                }
                String path = "storage/" + StringsKt.replace$default(docId, ":", "/", false, 4, (Object) null);
                if (new File(path).exists()) {
                    return path;
                }
                return "/storage/sdcard/" + split2[1];
            } else if (isDownloadsDocument(uri)) {
                return getDownloadDocument(context, uri);
            } else {
                if (isMediaDocument(uri)) {
                    return getMediaDocument(context, uri);
                }
            }
        } else {
            String scheme = uri.getScheme();
            Intrinsics.checkNotNull(scheme);
            if (StringsKt.equals(GlobalConstant.CONTENT, scheme, true)) {
                return isGooglePhotosUri(uri) ? uri.getLastPathSegment() : getDataColumn(context, uri, null, null);
            }
            String scheme2 = uri.getScheme();
            Intrinsics.checkNotNull(scheme2);
            if (StringsKt.equals("file", scheme2, true)) {
                return uri.getPath();
            }
        }
        return null;
    }

    /* JADX WARN: Removed duplicated region for block: B:28:0x004f */
    /*
     * Code decompiled incorrectly, please refer to instructions dump.
     * To view partially-correct code enable 'Show inconsistent code' option in
     * preferences
     */
    private final String getDataColumn(Context r11, Uri r12, String r13, String[] r14) {
        /*
         * r10 = this;
         * r1 = 0
         * java.lang.String r2 = "_data"
         * r0 = 1
         * java.lang.String[] r0 = new java.lang.String[r0]
         * r3 = 0
         * r0[r3] = r2
         * r6 = r0
         * android.content.ContentResolver r4 = r11.getContentResolver() // Catch:
         * java.lang.Throwable -> L35 java.lang.Exception -> L3b
         * kotlin.jvm.internal.Intrinsics.checkNotNull(r12) // Catch:
         * java.lang.Throwable -> L35 java.lang.Exception -> L3b
         * r9 = 0
         * r5 = r12
         * r7 = r13
         * r8 = r14
         * android.database.Cursor r12 = r4.query(r5, r6, r7, r8, r9) // Catch:
         * java.lang.Exception -> L32 java.lang.Throwable -> L4b
         * r1 = r12
         * if (r1 == 0) goto L2f
         * boolean r12 = r1.moveToFirst() // Catch: java.lang.Exception -> L32
         * java.lang.Throwable -> L4b
         * if (r12 == 0) goto L2f
         * int r12 = r1.getColumnIndexOrThrow(r2) // Catch: java.lang.Exception -> L32
         * java.lang.Throwable -> L4b
         * java.lang.String r13 = r1.getString(r12) // Catch: java.lang.Exception -> L32
         * java.lang.Throwable -> L4b
         * r1.close()
         * return r13
         * L2f:
         * if (r1 == 0) goto L48
         * goto L45
         * L32:
         * r0 = move-exception
         * r12 = r0
         * goto L40
         * L35:
         * r0 = move-exception
         * r5 = r12
         * r7 = r13
         * r8 = r14
         * r12 = r0
         * goto L4d
         * L3b:
         * r0 = move-exception
         * r5 = r12
         * r7 = r13
         * r8 = r14
         * r12 = r0
         * L40:
         * r12.printStackTrace() // Catch: java.lang.Throwable -> L4b
         * if (r1 == 0) goto L48
         * L45:
         * r1.close()
         * L48:
         * r12 = 0
         * return r12
         * L4b:
         * r0 = move-exception
         * r12 = r0
         * L4d:
         * if (r1 == 0) goto L52
         * r1.close()
         * L52:
         * throw r12
         */
        throw new UnsupportedOperationException(
                "Method not decompiled: com.example.ardrawingsketch.imagepicker.util.FileUriUtils.getDataColumn(android.content.Context, android.net.Uri, java.lang.String, java.lang.String[]):java.lang.String");
    }

    private final String getDownloadDocument(Context context, Uri uri) {
        String fileName = getFilePath(context, uri);
        if (fileName != null) {
            String path = Environment.getExternalStorageDirectory() + "/Download/" + fileName;
            if (new File(path).exists()) {
                return path;
            }
        }
        String id = DocumentsContract.getDocumentId(uri);
        Intrinsics.checkNotNull(id);
        if (StringsKt.contains$default((CharSequence) id, (CharSequence) ":", false, 2, (Object) null)) {
            Intrinsics.checkNotNull(id);
            id = (String) StringsKt.split$default((CharSequence) id, new String[] { ":" }, false, 0, 6, (Object) null)
                    .get(1);
        }
        Uri parse = Uri.parse("content://downloads/public_downloads");
        Long valueOf = Long.valueOf(id);
        Intrinsics.checkNotNullExpressionValue(valueOf, "valueOf(...)");
        Uri contentUri = ContentUris.withAppendedId(parse, valueOf.longValue());
        Intrinsics.checkNotNullExpressionValue(contentUri, "withAppendedId(...)");
        return getDataColumn(context, contentUri, null, null);
    }

    private final String getMediaDocument(Context context, Uri uri) {
        Collection emptyList;
        boolean z;
        String docId = DocumentsContract.getDocumentId(uri);
        Intrinsics.checkNotNull(docId);
        List split = new Regex(":").split(docId, 0);
        if (!split.isEmpty()) {
            ListIterator listIterator = split.listIterator(split.size());
            while (listIterator.hasPrevious()) {
                if (listIterator.previous().length() == 0) {
                    z = true;
                    continue;
                } else {
                    z = false;
                    continue;
                }
                if (!z) {
                    emptyList = CollectionsKt.take(split, listIterator.nextIndex() + 1);
                    break;
                }
            }
        }
        emptyList = CollectionsKt.emptyList();
        String[] split2 = (String[]) emptyList.toArray(new String[0]);
        String type = split2[0];
        Uri contentUri = null;
        if (Intrinsics.areEqual("image", type)) {
            contentUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
        } else if (Intrinsics.areEqual("video", type)) {
            contentUri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI;
        } else if (Intrinsics.areEqual("audio", type)) {
            contentUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI;
        }
        String[] selectionArgs = { split2[1] };
        return getDataColumn(context, contentUri, "_id=?", selectionArgs);
    }

    private final String getFilePath(Context context, Uri uri) {
        Throwable th;
        Cursor cursor = null;
        String[] projection = { "_display_name" };
        try {
        } catch (Throwable th2) {
            th = th2;
        }
        try {
            cursor = context.getContentResolver().query(uri, projection, null, null, null);
            if (cursor != null && cursor.moveToFirst()) {
                int index = cursor.getColumnIndexOrThrow("_display_name");
                String string = cursor.getString(index);
                cursor.close();
                return string;
            } else if (cursor != null) {
                cursor.close();
                return null;
            } else {
                return null;
            }
        } catch (Throwable th3) {
            th = th3;
            if (cursor != null) {
                cursor.close();
            }
            throw th;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:51:0x0078 */
    /*
     * JADX WARN: Removed duplicated region for block: B:69:? A[RETURN, SYNTHETIC]
     */
    /*
     * Code decompiled incorrectly, please refer to instructions dump.
     * To view partially-correct code enable 'Show inconsistent code' option in
     * preferences
     */
    private final String getPathFromRemoteUri(Context r10, Uri r11) {
        /*
         * r9 = this;
         * r0 = 0
         * r1 = 0
         * r2 = 0
         * r3 = 0
         * r4 = 0
         * com.example.ardrawingsketch.imagepicker.util.FileUtil r5 =
         * com.example.ardrawingsketch.imagepicker.util.FileUtil.INSTANCE // Catch:
         * java.lang.Throwable -> L51 java.io.IOException -> L63
         * java.lang.String r5 = r5.getImageExtension(r11) // Catch: java.lang.Throwable
         * -> L51 java.io.IOException -> L63
         * android.content.ContentResolver r6 = r10.getContentResolver() // Catch:
         * java.lang.Throwable -> L51 java.io.IOException -> L63
         * java.io.InputStream r6 = r6.openInputStream(r11) // Catch:
         * java.lang.Throwable -> L51 java.io.IOException -> L63
         * r1 = r6
         * com.example.ardrawingsketch.imagepicker.util.FileUtil r6 =
         * com.example.ardrawingsketch.imagepicker.util.FileUtil.INSTANCE // Catch:
         * java.lang.Throwable -> L51 java.io.IOException -> L63
         * java.io.File r7 = r10.getCacheDir() // Catch: java.lang.Throwable -> L51
         * java.io.IOException -> L63
         * java.lang.String r8 = "getCacheDir(...)"
         * kotlin.jvm.internal.Intrinsics.checkNotNullExpressionValue(r7, r8) // Catch:
         * java.lang.Throwable -> L51 java.io.IOException -> L63
         * java.io.File r6 = r6.getImageFile(r7, r5) // Catch: java.lang.Throwable ->
         * L51 java.io.IOException -> L63
         * r0 = r6
         * if (r0 != 0) goto L32
         * L28:
         * if (r1 == 0) goto L2f
         * r1.close() // Catch: java.io.IOException -> L2e
         * goto L2f
         * L2e:
         * r6 = move-exception
         * L2f:
         * return r4
         * L32:
         * java.io.FileOutputStream r6 = new java.io.FileOutputStream // Catch:
         * java.lang.Throwable -> L51 java.io.IOException -> L63
         * r6.<init>(r0) // Catch: java.lang.Throwable -> L51 java.io.IOException -> L63
         * java.io.OutputStream r6 = (java.io.OutputStream) r6 // Catch:
         * java.lang.Throwable -> L51 java.io.IOException -> L63
         * r2 = r6
         * if (r1 == 0) goto L42
         * r6 = 4096(0x1000, float:5.74E-42)
         * kotlin.io.ByteStreamsKt.copyTo(r1, r2, r6) // Catch: java.lang.Throwable ->
         * L51 java.io.IOException -> L63
         * r3 = 1
         * L42:
         * if (r1 == 0) goto L4a
         * r1.close() // Catch: java.io.IOException -> L49
         * goto L4a
         * L49:
         * r5 = move-exception
         * L4a:
         * r2.close() // Catch: java.io.IOException -> L4f
         * goto L75
         * L4f:
         * r5 = move-exception
         * goto L74
         * L51:
         * r4 = move-exception
         * if (r1 == 0) goto L59
         * r1.close() // Catch: java.io.IOException -> L58
         * goto L59
         * L58:
         * r5 = move-exception
         * L59:
         * if (r2 == 0) goto L62
         * r2.close() // Catch: java.io.IOException -> L60
         * goto L62
         * L60:
         * r5 = move-exception
         * r3 = 0
         * L62:
         * throw r4
         * L63:
         * r5 = move-exception
         * if (r1 == 0) goto L6c
         * r1.close() // Catch: java.io.IOException -> L6b
         * goto L6c
         * L6b:
         * r5 = move-exception
         * L6c:
         * if (r2 == 0) goto L75
         * r2.close() // Catch: java.io.IOException -> L73
         * goto L75
         * L73:
         * r5 = move-exception
         * L74:
         * r3 = 0
         * L75:
         * if (r3 == 0) goto L7f
         * kotlin.jvm.internal.Intrinsics.checkNotNull(r0)
         * java.lang.String r4 = r0.getPath()
         * L7f:
         * return r4
         */
        throw new UnsupportedOperationException(
                "Method not decompiled: com.example.ardrawingsketch.imagepicker.util.FileUriUtils.getPathFromRemoteUri(android.content.Context, android.net.Uri):java.lang.String");
    }

    private final boolean isExternalStorageDocument(Uri uri) {
        return Intrinsics.areEqual("com.android.externalstorage.documents", uri.getAuthority());
    }

    private final boolean isDownloadsDocument(Uri uri) {
        return Intrinsics.areEqual("com.android.providers.downloads.documents", uri.getAuthority());
    }

    private final boolean isMediaDocument(Uri uri) {
        return Intrinsics.areEqual("com.android.providers.media.documents", uri.getAuthority());
    }

    private final boolean isGooglePhotosUri(Uri uri) {
        return Intrinsics.areEqual("com.google.android.apps.photos.content", uri.getAuthority());
    }
}
