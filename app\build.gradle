// ملف إعدادات البناء للتطبيق - يحتوي على جميع التبعيات والإعدادات المطلوبة
plugins {
    // إضافة البرنامج المساعد لتطبيقات الأندرويد
    id 'com.android.application'
}

// إعدادات الأندرويد الرئيسية
android {
    // إصدار SDK المستخدم للتجميع
    compileSdk 32

    // الإعدادات الافتراضية للتطبيق
    defaultConfig {
        // معرف التطبيق الفريد في متجر جوجل بلاي
        applicationId "com.example.ardrawingsketch"
        // الحد الأدنى لإصدار الأندرويد المدعوم (أندرويد 5.0)
        minSdk 21
        // الإصدار المستهدف (أندرويد 12)
        targetSdk 32
        // رقم إصدار التطبيق (يزيد مع كل تحديث)
        versionCode 1
        // اسم الإصدار المعروض للمستخدمين
        versionName "1.0"

        // أداة تشغيل الاختبارات
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    // أنواع البناء المختلفة
    buildTypes {
        // إعدادات النسخة النهائية للنشر
        release {
            // تعطيل تصغير الكود (يمكن تفعيله لتقليل حجم التطبيق)
            minifyEnabled false
            // ملفات قواعد ProGuard لحماية وتحسين الكود
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    // إعدادات التوافق مع إصدارات جافا
    compileOptions {
        // إصدار جافا المصدر
        sourceCompatibility JavaVersion.VERSION_1_8
        // إصدار جافا المستهدف
        targetCompatibility JavaVersion.VERSION_1_8
    }

    // تفعيل ميزات البناء الإضافية
    buildFeatures {
        // تفعيل View Binding لربط العناصر بسهولة
        viewBinding = true
    }
}

// التبعيات والمكتبات المطلوبة للتطبيق
dependencies {

    // ===== مكتبات الأندرويد الأساسية =====
    // مكتبة الدعم للتوافق مع الإصدارات القديمة
    implementation 'androidx.appcompat:appcompat:1.7.1'
    // مكتبة Material Design للواجهات الحديثة
    implementation 'com.google.android.material:material:1.12.0'
    // مكتبة تخطيط القيود لتصميم الواجهات المرنة
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'

    // ===== مكتبات الاختبار =====
    // مكتبة JUnit للاختبارات الوحدة
    testImplementation 'junit:junit:4.13.2'
    // مكتبة JUnit للاختبارات على الأندرويد
    androidTestImplementation 'androidx.test.ext:junit:1.3.0'
    // مكتبة Espresso لاختبار واجهة المستخدم
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.7.0'

    // ===== مكتبات إضافية للتطبيق =====
    // مكتبة Lottie للرسوم المتحركة المتقدمة
    implementation 'com.airbnb.android:lottie:6.0.0'
    // مكتبة CameraView لإدارة الكاميرا بسهولة
    implementation 'com.otaliastudios:cameraview:2.7.2'
    // مكتبة GPUImage لمعالجة الصور باستخدام GPU
    implementation 'jp.co.cyberagent.android:gpuimage:2.1.0'
}