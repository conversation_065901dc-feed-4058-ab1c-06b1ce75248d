package com.example.ardrawingsketch;

import android.app.Activity;
import android.content.ContentUris;
import android.content.Context;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.DocumentsContract;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

/* loaded from: classes8.dex */
public class Utils {
    public static Bitmap flip(Bitmap bitmap, int i) {
        if (bitmap == null) {
            return bitmap;
        }
        Matrix matrix = new Matrix();
        if (i == 1) {
            matrix.preScale(1.0f, -1.0f);
        } else if (i != 2) {
            return null;
        } else {
            matrix.preScale(-1.0f, 1.0f);
        }
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
    }

    public static Bitmap getBitmapWithTransparentBG(Bitmap bitmap, int i) {
        Bitmap copy = bitmap.copy(Bitmap.Config.ARGB_8888, true);
        int width = copy.getWidth();
        int height = copy.getHeight();
        for (int i2 = 0; i2 < height; i2++) {
            for (int i3 = 0; i3 < width; i3++) {
                if (copy.getPixel(i3, i2) == i) {
                    copy.setPixel(i3, i2, 0);
                }
            }
        }
        return copy;
    }

    public static Bitmap getBitmapFromDrawable(Context context, int drawableResId) {
        return BitmapFactory.decodeResource(context.getResources(), drawableResId);
    }

    public static void getPermissions(Activity context, String[] strArr, int i) {
        ActivityCompat.requestPermissions(context, strArr, i);
    }

    public static Bitmap getBitmap(String str) {
        return BitmapFactory.decodeFile(str, new BitmapFactory.Options());
    }

    public static boolean checkPermission(Context context2, String[] strArr) {
        for (String str : strArr) {
            if (ContextCompat.checkSelfPermission(context2, str) != 0) {
                return false;
            }
        }
        return true;
    }

    public static String getPath(Context context, Uri uri) {
        Uri uri2;
        Uri uri22 = null;
        if (isExternalStorageDocument(uri)) {
            String[] split = DocumentsContract.getDocumentId(uri).split(":");
            String str = split[0];
            String pathFromExtSD = getPathFromExtSD(split);
            if (pathFromExtSD.isEmpty()) {
                return null;
            }
            return pathFromExtSD;
        }
        if (!isDownloadsDocument(uri)) {
            uri2 = uri;
        } else {
            try {
                uri2 = uri;
                try {
                    Cursor query = context.getContentResolver().query(uri2, new String[]{"_display_name"}, null, null, null);
                    if (query != null) {
                        if (query.moveToFirst()) {
                            String str2 = Environment.getExternalStorageDirectory().toString() + "/Download/" + query.getString(0);
                            if (!TextUtils.isEmpty(str2)) {
                                query.close();
                                return str2;
                            }
                        }
                        query.close();
                    }
                    if (query != null) {
                        query.close();
                    }
                    String documentId = DocumentsContract.getDocumentId(uri2);
                    if (!TextUtils.isEmpty(documentId)) {
                        if (documentId.startsWith("raw:")) {
                            return documentId.replaceFirst("raw:", "");
                        }
                        try {
                            return getDataColumn(context, ContentUris.withAppendedId(Uri.parse(new String[]{"content://downloads/public_downloads", "content://downloads/my_downloads"}[0]), Long.parseLong(documentId)), null, null);
                        } catch (NumberFormatException e) {
                            return uri2.getPath().replaceFirst("^/document/raw:", "").replaceFirst("^raw:", "");
                        }
                    }
                } catch (Throwable th) {
                }
            } catch (Throwable th2) {
                uri2 = uri;
            }
        }
        if (isMediaDocument(uri2)) {
            String[] split2 = DocumentsContract.getDocumentId(uri2).split(":");
            String str3 = split2[0];
            if ("image".equals(str3)) {
                uri22 = MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
            } else if ("video".equals(str3)) {
                uri22 = MediaStore.Video.Media.EXTERNAL_CONTENT_URI;
            } else if ("audio".equals(str3)) {
                uri22 = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI;
            }
            return getDataColumn(context, uri22, "_id=?", new String[]{split2[1]});
        } else if (isGoogleDriveUri(uri2)) {
            return getDriveFilePath(context, uri2);
        } else {
            if (isWhatsAppFile(uri2)) {
                return getFilePathForWhatsApp(context, uri2);
            }
            if (GlobalConstant.CONTENT.equalsIgnoreCase(uri2.getScheme())) {
                if (isGooglePhotosUri(uri2)) {
                    return uri2.getLastPathSegment();
                }
                if (isGoogleDriveUri(uri2)) {
                    return getDriveFilePath(context, uri2);
                }
                if (Build.VERSION.SDK_INT >= 29) {
                    return copyFileToInternalStorage(context, uri2, "userfiles");
                }
                return getDataColumn(context, uri2, null, null);
            } else if ("file".equalsIgnoreCase(uri2.getScheme())) {
                return uri2.getPath();
            } else {
                return null;
            }
        }
    }

    private static boolean fileExists(String str) {
        return new File(str).exists();
    }

    private static String getPathFromExtSD(String[] strArr) {
        String str = strArr[0];
        String str2 = "/" + strArr[1];
        if ("primary".equalsIgnoreCase(str)) {
            String str3 = Environment.getExternalStorageDirectory() + str2;
            if (fileExists(str3)) {
                return str3;
            }
        }
        String str4 = System.getenv("SECONDARY_STORAGE") + str2;
        if (fileExists(str4)) {
            return str4;
        }
        String str5 = System.getenv("EXTERNAL_STORAGE") + str2;
        fileExists(str5);
        return str5;
    }

    private static String getDriveFilePath(Context context, Uri uri) {
        Cursor query = context.getContentResolver().query(uri, null, null, null, null);
        int columnIndex = query.getColumnIndex("_display_name");
        int columnIndex2 = query.getColumnIndex("_size");
        query.moveToFirst();
        String string = query.getString(columnIndex);
        Long.toString(query.getLong(columnIndex2));
        File file = new File(context.getCacheDir(), string);
        try {
            InputStream openInputStream = context.getContentResolver().openInputStream(uri);
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            byte[] bArr = new byte[Math.min(openInputStream.available(), 1048576)];
            while (true) {
                int read = openInputStream.read(bArr);
                if (read == -1) {
                    break;
                }
                fileOutputStream.write(bArr, 0, read);
            }
            Log.e("File Size", "Size " + file.length());
            openInputStream.close();
            fileOutputStream.close();
            Log.e("File Path", "Path " + file.getPath());
            Log.e("File Size", "Size " + file.length());
        } catch (Exception e) {
            Log.e("Exception", e.getMessage());
        }
        return file.getPath();
    }

    private static String copyFileToInternalStorage(Context context, Uri uri, String str) {
        File file;
        Cursor query = context.getContentResolver().query(uri, new String[]{"_display_name", "_size"}, null, null, null);
        int columnIndex = query.getColumnIndex("_display_name");
        int columnIndex2 = query.getColumnIndex("_size");
        query.moveToFirst();
        String string = query.getString(columnIndex);
        Long.toString(query.getLong(columnIndex2));
        if (!str.equals("")) {
            File file2 = new File(context.getFilesDir() + "/" + str);
            if (!file2.exists()) {
                file2.mkdir();
            }
            file = new File(context.getFilesDir() + "/" + str + "/" + string);
        } else {
            file = new File(context.getFilesDir() + "/" + string);
        }
        try {
            InputStream openInputStream = context.getContentResolver().openInputStream(uri);
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            byte[] bArr = new byte[1024];
            while (true) {
                int read = openInputStream.read(bArr);
                if (read == -1) {
                    break;
                }
                fileOutputStream.write(bArr, 0, read);
            }
            openInputStream.close();
            fileOutputStream.close();
        } catch (Exception e) {
            Log.e("Exception", e.getMessage());
        }
        return file.getPath();
    }

    private static String getFilePathForWhatsApp(Context context, Uri uri) {
        return copyFileToInternalStorage(context, uri, "whatsapp");
    }

    private static String getDataColumn(Context context2, Uri uri, String str, String[] strArr) {
        try {
            try {
                Cursor query = context2.getContentResolver().query(uri, new String[]{"_data"}, str, strArr, null);
                if (query != null) {
                    if (query.moveToFirst()) {
                        String string = query.getString(query.getColumnIndexOrThrow("_data"));
                        if (query != null) {
                            query.close();
                        }
                        return string;
                    } else if (query != null) {
                        query.close();
                    }
                }
                if (query == null) {
                    return null;
                }
                query.close();
                return null;
            } catch (Throwable th) {
                return str;
            }
        } catch (Throwable th2) {
            return str;
        }
    }

    private static boolean isExternalStorageDocument(Uri uri) {
        return "com.android.externalstorage.documents".equals(uri.getAuthority());
    }

    private static boolean isDownloadsDocument(Uri uri) {
        return "com.android.providers.downloads.documents".equals(uri.getAuthority());
    }

    private static boolean isMediaDocument(Uri uri) {
        return "com.android.providers.media.documents".equals(uri.getAuthority());
    }

    private static boolean isGooglePhotosUri(Uri uri) {
        return "com.google.android.apps.photos.content".equals(uri.getAuthority());
    }

    public static boolean isWhatsAppFile(Uri uri) {
        return "com.whatsapp.provider.media".equals(uri.getAuthority());
    }

    private static boolean isGoogleDriveUri(Uri uri) {
        return "com.google.android.apps.docs.storage".equals(uri.getAuthority()) || "com.google.android.apps.docs.storage.legacy".equals(uri.getAuthority());
    }
}
