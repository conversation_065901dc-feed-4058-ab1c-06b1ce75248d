package com.example.ardrawingsketch.imagepicker.provider;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.imagepicker.ImagePicker;
import com.example.ardrawingsketch.imagepicker.ImagePickerActivity;
import com.example.ardrawingsketch.imagepicker.util.IntentUtils;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;


public final class GalleryProvider extends BaseProvider {
    public static final Companion Companion = new Companion(null);
    private static final int GALLERY_INTENT_REQ_CODE = 4261;
    private final String[] mimeTypes;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public GalleryProvider(ImagePickerActivity activity) {
        super(activity);
        Intrinsics.checkNotNullParameter(activity, "activity");
        Bundle bundle = activity.getIntent().getExtras();
        String[] stringArray = (bundle == null ? new Bundle() : bundle).getStringArray(ImagePicker.EXTRA_MIME_TYPES);
        this.mimeTypes = stringArray == null ? new String[0] : stringArray;
    }


    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        private Companion() {
        }
    }

    public final void startIntent() {
        startGalleryIntent();
    }

    private final void startGalleryIntent() {
        Intent galleryIntent = IntentUtils.getGalleryIntent(getActivity(), this.mimeTypes);
        getActivity().startActivityForResult(galleryIntent, GALLERY_INTENT_REQ_CODE);
    }

    public final void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == GALLERY_INTENT_REQ_CODE) {
            if (resultCode == -1) {
                handleResult(data);
            } else {
                setResultCancel();
            }
        }
    }

    private final void handleResult(Intent data) {
        Uri uri = data != null ? data.getData() : null;
        if (uri != null) {
            takePersistableUriPermission(uri);
            getActivity().setImage(uri);
            return;
        }
        setError(R.string.error_failed_pick_gallery_image);
    }

    private final void takePersistableUriPermission(Uri uri) {
        getContentResolver().takePersistableUriPermission(uri, Intent.FLAG_GRANT_READ_URI_PERMISSION);
    }
}
