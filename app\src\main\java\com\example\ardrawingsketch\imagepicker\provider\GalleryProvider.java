package com.example.ardrawingsketch.imagepicker.provider;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.imagepicker.ImagePicker;
import com.example.ardrawingsketch.imagepicker.ImagePickerActivity;
import com.example.ardrawingsketch.imagepicker.util.IntentUtils;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: GalleryProvider.kt */
@Metadata(d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u0000 \u00172\u00020\u0001:\u0001\u0017B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003¢\u0006\u0004\b\u0004\u0010\u0005J\u0006\u0010\n\u001a\u00020\u000bJ\b\u0010\f\u001a\u00020\u000bH\u0002J \u0010\r\u001a\u00020\u000b2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000f2\b\u0010\u0011\u001a\u0004\u0018\u00010\u0012J\u0012\u0010\u0013\u001a\u00020\u000b2\b\u0010\u0011\u001a\u0004\u0018\u00010\u0012H\u0002J\u0010\u0010\u0014\u001a\u00020\u000b2\u0006\u0010\u0015\u001a\u00020\u0016H\u0002R\u0016\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007X\u0082\u0004¢\u0006\u0004\n\u0002\u0010\t¨\u0006\u0018"}, d2 = {"Lcom/example/ardrawsketch/sketch/imagepicker/provider/GalleryProvider;", "Lcom/example/ardrawsketch/sketch/imagepicker/provider/BaseProvider;", "activity", "Lcom/example/ardrawsketch/sketch/imagepicker/ImagePickerActivity;", "<init>", "(Lcom/example/ardrawsketch/sketch/imagepicker/ImagePickerActivity;)V", "mimeTypes", "", "", "[Ljava/lang/String;", "startIntent", "", "startGalleryIntent", "onActivityResult", "requestCode", "", "resultCode", "data", "Landroid/content/Intent;", "handleResult", "takePersistableUriPermission", "uri", "Landroid/net/Uri;", "Companion", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes7.dex */
public final class GalleryProvider extends BaseProvider {
    public static final Companion Companion = new Companion(null);
    private static final int GALLERY_INTENT_REQ_CODE = 4261;
    private final String[] mimeTypes;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public GalleryProvider(ImagePickerActivity activity) {
        super(activity);
        Intrinsics.checkNotNullParameter(activity, "activity");
        Bundle bundle = activity.getIntent().getExtras();
        String[] stringArray = (bundle == null ? new Bundle() : bundle).getStringArray(ImagePicker.EXTRA_MIME_TYPES);
        this.mimeTypes = stringArray == null ? new String[0] : stringArray;
    }

    /* compiled from: GalleryProvider.kt */
    @Metadata(d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082T¢\u0006\u0002\n\u0000¨\u0006\u0006"}, d2 = {"Lcom/example/ardrawsketch/sketch/imagepicker/provider/GalleryProvider$Companion;", "", "<init>", "()V", "GALLERY_INTENT_REQ_CODE", "", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* loaded from: classes7.dex */
    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        private Companion() {
        }
    }

    public final void startIntent() {
        startGalleryIntent();
    }

    private final void startGalleryIntent() {
        Intent galleryIntent = IntentUtils.getGalleryIntent(getActivity(), this.mimeTypes);
        getActivity().startActivityForResult(galleryIntent, GALLERY_INTENT_REQ_CODE);
    }

    public final void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == GALLERY_INTENT_REQ_CODE) {
            if (resultCode == -1) {
                handleResult(data);
            } else {
                setResultCancel();
            }
        }
    }

    private final void handleResult(Intent data) {
        Uri uri = data != null ? data.getData() : null;
        if (uri != null) {
            takePersistableUriPermission(uri);
            getActivity().setImage(uri);
            return;
        }
        setError(R.string.error_failed_pick_gallery_image);
    }

    private final void takePersistableUriPermission(Uri uri) {
        getContentResolver().takePersistableUriPermission(uri, 1);
    }
}
