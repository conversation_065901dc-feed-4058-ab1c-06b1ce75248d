package com.example.ardrawingsketch.activities;

import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.SystemClock;
import android.view.View;
import android.widget.Toast;
import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.location.LocationRequestCompat;
import androidx.core.view.OnApplyWindowInsetsListener;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.example.ardrawingsketch.databinding.ActivityMainBinding;
import com.example.ardrawingsketch.GlobalConstant;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.Utils;
import com.example.ardrawingsketch.imagepicker.ImagePicker;

/**
 * نشاط الشاشة الرئيسية للتطبيق
 * يحتوي على القوائم الرئيسية للتطبيق:
 * - زر الكاميرا: لالتقاط صورة جديدة
 * - زر الرسم: لاختيار فئة رسم
 * - زر المعرض: لاختيار صورة من المعرض
 *
 * يتعامل مع طلب الصلاحيات وإدارة النقرات على الأزرار
 */
public class MainActivity extends AppCompatActivity {
    // ===== متغيرات الفئة =====
    /** مصفوفة الصلاحيات المطلوبة للتطبيق */
    private String[] PERMISSIONS;
    /** ربط عناصر واجهة المستخدم */
    private ActivityMainBinding binding;
    /** زمن آخر نقرة لمنع النقرات المتعددة السريعة */
    private long mLastClickTime = 0;

    /**
     * دالة إنشاء النشاط - تُستدعى عند بدء النشاط
     * تقوم بتهيئة واجهة المستخدم، فحص الصلاحيات، وإعداد مستمعات الأحداث
     *
     * @param savedInstanceState حالة النشاط المحفوظة من جلسة سابقة
     */
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // تفعيل وضع العرض من حافة إلى حافة
        EdgeToEdge.enable(this);

        // ربط عناصر واجهة المستخدم باستخدام View Binding
        this.binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(this.binding.getRoot());

        // إعداد معالج حواف النظام (شريط الحالة، شريط التنقل)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), new OnApplyWindowInsetsListener() {
            @Override
            public WindowInsetsCompat onApplyWindowInsets(View view, WindowInsetsCompat insets) {
                return MainActivity.lambda$onCreate$0(view, insets);
            }
        });

        // فحص وطلب الصلاحيات المطلوبة
        checkPermission();

        // إعداد مستمعات النقر على الأزرار
        initListener();
    }

    /**
     * دالة معالجة حواف النظام
     * تقوم بضبط الحواف لتجنب تداخل المحتوى مع شريط الحالة وشريط التنقل
     *
     * @param v      العنصر المراد ضبط حوافه
     * @param insets معلومات حواف النظام
     * @return معلومات الحواف بعد المعالجة
     */
    public static WindowInsetsCompat lambda$onCreate$0(View v, WindowInsetsCompat insets) {
        // استخراج معلومات حواف النظام (شريط الحالة، شريط التنقل)
        Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
        // ضبط الحواف لتجنب التداخل
        v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
        return insets;
    }

    /**
     * دالة فحص وطلب الصلاحيات
     * تحدد الصلاحيات المطلوبة حسب إصدار الأندرويد وتطلبها إذا لم تكن ممنوحة
     * للأجهزة الحديثة (أندرويد 13+): تطلب صلاحية قراءة الصور
     * للأجهزة القديمة: تطلب صلاحيات التخزين الخارجي
     */
    private void checkPermission() {
        // تحديد الصلاحيات حسب إصدار الأندرويد
        if (Build.VERSION.SDK_INT >= 33) {
            // للأجهزة الحديثة (أندرويد 13 فما فوق)
            this.PERMISSIONS = new String[] {
                    "android.permission.CAMERA", // صلاحية استخدام الكاميرا
                    "android.permission.RECORD_AUDIO", // صلاحية تسجيل الصوت
                    "android.permission.READ_MEDIA_IMAGES" // صلاحية قراءة الصور
            };
        } else {
            // للأجهزة القديمة (أندرويد 12 فما دون)
            this.PERMISSIONS = new String[] {
                    "android.permission.CAMERA", // صلاحية استخدام الكاميرا
                    "android.permission.READ_EXTERNAL_STORAGE", // صلاحية قراءة التخزين الخارجي
                    "android.permission.WRITE_EXTERNAL_STORAGE" // صلاحية الكتابة في التخزين الخارجي
            };
        }

        // فحص إذا كانت الصلاحيات ممنوحة، وإذا لم تكن فاطلبها
        if (!Utils.checkPermission(this, this.PERMISSIONS)) {
            Utils.getPermissions(this, this.PERMISSIONS, GlobalConstant.PERMISSION_CODE);
        }
    }

    /**
     * دالة إعداد مستمعات النقر
     * تربط الأزرار بالدوال المناسبة للتعامل مع نقرات المستخدم
     */
    private void initListener() {
        // مستمع النقر على زر الكاميرا
        this.binding.btnCamera.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // استدعاء دالة فتح الكاميرا
                onCameraButtonClick(view);
            }
        });

        // مستمع النقر على زر الرسم
        this.binding.btnDraw.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // استدعاء دالة فتح شاشة الفئات
                onDrawButtonClick(view);
            }
        });

        // مستمع النقر على زر المعرض
        this.binding.btnGallery.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // استدعاء دالة فتح المعرض
                onGalleryButtonClick(view);
            }
        });
    }

    /**
     * دالة معالجة النقر على زر الكاميرا
     * تفتح الكاميرا لالتقاط صورة جديدة
     *
     * @param v العنصر الذي تم النقر عليه
     */
    public void onCameraButtonClick(View v) {
        choiceCamera();
    }

    /**
     * دالة معالجة النقر على زر الرسم
     * تفتح شاشة اختيار فئات الرسم
     *
     * @param v العنصر الذي تم النقر عليه
     */
    public void onDrawButtonClick(View v) {
        // فتح نشاط اختيار الفئات
        startActivity(new Intent(this, CategoryActivity.class));
    }

    /**
     * دالة معالجة النقر على زر المعرض
     * تفتح معرض الصور لاختيار صورة موجودة
     *
     * @param v العنصر الذي تم النقر عليه
     */
    public void onGalleryButtonClick(View v) {
        choiceGallery();
    }

    /**
     * دالة فتح معرض الصور
     * تتحقق من الصلاحيات وتفتح معرض الصور لاختيار صورة
     * تتضمن حماية من النقرات المتعددة السريعة
     */
    public void choiceGallery() {
        // حماية من النقرات المتعددة السريعة (ثانية واحدة بين النقرات)
        if (SystemClock.elapsedRealtime() - this.mLastClickTime >= 1000) {
            this.mLastClickTime = SystemClock.elapsedRealtime();

            // فحص إذا كانت الصلاحيات ممنوحة
            if (Utils.checkPermission(this, this.PERMISSIONS)) {
                // فتح معرض الصور فقط (بدون الكاميرا)
                ImagePicker.with(this).galleryOnly().start(LocationRequestCompat.QUALITY_BALANCED_POWER_ACCURACY);
            } else {
                // طلب الصلاحيات إذا لم تكن ممنوحة
                Utils.getPermissions(this, this.PERMISSIONS, GlobalConstant.PERMISSION_CODE_GALLERY);
            }
        }
    }

    /**
     * دالة الانتقال إلى شاشة الرسم
     * تفتح نشاط الدليل مع تمرير مسار الصورة المختارة
     *
     * @param imagePath مسار الصورة المختارة من المعرض
     */
    private void goToDrawingScreen(String imagePath) {
        Intent intent = new Intent(this, GuideActivity.class);
        // تمرير مسار الصورة
        intent.putExtra(GlobalConstant.IMAGE_PATH, imagePath);
        // تحديد أن الصورة من المعرض
        intent.putExtra(GlobalConstant.FROM_GALLERY, true);
        startActivity(intent);
    }

    public void choiceCamera() {
        if (SystemClock.elapsedRealtime() - this.mLastClickTime >= 1000) {
            this.mLastClickTime = SystemClock.elapsedRealtime();
            if (Utils.checkPermission(this, this.PERMISSIONS)) {
                ImagePicker.with(this).cameraOnly().saveDir(getExternalFilesDir(Environment.DIRECTORY_DCIM)).start(103);
            } else {
                Utils.getPermissions(this, this.PERMISSIONS, GlobalConstant.PERMISSION_CODE_CAMERA);
            }
        }
    }

    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity,
              // android.app.Activity
    public void onRequestPermissionsResult(int i, String[] strArr, int[] iArr) {
        super.onRequestPermissionsResult(i, strArr, iArr);
        if (i == 2200) {
            if (Utils.checkPermission(this, this.PERMISSIONS)) {
                ImagePicker.with(this).galleryOnly().start(LocationRequestCompat.QUALITY_BALANCED_POWER_ACCURACY);
            } else {
                Toast.makeText(this, "Please grant permissions to proceed", Toast.LENGTH_LONG).show();
            }
        } else if (i == 2300) {
            if (Utils.checkPermission(this, this.PERMISSIONS)) {
                ImagePicker.with(this).cameraOnly().saveDir(getExternalFilesDir(Environment.DIRECTORY_DCIM)).start(103);
            } else {
                Toast.makeText(this, "Please grant permissions to proceed", Toast.LENGTH_LONG).show();
            }
        }
    }

    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity,
              // android.app.Activity
    public void onActivityResult(int i, int i2, Intent intent) {
        super.onActivityResult(i, i2, intent);
        if (i2 != -1) {
            return;
        }
        if (i != 102 && i != 103) {
            return;
        }
        if (intent != null) {
            Uri data = intent.getData();
            String path = Utils.getPath(this, data);
            goToDrawingScreen(path);
            return;
        }
        Toast.makeText(this, "Failed to get Image", Toast.LENGTH_LONG).show();
    }
}
