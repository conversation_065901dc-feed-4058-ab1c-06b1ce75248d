<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true" android:color="?attr/colorOnPrimary" android:alpha="@dimen/mtrl_high_ripple_pressed_alpha"/>
    <item android:state_focused="true" android:color="?attr/colorOnPrimary" android:alpha="@dimen/mtrl_high_ripple_focused_alpha" android:state_hovered="true"/>
    <item android:state_focused="true" android:color="?attr/colorOnPrimary" android:alpha="@dimen/mtrl_high_ripple_focused_alpha"/>
    <item android:color="?attr/colorOnPrimary" android:alpha="@dimen/mtrl_high_ripple_hovered_alpha" android:state_hovered="true"/>
    <item android:color="?attr/colorOnPrimary" android:alpha="@dimen/mtrl_high_ripple_default_alpha"/>
</selector>
