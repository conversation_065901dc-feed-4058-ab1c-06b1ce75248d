<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" android:orientation="@integer/mtrl_calendar_header_orientation" android:layout_width="wrap_content" android:layout_height="match_parent">
    <include layout="@layout/mtrl_picker_header_dialog"/>
    <LinearLayout android:orientation="vertical" android:id="@+id/mtrl_calendar_main_pane" android:layout_width="wrap_content" android:layout_height="match_parent">
        <androidx.fragment.app.FragmentContainerView android:id="@+id/mtrl_calendar_frame" android:paddingLeft="@dimen/mtrl_calendar_content_padding" android:paddingRight="@dimen/mtrl_calendar_content_padding" android:paddingBottom="@dimen/mtrl_calendar_bottom_padding" android:layout_width="match_parent" android:layout_height="0dp" android:layout_weight="1" android:paddingStart="@dimen/mtrl_calendar_content_padding" android:paddingEnd="@dimen/mtrl_calendar_content_padding"/>
        <include android:layout_width="match_parent" android:layout_height="@dimen/mtrl_calendar_action_height" layout="@layout/mtrl_picker_actions"/>
    </LinearLayout>
</LinearLayout>
