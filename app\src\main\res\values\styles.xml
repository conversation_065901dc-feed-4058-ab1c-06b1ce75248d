<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="AlertDialog.AppCompat" parent="@style/Base.AlertDialog.AppCompat">
    </style>
    <style name="AlertDialog.AppCompat.Light" parent="@style/Base.AlertDialog.AppCompat.Light">
    </style>
    <style name="Animation.AppCompat.Dialog" parent="@style/Base.Animation.AppCompat.Dialog">
    </style>
    <style name="Animation.AppCompat.DropDownUp" parent="@style/Base.Animation.AppCompat.DropDownUp">
    </style>
    <style name="Animation.AppCompat.Tooltip" parent="@style/Base.Animation.AppCompat.Tooltip">
    </style>
    <style name="Animation.Design.BottomSheetDialog" parent="@style/Animation.AppCompat.Dialog">
        <item name="android:windowEnterAnimation">@anim/design_bottom_sheet_slide_in</item>
        <item name="android:windowExitAnimation">@anim/design_bottom_sheet_slide_out</item>
    </style>
    <style name="Animation.Material3.BottomSheetDialog" parent="@style/Animation.AppCompat.Dialog">
        <item name="android:windowEnterAnimation">@anim/m3_bottom_sheet_slide_in</item>
        <item name="android:windowExitAnimation">@anim/m3_bottom_sheet_slide_out</item>
    </style>
    <style name="Animation.Material3.SideSheetDialog" parent="@style/Animation.AppCompat.Dialog">
        <item name="android:windowEnterAnimation">@anim/m3_side_sheet_enter_from_right</item>
        <item name="android:windowExitAnimation">@anim/m3_side_sheet_exit_to_right</item>
    </style>
    <style name="Animation.Material3.SideSheetDialog.Left" parent="@style/Animation.Material3.SideSheetDialog">
        <item name="android:windowEnterAnimation">@anim/m3_side_sheet_enter_from_left</item>
        <item name="android:windowExitAnimation">@anim/m3_side_sheet_exit_to_left</item>
    </style>
    <style name="Animation.Material3.SideSheetDialog.Right" parent="@style/Animation.Material3.SideSheetDialog">
    </style>
    <style name="Animation.MaterialComponents.BottomSheetDialog" parent="@style/Animation.AppCompat.Dialog">
        <item name="android:windowEnterAnimation">@anim/mtrl_bottom_sheet_slide_in</item>
        <item name="android:windowExitAnimation">@anim/mtrl_bottom_sheet_slide_out</item>
    </style>
    <style name="Base.AlertDialog.AppCompat" parent="@android:style/Widget">
        <item name="android:layout">@layout/abc_alert_dialog_material</item>
        <item name="buttonIconDimen">@dimen/abc_alert_dialog_button_dimen</item>
        <item name="listItemLayout">@layout/select_dialog_item_material</item>
        <item name="listLayout">@layout/abc_select_dialog_material</item>
        <item name="multiChoiceItemLayout">@layout/select_dialog_multichoice_material</item>
        <item name="singleChoiceItemLayout">@layout/select_dialog_singlechoice_material</item>
    </style>
    <style name="Base.AlertDialog.AppCompat.Light" parent="@style/Base.AlertDialog.AppCompat">
    </style>
    <style name="Base.Animation.AppCompat.Dialog" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_popup_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_popup_exit</item>
    </style>
    <style name="Base.Animation.AppCompat.DropDownUp" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_grow_fade_in_from_bottom</item>
        <item name="android:windowExitAnimation">@anim/abc_shrink_fade_out_from_bottom</item>
    </style>
    <style name="Base.Animation.AppCompat.Tooltip" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_tooltip_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_tooltip_exit</item>
    </style>
    <style name="Base.CardView" parent="@android:style/Widget">
        <item name="cardCornerRadius">@dimen/cardview_default_radius</item>
        <item name="cardElevation">@dimen/cardview_default_elevation</item>
        <item name="cardMaxElevation">@dimen/cardview_default_elevation</item>
        <item name="cardPreventCornerOverlap">true</item>
        <item name="cardUseCompatPadding">false</item>
    </style>
    <style name="Base.DialogWindowTitle.AppCompat" parent="@android:style/Widget">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Title</item>
        <item name="android:maxLines">1</item>
        <item name="android:scrollHorizontally">true</item>
    </style>
    <style name="Base.DialogWindowTitleBackground.AppCompat" parent="@android:style/Widget">
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">?attr/dialogPreferredPadding</item>
        <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
        <item name="android:paddingRight">?attr/dialogPreferredPadding</item>
    </style>
    <style name="Base.MaterialAlertDialog.MaterialComponents.Title.Icon" parent="@android:style/Widget">
        <item name="android:layout_width">32dp</item>
        <item name="android:layout_height">32dp</item>
        <item name="android:src">@null</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:contentDescription">@null</item>
        <item name="android:importantForAccessibility">no</item>
    </style>
    <style name="Base.MaterialAlertDialog.MaterialComponents.Title.Panel" parent="@android:style/Widget">
        <item name="android:paddingLeft">?attr/dialogPreferredPadding</item>
        <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
        <item name="android:paddingRight">?attr/dialogPreferredPadding</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
    </style>
    <style name="Base.MaterialAlertDialog.MaterialComponents.Title.Text" parent="@style/RtlOverlay.DialogWindowTitle.AppCompat">
        <item name="android:textAppearance">?attr/textAppearanceSubtitle1</item>
        <item name="android:textColor">@color/material_on_surface_emphasis_high_type</item>
        <item name="android:ellipsize">end</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:singleLine">true</item>
    </style>
    <style name="Base.TextAppearance.AppCompat" parent="@android:style/TextAppearance.Material">
    </style>
    <style name="Base.TextAppearance.AppCompat.Body1" parent="@android:style/TextAppearance.Material.Body1">
    </style>
    <style name="Base.TextAppearance.AppCompat.Body2" parent="@android:style/TextAppearance.Material.Body2">
    </style>
    <style name="Base.TextAppearance.AppCompat.Button" parent="@android:style/TextAppearance.Material.Button">
    </style>
    <style name="Base.TextAppearance.AppCompat.Caption" parent="@android:style/TextAppearance.Material.Caption">
    </style>
    <style name="Base.TextAppearance.AppCompat.Display1" parent="@android:style/TextAppearance.Material.Display1">
    </style>
    <style name="Base.TextAppearance.AppCompat.Display2" parent="@android:style/TextAppearance.Material.Display2">
    </style>
    <style name="Base.TextAppearance.AppCompat.Display3" parent="@android:style/TextAppearance.Material.Display3">
    </style>
    <style name="Base.TextAppearance.AppCompat.Display4" parent="@android:style/TextAppearance.Material.Display4">
    </style>
    <style name="Base.TextAppearance.AppCompat.Headline" parent="@android:style/TextAppearance.Material.Headline">
    </style>
    <style name="Base.TextAppearance.AppCompat.Inverse" parent="@android:style/TextAppearance.Material.Inverse">
    </style>
    <style name="Base.TextAppearance.AppCompat.Large" parent="@android:style/TextAppearance.Material.Large">
    </style>
    <style name="Base.TextAppearance.AppCompat.Large.Inverse" parent="@android:style/TextAppearance.Material.Large.Inverse">
    </style>
    <style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Large">
    </style>
    <style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Small">
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium" parent="@android:style/TextAppearance.Material.Medium">
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium.Inverse" parent="@android:style/TextAppearance.Material.Medium.Inverse">
    </style>
    <style name="Base.TextAppearance.AppCompat.Menu" parent="@android:style/TextAppearance.Material.Menu">
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult" parent="">
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textColorHint">?android:attr/textColorHint</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" parent="@android:style/TextAppearance.Material.SearchResult.Subtitle">
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Title" parent="@android:style/TextAppearance.Material.SearchResult.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Small" parent="@android:style/TextAppearance.Material.Small">
    </style>
    <style name="Base.TextAppearance.AppCompat.Small.Inverse" parent="@android:style/TextAppearance.Material.Small.Inverse">
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead" parent="@android:style/TextAppearance.Material.Subhead">
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead.Inverse" parent="@style/Base.TextAppearance.AppCompat.Subhead">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title" parent="@android:style/TextAppearance.Material.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Title">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Tooltip" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">14sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Menu">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Subtitle">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Subtitle.Inverse">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Title.Inverse">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="@android:style/TextAppearance.Material.Widget.ActionMode.Subtitle">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="@android:style/TextAppearance.Material.Widget.ActionMode.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button" parent="@android:style/TextAppearance.Material.Widget.Button">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="@android:style/TextAppearance.Material.Widget.Button.Borderless.Colored">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Colored" parent="@android:style/TextAppearance.Material.Widget.Button.Colored">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" parent="@android:style/TextAppearance.Material.Widget.Button.Inverse">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.DropDownItem" parent="@android:style/TextAppearance.Small">
        <item name="android:textColor">?android:attr/textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_menu_header_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Large">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Small">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="@android:style/TextAppearance.Material.Button">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="@android:style/TextAppearance.Material.Widget.TextView.SpinnerItem">
    </style>
    <style name="Base.TextAppearance.Material3.Search" parent="">
        <item name="android:textSize">@dimen/m3_searchbar_text_size</item>
    </style>
    <style name="Base.TextAppearance.MaterialComponents.Badge" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/mtrl_badge_text_size</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?attr/colorOnError</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.0893</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.MaterialComponents.Button" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.0893</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.MaterialComponents.Headline6" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.0125</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.MaterialComponents.Subtitle2" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.0071</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="@android:style/TextAppearance.Medium">
        <item name="android:textColor">?android:attr/textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Subtitle">
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Title">
    </style>
    <style name="Base.Theme.ARDrawSketch" parent="@style/Theme.Material3.DayNight.NoActionBar">
    </style>
    <style name="Base.Theme.AppCompat" parent="@style/Base.V23.Theme.AppCompat">
    </style>
    <style name="Base.Theme.AppCompat.CompactMenu" parent="">
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
        <item name="android:itemTextAppearance">?android:attr/textAppearanceMedium</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog" parent="@style/Base.V21.Theme.AppCompat.Dialog">
    </style>
    <style name="Base.Theme.AppCompat.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.FixedSize" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.DialogWhenLarge" parent="@style/Theme.AppCompat">
    </style>
    <style name="Base.Theme.AppCompat.Light" parent="@style/Base.V23.Theme.AppCompat.Light">
    </style>
    <style name="Base.Theme.AppCompat.Light.DarkActionBar" parent="@style/Base.Theme.AppCompat.Light">
        <item name="actionBarPopupTheme">@style/ThemeOverlay.AppCompat.Light</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.Dark.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionModeTheme">?attr/actionBarTheme</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog" parent="@style/Base.V21.Theme.AppCompat.Light.Dialog">
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.FixedSize" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.DialogWhenLarge" parent="@style/Theme.AppCompat.Light">
    </style>
    <style name="Base.Theme.Material3.Dark" parent="@style/Base.V24.Theme.Material3.Dark">
    </style>
    <style name="Base.Theme.Material3.Dark.BottomSheetDialog" parent="@style/Base.V14.Theme.Material3.Dark.BottomSheetDialog">
    </style>
    <style name="Base.Theme.Material3.Dark.Dialog" parent="@style/Base.V24.Theme.Material3.Dark.Dialog">
    </style>
    <style name="Base.Theme.Material3.Dark.Dialog.FixedSize" parent="@style/Base.Theme.Material3.Dark.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.Material3.Dark.DialogWhenLarge" parent="@style/Base.Theme.Material3.Dark">
    </style>
    <style name="Base.Theme.Material3.Dark.SideSheetDialog" parent="@style/Base.V14.Theme.Material3.Dark.SideSheetDialog">
    </style>
    <style name="Base.Theme.Material3.Light" parent="@style/Base.V24.Theme.Material3.Light">
    </style>
    <style name="Base.Theme.Material3.Light.BottomSheetDialog" parent="@style/Base.V14.Theme.Material3.Light.BottomSheetDialog">
    </style>
    <style name="Base.Theme.Material3.Light.Dialog" parent="@style/Base.V24.Theme.Material3.Light.Dialog">
    </style>
    <style name="Base.Theme.Material3.Light.Dialog.FixedSize" parent="@style/Base.Theme.Material3.Light.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.Material3.Light.DialogWhenLarge" parent="@style/Base.Theme.Material3.Light">
    </style>
    <style name="Base.Theme.Material3.Light.SideSheetDialog" parent="@style/Base.V14.Theme.Material3.Light.SideSheetDialog">
    </style>
    <style name="Base.Theme.MaterialComponents" parent="@style/Base.V21.Theme.MaterialComponents">
    </style>
    <style name="Base.Theme.MaterialComponents.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Bridge">
    </style>
    <style name="Base.Theme.MaterialComponents.CompactMenu" parent="">
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
        <item name="android:itemTextAppearance">?android:attr/textAppearanceMedium</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog" parent="@style/Base.V21.Theme.MaterialComponents.Dialog">
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Dialog.Bridge">
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog.FixedSize" parent="@style/Base.Theme.MaterialComponents.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.DialogWhenLarge" parent="@style/Theme.MaterialComponents">
    </style>
    <style name="Base.Theme.MaterialComponents.Light" parent="@style/Base.V21.Theme.MaterialComponents.Light">
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Light.Bridge">
    </style>
    <style name="Base.Theme.MaterialComponents.Light.DarkActionBar" parent="@style/Base.Theme.MaterialComponents.Light">
        <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.Dark.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge">
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog" parent="@style/Base.V21.Theme.MaterialComponents.Light.Dialog">
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Light.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge">
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.FixedSize" parent="@style/Base.Theme.MaterialComponents.Light.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Light.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.DialogWhenLarge" parent="@style/Theme.MaterialComponents.Light">
    </style>
    <style name="Base.ThemeOverlay.AppCompat" parent="@style/Platform.ThemeOverlay.AppCompat">
    </style>
    <style name="Base.ThemeOverlay.AppCompat.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark" parent="@style/Platform.ThemeOverlay.AppCompat.Dark">
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="isLightTheme">false</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.Dark">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dialog" parent="@style/Base.V21.ThemeOverlay.AppCompat.Dialog">
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dialog.Alert" parent="@style/Base.ThemeOverlay.AppCompat.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Light" parent="@style/Platform.ThemeOverlay.AppCompat.Light">
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="isLightTheme">true</item>
    </style>
    <style name="Base.ThemeOverlay.Material3.AutoCompleteTextView" parent="@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView">
        <item name="colorControlActivated">?attr/colorPrimary</item>
    </style>
    <style name="Base.ThemeOverlay.Material3.BottomSheetDialog" parent="@style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog">
    </style>
    <style name="Base.ThemeOverlay.Material3.Dialog" parent="@style/Base.ThemeOverlay.MaterialComponents.Dialog">
        <item name="android:backgroundDimAmount">@dimen/m3_comp_scrim_container_opacity</item>
        <item name="android:windowAnimationStyle">@style/MaterialAlertDialog.Material3.Animation</item>
        <item name="materialButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog.Flush</item>
    </style>
    <style name="Base.ThemeOverlay.Material3.SideSheetDialog" parent="@style/Base.V21.ThemeOverlay.Material3.SideSheetDialog">
    </style>
    <style name="Base.ThemeOverlay.Material3.TextInputEditText" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText">
        <item name="colorControlActivated">?attr/colorPrimary</item>
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog" parent="@style/Base.V14.ThemeOverlay.MaterialComponents.Dialog">
        <item name="android:windowBackground">@drawable/mtrl_dialog_background</item>
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert" parent="@style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert">
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework" parent="@android:style/Theme.Material.Dialog.Alert">
        <item name="android:buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework" parent="@android:style/Theme.Material.Light.Dialog.Alert">
        <item name="android:buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog" parent="@style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog">
    </style>
    <style name="Base.V14.Theme.Material3.Dark" parent="@style/Theme.MaterialComponents">
        <item name="android:colorBackground">@color/m3_sys_color_dark_background</item>
        <item name="android:backgroundDimAmount">@dimen/m3_comp_scrim_container_opacity</item>
        <item name="android:textColorPrimary">@color/m3_dark_default_color_primary_text</item>
        <item name="android:textColorPrimaryDisableOnly">@color/m3_dark_primary_text_disable_only</item>
        <item name="android:textColorSecondary">@color/m3_dark_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverse">@color/m3_default_color_primary_text</item>
        <item name="android:textColorSecondaryInverse">@color/m3_default_color_secondary_text</item>
        <item name="android:textColorHintInverse">@color/m3_hint_foreground</item>
        <item name="android:windowBackground">?android:attr/colorBackground</item>
        <item name="android:textColorHighlight">@color/m3_dark_highlighted_text</item>
        <item name="android:textColorHint">@color/m3_dark_hint_foreground</item>
        <item name="android:textColorLink">?attr/colorPrimary</item>
        <item name="android:textColorTertiary">@color/m3_dark_default_color_secondary_text</item>
        <item name="android:textColorTertiaryInverse">@color/m3_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_primary_text_disable_only</item>
        <item name="android:textColorAlertDialogListItem">@color/m3_dark_default_color_primary_text</item>
        <item name="android:textColorHighlightInverse">@color/m3_highlighted_text</item>
        <item name="android:textColorLinkInverse">?attr/colorPrimaryInverse</item>
        <item name="android:textAppearanceListItem">?attr/textAppearanceTitleMedium</item>
        <item name="android:textAppearanceListItemSmall">?attr/textAppearanceTitleMedium</item>
        <item name="android:textAppearanceListItemSecondary">?attr/textAppearanceBodyMedium</item>
        <item name="actionBarPopupTheme">@style/ThemeOverlay.Material3.Dark</item>
        <item name="actionBarSize">@dimen/m3_appbar_size_compact</item>
        <item name="actionBarStyle">@style/Widget.Material3.ActionBar.Solid</item>
        <item name="actionModeStyle">@style/Widget.Material3.ActionMode</item>
        <item name="actionOverflowMenuStyle">@style/Widget.Material3.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.Material3.Dialog.Alert</item>
        <item name="appBarLayoutStyle">@style/Widget.Material3.AppBarLayout</item>
        <item name="badgeStyle">@style/Widget.Material3.Badge</item>
        <item name="borderlessButtonStyle">@style/Widget.Material3.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.Material3.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.Material3.BottomNavigationView</item>
        <item name="bottomSheetDialogTheme">@style/ThemeOverlay.Material3.BottomSheetDialog</item>
        <item name="bottomSheetDragHandleStyle">@style/Widget.Material3.BottomSheet.DragHandle</item>
        <item name="buttonBarButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog</item>
        <item name="checkboxStyle">@style/Widget.Material3.CompoundButton.CheckBox</item>
        <item name="chipGroupStyle">@style/Widget.Material3.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.Material3.Chip.Input</item>
        <item name="chipStyle">@style/Widget.Material3.Chip.Assist</item>
        <item name="circularProgressIndicatorStyle">@style/Widget.Material3.CircularProgressIndicator</item>
        <item name="collapsingToolbarLayoutLargeSize">@dimen/m3_appbar_size_large</item>
        <item name="collapsingToolbarLayoutLargeStyle">@style/Widget.Material3.CollapsingToolbar.Large</item>
        <item name="collapsingToolbarLayoutMediumSize">@dimen/m3_appbar_size_medium</item>
        <item name="collapsingToolbarLayoutMediumStyle">@style/Widget.Material3.CollapsingToolbar.Medium</item>
        <item name="collapsingToolbarLayoutStyle">@style/Widget.Material3.CollapsingToolbar</item>
        <item name="colorError">@color/m3_sys_color_dark_error</item>
        <item name="colorErrorContainer">@color/m3_sys_color_dark_error_container</item>
        <item name="colorOnBackground">@color/m3_sys_color_dark_on_background</item>
        <item name="colorOnError">@color/m3_sys_color_dark_on_error</item>
        <item name="colorOnErrorContainer">@color/m3_sys_color_dark_on_error_container</item>
        <item name="colorOnPrimary">@color/m3_sys_color_dark_on_primary</item>
        <item name="colorOnPrimaryContainer">@color/m3_sys_color_dark_on_primary_container</item>
        <item name="colorOnPrimaryFixed">@color/m3_sys_color_on_primary_fixed</item>
        <item name="colorOnPrimaryFixedVariant">@color/m3_sys_color_on_primary_fixed_variant</item>
        <item name="colorOnSecondary">@color/m3_sys_color_dark_on_secondary</item>
        <item name="colorOnSecondaryContainer">@color/m3_sys_color_dark_on_secondary_container</item>
        <item name="colorOnSecondaryFixed">@color/m3_sys_color_on_secondary_fixed</item>
        <item name="colorOnSecondaryFixedVariant">@color/m3_sys_color_on_secondary_fixed_variant</item>
        <item name="colorOnSurface">@color/m3_sys_color_dark_on_surface</item>
        <item name="colorOnSurfaceInverse">@color/m3_sys_color_dark_inverse_on_surface</item>
        <item name="colorOnSurfaceVariant">@color/m3_sys_color_dark_on_surface_variant</item>
        <item name="colorOnTertiary">@color/m3_sys_color_dark_on_tertiary</item>
        <item name="colorOnTertiaryContainer">@color/m3_sys_color_dark_on_tertiary_container</item>
        <item name="colorOnTertiaryFixed">@color/m3_sys_color_on_tertiary_fixed</item>
        <item name="colorOnTertiaryFixedVariant">@color/m3_sys_color_on_tertiary_fixed_variant</item>
        <item name="colorOutline">@color/m3_sys_color_dark_outline</item>
        <item name="colorOutlineVariant">@color/m3_sys_color_dark_outline_variant</item>
        <item name="colorPrimary">@color/m3_sys_color_dark_primary</item>
        <item name="colorPrimaryContainer">@color/m3_sys_color_dark_primary_container</item>
        <item name="colorPrimaryDark">?attr/colorPrimary</item>
        <item name="colorPrimaryFixed">@color/m3_sys_color_primary_fixed</item>
        <item name="colorPrimaryFixedDim">@color/m3_sys_color_primary_fixed_dim</item>
        <item name="colorPrimaryInverse">@color/m3_sys_color_dark_inverse_primary</item>
        <item name="colorPrimaryVariant">?attr/colorPrimary</item>
        <item name="colorSecondary">@color/m3_sys_color_dark_secondary</item>
        <item name="colorSecondaryContainer">@color/m3_sys_color_dark_secondary_container</item>
        <item name="colorSecondaryFixed">@color/m3_sys_color_secondary_fixed</item>
        <item name="colorSecondaryFixedDim">@color/m3_sys_color_secondary_fixed_dim</item>
        <item name="colorSecondaryVariant">?attr/colorSecondary</item>
        <item name="colorSurface">@color/m3_sys_color_dark_surface</item>
        <item name="colorSurfaceBright">@color/m3_sys_color_dark_surface_bright</item>
        <item name="colorSurfaceContainer">@color/m3_sys_color_dark_surface_container</item>
        <item name="colorSurfaceContainerHigh">@color/m3_sys_color_dark_surface_container_high</item>
        <item name="colorSurfaceContainerHighest">@color/m3_sys_color_dark_surface_container_highest</item>
        <item name="colorSurfaceContainerLow">@color/m3_sys_color_dark_surface_container_low</item>
        <item name="colorSurfaceContainerLowest">@color/m3_sys_color_dark_surface_container_lowest</item>
        <item name="colorSurfaceDim">@color/m3_sys_color_dark_surface_dim</item>
        <item name="colorSurfaceInverse">@color/m3_sys_color_dark_inverse_surface</item>
        <item name="colorSurfaceVariant">@color/m3_sys_color_dark_surface_variant</item>
        <item name="colorTertiary">@color/m3_sys_color_dark_tertiary</item>
        <item name="colorTertiaryContainer">@color/m3_sys_color_dark_tertiary_container</item>
        <item name="colorTertiaryFixed">@color/m3_sys_color_tertiary_fixed</item>
        <item name="colorTertiaryFixedDim">@color/m3_sys_color_tertiary_fixed_dim</item>
        <item name="drawerLayoutStyle">@style/Widget.Material3.DrawerLayout</item>
        <item name="dynamicColorThemeOverlay">@style/ThemeOverlay.Material3.DynamicColors.Dark</item>
        <item name="elevationOverlayColor">?attr/colorPrimary</item>
        <item name="elevationOverlayEnabled">true</item>
        <item name="extendedFloatingActionButtonPrimaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary</item>
        <item name="extendedFloatingActionButtonSecondaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary</item>
        <item name="extendedFloatingActionButtonStyle">?attr/extendedFloatingActionButtonPrimaryStyle</item>
        <item name="extendedFloatingActionButtonSurfaceStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface</item>
        <item name="extendedFloatingActionButtonTertiaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary</item>
        <item name="floatingActionButtonLargePrimaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Primary</item>
        <item name="floatingActionButtonLargeSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Secondary</item>
        <item name="floatingActionButtonLargeStyle">?attr/floatingActionButtonLargePrimaryStyle</item>
        <item name="floatingActionButtonLargeSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Large.Surface</item>
        <item name="floatingActionButtonLargeTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Tertiary</item>
        <item name="floatingActionButtonPrimaryStyle">@style/Widget.Material3.FloatingActionButton.Primary</item>
        <item name="floatingActionButtonSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Secondary</item>
        <item name="floatingActionButtonSmallPrimaryStyle">@style/Widget.Material3.FloatingActionButton.Small.Primary</item>
        <item name="floatingActionButtonSmallSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Small.Secondary</item>
        <item name="floatingActionButtonSmallStyle">?attr/floatingActionButtonSmallPrimaryStyle</item>
        <item name="floatingActionButtonSmallSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Small.Surface</item>
        <item name="floatingActionButtonSmallTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Small.Tertiary</item>
        <item name="floatingActionButtonStyle">?attr/floatingActionButtonPrimaryStyle</item>
        <item name="floatingActionButtonSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Surface</item>
        <item name="floatingActionButtonTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Tertiary</item>
        <item name="isMaterial3Theme">true</item>
        <item name="linearProgressIndicatorStyle">@style/Widget.Material3.LinearProgressIndicator</item>
        <item name="listPopupWindowStyle">@style/Widget.Material3.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.Material3.MaterialAlertDialog</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.Material3.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.Material3.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.Material3.MaterialButtonToggleGroup</item>
        <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.Material3.MaterialCalendar</item>
        <item name="materialCalendarTheme">@style/ThemeOverlay.Material3.MaterialCalendar</item>
        <item name="materialCardViewElevatedStyle">@style/Widget.Material3.CardView.Elevated</item>
        <item name="materialCardViewFilledStyle">@style/Widget.Material3.CardView.Filled</item>
        <item name="materialCardViewOutlinedStyle">@style/Widget.Material3.CardView.Outlined</item>
        <item name="materialCardViewStyle">?attr/materialCardViewOutlinedStyle</item>
        <item name="materialDividerHeavyStyle">@style/Widget.Material3.MaterialDivider.Heavy</item>
        <item name="materialDividerStyle">@style/Widget.Material3.MaterialDivider</item>
        <item name="materialIconButtonFilledStyle">@style/Widget.Material3.Button.IconButton.Filled</item>
        <item name="materialIconButtonFilledTonalStyle">@style/Widget.Material3.Button.IconButton.Filled.Tonal</item>
        <item name="materialIconButtonOutlinedStyle">@style/Widget.Material3.Button.IconButton.Outlined</item>
        <item name="materialIconButtonStyle">@style/Widget.Material3.Button.IconButton</item>
        <item name="materialSearchBarStyle">@style/Widget.Material3.SearchBar</item>
        <item name="materialSearchViewPrefixStyle">@style/Widget.Material3.SearchView.Prefix</item>
        <item name="materialSearchViewStyle">@style/Widget.Material3.SearchView</item>
        <item name="materialSearchViewToolbarHeight">@dimen/m3_searchview_height</item>
        <item name="materialSearchViewToolbarStyle">@style/Widget.Material3.SearchView.Toolbar</item>
        <item name="materialSwitchStyle">@style/Widget.Material3.CompoundButton.MaterialSwitch</item>
        <item name="materialTimePickerTheme">@style/ThemeOverlay.Material3.MaterialTimePicker</item>
        <item name="motionDurationExtraLong1">@integer/m3_sys_motion_duration_extra_long1</item>
        <item name="motionDurationExtraLong2">@integer/m3_sys_motion_duration_extra_long2</item>
        <item name="motionDurationExtraLong3">@integer/m3_sys_motion_duration_extra_long3</item>
        <item name="motionDurationExtraLong4">@integer/m3_sys_motion_duration_extra_long4</item>
        <item name="motionDurationLong1">@integer/m3_sys_motion_duration_long1</item>
        <item name="motionDurationLong2">@integer/m3_sys_motion_duration_long2</item>
        <item name="motionDurationLong3">@integer/m3_sys_motion_duration_long3</item>
        <item name="motionDurationLong4">@integer/m3_sys_motion_duration_long4</item>
        <item name="motionDurationMedium1">@integer/m3_sys_motion_duration_medium1</item>
        <item name="motionDurationMedium2">@integer/m3_sys_motion_duration_medium2</item>
        <item name="motionDurationMedium3">@integer/m3_sys_motion_duration_medium3</item>
        <item name="motionDurationMedium4">@integer/m3_sys_motion_duration_medium4</item>
        <item name="motionDurationShort1">@integer/m3_sys_motion_duration_short1</item>
        <item name="motionDurationShort2">@integer/m3_sys_motion_duration_short2</item>
        <item name="motionDurationShort3">@integer/m3_sys_motion_duration_short3</item>
        <item name="motionDurationShort4">@integer/m3_sys_motion_duration_short4</item>
        <item name="motionEasingAccelerated">@string/m3_sys_motion_easing_legacy_accelerate</item>
        <item name="motionEasingDecelerated">@string/m3_sys_motion_easing_legacy_decelerate</item>
        <item name="motionEasingEmphasized">@string/m3_sys_motion_easing_emphasized</item>
        <item name="motionEasingEmphasizedAccelerateInterpolator">@interpolator/m3_sys_motion_easing_emphasized_accelerate</item>
        <item name="motionEasingEmphasizedDecelerateInterpolator">@interpolator/m3_sys_motion_easing_emphasized_decelerate</item>
        <item name="motionEasingEmphasizedInterpolator">@interpolator/m3_sys_motion_easing_emphasized</item>
        <item name="motionEasingLinear">@string/m3_sys_motion_easing_linear</item>
        <item name="motionEasingLinearInterpolator">@interpolator/m3_sys_motion_easing_linear</item>
        <item name="motionEasingStandard">@string/m3_sys_motion_easing_standard</item>
        <item name="motionEasingStandardAccelerateInterpolator">@interpolator/m3_sys_motion_easing_standard_accelerate</item>
        <item name="motionEasingStandardDecelerateInterpolator">@interpolator/m3_sys_motion_easing_standard_decelerate</item>
        <item name="motionEasingStandardInterpolator">@interpolator/m3_sys_motion_easing_standard</item>
        <item name="motionPath">@integer/m3_sys_motion_path</item>
        <item name="navigationRailStyle">@style/Widget.Material3.NavigationRailView</item>
        <item name="navigationViewStyle">@style/Widget.Material3.NavigationView</item>
        <item name="popupMenuBackground">@drawable/m3_popupmenu_background_overlay</item>
        <item name="popupMenuStyle">@style/Widget.Material3.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.Material3.CompoundButton.RadioButton</item>
        <item name="shapeAppearanceCornerExtraLarge">@style/ShapeAppearance.Material3.Corner.ExtraLarge</item>
        <item name="shapeAppearanceCornerExtraSmall">@style/ShapeAppearance.Material3.Corner.ExtraSmall</item>
        <item name="shapeAppearanceCornerLarge">@style/ShapeAppearance.Material3.Corner.Large</item>
        <item name="shapeAppearanceCornerMedium">@style/ShapeAppearance.Material3.Corner.Medium</item>
        <item name="shapeAppearanceCornerSmall">@style/ShapeAppearance.Material3.Corner.Small</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.Material3.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.Material3.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.Material3.SmallComponent</item>
        <item name="shapeCornerFamily">@integer/m3_sys_shape_corner_full_corner_family</item>
        <item name="sideSheetDialogTheme">@style/ThemeOverlay.Material3.SideSheetDialog</item>
        <item name="sliderStyle">@style/Widget.Material3.Slider</item>
        <item name="snackbarButtonStyle">@style/Widget.Material3.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.Material3.Snackbar</item>
        <item name="snackbarTextViewStyle">@style/Widget.Material3.Snackbar.TextView</item>
        <item name="switchStyle">@style/Widget.Material3.CompoundButton.Switch</item>
        <item name="tabSecondaryStyle">@style/Widget.Material3.TabLayout.Secondary</item>
        <item name="tabStyle">@style/Widget.Material3.TabLayout</item>
        <item name="textAppearanceBodyLarge">@style/TextAppearance.Material3.BodyLarge</item>
        <item name="textAppearanceBodyMedium">@style/TextAppearance.Material3.BodyMedium</item>
        <item name="textAppearanceBodySmall">@style/TextAppearance.Material3.BodySmall</item>
        <item name="textAppearanceDisplayLarge">@style/TextAppearance.Material3.DisplayLarge</item>
        <item name="textAppearanceDisplayMedium">@style/TextAppearance.Material3.DisplayMedium</item>
        <item name="textAppearanceDisplaySmall">@style/TextAppearance.Material3.DisplaySmall</item>
        <item name="textAppearanceHeadlineLarge">@style/TextAppearance.Material3.HeadlineLarge</item>
        <item name="textAppearanceHeadlineMedium">@style/TextAppearance.Material3.HeadlineMedium</item>
        <item name="textAppearanceHeadlineSmall">@style/TextAppearance.Material3.HeadlineSmall</item>
        <item name="textAppearanceLabelLarge">@style/TextAppearance.Material3.LabelLarge</item>
        <item name="textAppearanceLabelMedium">@style/TextAppearance.Material3.LabelMedium</item>
        <item name="textAppearanceLabelSmall">@style/TextAppearance.Material3.LabelSmall</item>
        <item name="textAppearanceLargePopupMenu">?attr/textAppearanceBodyLarge</item>
        <item name="textAppearanceListItem">?attr/textAppearanceTitleMedium</item>
        <item name="textAppearanceListItemSecondary">?attr/textAppearanceBodyMedium</item>
        <item name="textAppearanceListItemSmall">?attr/textAppearanceTitleMedium</item>
        <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceBodyLarge</item>
        <item name="textAppearanceTitleLarge">@style/TextAppearance.Material3.TitleLarge</item>
        <item name="textAppearanceTitleMedium">@style/TextAppearance.Material3.TitleMedium</item>
        <item name="textAppearanceTitleSmall">@style/TextAppearance.Material3.TitleSmall</item>
        <item name="textInputFilledDenseStyle">@style/Widget.Material3.TextInputLayout.FilledBox.Dense</item>
        <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
        <item name="textInputFilledStyle">@style/Widget.Material3.TextInputLayout.FilledBox</item>
        <item name="textInputOutlinedDenseStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox.Dense</item>
        <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
        <item name="textInputOutlinedStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox</item>
        <item name="textInputStyle">?attr/textInputOutlinedStyle</item>
        <item name="toolbarStyle">@style/Widget.Material3.Toolbar</item>
        <item name="toolbarSurfaceStyle">@style/Widget.Material3.Toolbar.Surface</item>
    </style>
    <style name="Base.V14.Theme.Material3.Dark.BottomSheetDialog" parent="@style/Theme.Material3.Dark.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowAnimationStyle">@style/Animation.Material3.BottomSheetDialog</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
        <item name="bottomSheetStyle">@style/Widget.Material3.BottomSheet.Modal</item>
    </style>
    <style name="Base.V14.Theme.Material3.Dark.Dialog" parent="@style/Theme.MaterialComponents.Dialog">
        <item name="android:colorBackground">@color/m3_sys_color_dark_background</item>
        <item name="android:backgroundDimAmount">@dimen/m3_comp_scrim_container_opacity</item>
        <item name="android:textColorPrimary">@color/m3_dark_default_color_primary_text</item>
        <item name="android:textColorPrimaryDisableOnly">@color/m3_dark_primary_text_disable_only</item>
        <item name="android:textColorSecondary">@color/m3_dark_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverse">@color/m3_default_color_primary_text</item>
        <item name="android:textColorSecondaryInverse">@color/m3_default_color_secondary_text</item>
        <item name="android:textColorHintInverse">@color/m3_hint_foreground</item>
        <item name="android:textColorHighlight">@color/m3_dark_highlighted_text</item>
        <item name="android:textColorHint">@color/m3_dark_hint_foreground</item>
        <item name="android:textColorLink">?attr/colorPrimary</item>
        <item name="android:textColorTertiary">@color/m3_dark_default_color_secondary_text</item>
        <item name="android:textColorTertiaryInverse">@color/m3_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_primary_text_disable_only</item>
        <item name="android:textColorAlertDialogListItem">@color/m3_dark_default_color_primary_text</item>
        <item name="android:textColorHighlightInverse">@color/m3_highlighted_text</item>
        <item name="android:textColorLinkInverse">?attr/colorPrimaryInverse</item>
        <item name="android:textAppearanceListItem">?attr/textAppearanceTitleMedium</item>
        <item name="android:textAppearanceListItemSmall">?attr/textAppearanceTitleMedium</item>
        <item name="android:textAppearanceListItemSecondary">?attr/textAppearanceBodyMedium</item>
        <item name="actionBarPopupTheme">@style/ThemeOverlay.Material3.Dark</item>
        <item name="actionBarSize">@dimen/m3_appbar_size_compact</item>
        <item name="actionBarStyle">@style/Widget.Material3.ActionBar.Solid</item>
        <item name="actionModeStyle">@style/Widget.Material3.ActionMode</item>
        <item name="actionOverflowMenuStyle">@style/Widget.Material3.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.Material3.Dialog.Alert</item>
        <item name="appBarLayoutStyle">@style/Widget.Material3.AppBarLayout</item>
        <item name="badgeStyle">@style/Widget.Material3.Badge</item>
        <item name="borderlessButtonStyle">@style/Widget.Material3.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.Material3.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.Material3.BottomNavigationView</item>
        <item name="bottomSheetDragHandleStyle">@style/Widget.Material3.BottomSheet.DragHandle</item>
        <item name="buttonBarButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog</item>
        <item name="checkboxStyle">@style/Widget.Material3.CompoundButton.CheckBox</item>
        <item name="chipGroupStyle">@style/Widget.Material3.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.Material3.Chip.Input</item>
        <item name="chipStyle">@style/Widget.Material3.Chip.Assist</item>
        <item name="circularProgressIndicatorStyle">@style/Widget.Material3.CircularProgressIndicator</item>
        <item name="collapsingToolbarLayoutLargeSize">@dimen/m3_appbar_size_large</item>
        <item name="collapsingToolbarLayoutLargeStyle">@style/Widget.Material3.CollapsingToolbar.Large</item>
        <item name="collapsingToolbarLayoutMediumSize">@dimen/m3_appbar_size_medium</item>
        <item name="collapsingToolbarLayoutMediumStyle">@style/Widget.Material3.CollapsingToolbar.Medium</item>
        <item name="collapsingToolbarLayoutStyle">@style/Widget.Material3.CollapsingToolbar</item>
        <item name="colorError">@color/m3_sys_color_dark_error</item>
        <item name="colorErrorContainer">@color/m3_sys_color_dark_error_container</item>
        <item name="colorOnBackground">@color/m3_sys_color_dark_on_background</item>
        <item name="colorOnError">@color/m3_sys_color_dark_on_error</item>
        <item name="colorOnErrorContainer">@color/m3_sys_color_dark_on_error_container</item>
        <item name="colorOnPrimary">@color/m3_sys_color_dark_on_primary</item>
        <item name="colorOnPrimaryContainer">@color/m3_sys_color_dark_on_primary_container</item>
        <item name="colorOnPrimaryFixed">@color/m3_sys_color_on_primary_fixed</item>
        <item name="colorOnPrimaryFixedVariant">@color/m3_sys_color_on_primary_fixed_variant</item>
        <item name="colorOnSecondary">@color/m3_sys_color_dark_on_secondary</item>
        <item name="colorOnSecondaryContainer">@color/m3_sys_color_dark_on_secondary_container</item>
        <item name="colorOnSecondaryFixed">@color/m3_sys_color_on_secondary_fixed</item>
        <item name="colorOnSecondaryFixedVariant">@color/m3_sys_color_on_secondary_fixed_variant</item>
        <item name="colorOnSurface">@color/m3_sys_color_dark_on_surface</item>
        <item name="colorOnSurfaceInverse">@color/m3_sys_color_dark_inverse_on_surface</item>
        <item name="colorOnSurfaceVariant">@color/m3_sys_color_dark_on_surface_variant</item>
        <item name="colorOnTertiary">@color/m3_sys_color_dark_on_tertiary</item>
        <item name="colorOnTertiaryContainer">@color/m3_sys_color_dark_on_tertiary_container</item>
        <item name="colorOnTertiaryFixed">@color/m3_sys_color_on_tertiary_fixed</item>
        <item name="colorOnTertiaryFixedVariant">@color/m3_sys_color_on_tertiary_fixed_variant</item>
        <item name="colorOutline">@color/m3_sys_color_dark_outline</item>
        <item name="colorOutlineVariant">@color/m3_sys_color_dark_outline_variant</item>
        <item name="colorPrimary">@color/m3_sys_color_dark_primary</item>
        <item name="colorPrimaryContainer">@color/m3_sys_color_dark_primary_container</item>
        <item name="colorPrimaryDark">?attr/colorPrimary</item>
        <item name="colorPrimaryFixed">@color/m3_sys_color_primary_fixed</item>
        <item name="colorPrimaryFixedDim">@color/m3_sys_color_primary_fixed_dim</item>
        <item name="colorPrimaryInverse">@color/m3_sys_color_dark_inverse_primary</item>
        <item name="colorPrimaryVariant">?attr/colorPrimary</item>
        <item name="colorSecondary">@color/m3_sys_color_dark_secondary</item>
        <item name="colorSecondaryContainer">@color/m3_sys_color_dark_secondary_container</item>
        <item name="colorSecondaryFixed">@color/m3_sys_color_secondary_fixed</item>
        <item name="colorSecondaryFixedDim">@color/m3_sys_color_secondary_fixed_dim</item>
        <item name="colorSecondaryVariant">?attr/colorSecondary</item>
        <item name="colorSurface">@color/m3_sys_color_dark_surface</item>
        <item name="colorSurfaceBright">@color/m3_sys_color_dark_surface_bright</item>
        <item name="colorSurfaceContainer">@color/m3_sys_color_dark_surface_container</item>
        <item name="colorSurfaceContainerHigh">@color/m3_sys_color_dark_surface_container_high</item>
        <item name="colorSurfaceContainerHighest">@color/m3_sys_color_dark_surface_container_highest</item>
        <item name="colorSurfaceContainerLow">@color/m3_sys_color_dark_surface_container_low</item>
        <item name="colorSurfaceContainerLowest">@color/m3_sys_color_dark_surface_container_lowest</item>
        <item name="colorSurfaceDim">@color/m3_sys_color_dark_surface_dim</item>
        <item name="colorSurfaceInverse">@color/m3_sys_color_dark_inverse_surface</item>
        <item name="colorSurfaceVariant">@color/m3_sys_color_dark_surface_variant</item>
        <item name="colorTertiary">@color/m3_sys_color_dark_tertiary</item>
        <item name="colorTertiaryContainer">@color/m3_sys_color_dark_tertiary_container</item>
        <item name="colorTertiaryFixed">@color/m3_sys_color_tertiary_fixed</item>
        <item name="colorTertiaryFixedDim">@color/m3_sys_color_tertiary_fixed_dim</item>
        <item name="drawerLayoutStyle">@style/Widget.Material3.DrawerLayout</item>
        <item name="dynamicColorThemeOverlay">@style/ThemeOverlay.Material3.DynamicColors.Dark</item>
        <item name="elevationOverlayColor">?attr/colorPrimary</item>
        <item name="elevationOverlayEnabled">true</item>
        <item name="extendedFloatingActionButtonPrimaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary</item>
        <item name="extendedFloatingActionButtonSecondaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary</item>
        <item name="extendedFloatingActionButtonStyle">?attr/extendedFloatingActionButtonPrimaryStyle</item>
        <item name="extendedFloatingActionButtonSurfaceStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface</item>
        <item name="extendedFloatingActionButtonTertiaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary</item>
        <item name="floatingActionButtonLargePrimaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Primary</item>
        <item name="floatingActionButtonLargeSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Secondary</item>
        <item name="floatingActionButtonLargeStyle">?attr/floatingActionButtonLargePrimaryStyle</item>
        <item name="floatingActionButtonLargeSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Large.Surface</item>
        <item name="floatingActionButtonLargeTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Tertiary</item>
        <item name="floatingActionButtonPrimaryStyle">@style/Widget.Material3.FloatingActionButton.Primary</item>
        <item name="floatingActionButtonSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Secondary</item>
        <item name="floatingActionButtonSmallPrimaryStyle">@style/Widget.Material3.FloatingActionButton.Small.Primary</item>
        <item name="floatingActionButtonSmallSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Small.Secondary</item>
        <item name="floatingActionButtonSmallStyle">?attr/floatingActionButtonSmallPrimaryStyle</item>
        <item name="floatingActionButtonSmallSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Small.Surface</item>
        <item name="floatingActionButtonSmallTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Small.Tertiary</item>
        <item name="floatingActionButtonStyle">?attr/floatingActionButtonPrimaryStyle</item>
        <item name="floatingActionButtonSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Surface</item>
        <item name="floatingActionButtonTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Tertiary</item>
        <item name="isMaterial3Theme">true</item>
        <item name="linearProgressIndicatorStyle">@style/Widget.Material3.LinearProgressIndicator</item>
        <item name="listPopupWindowStyle">@style/Widget.Material3.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.Material3.MaterialAlertDialog</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.Material3.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.Material3.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.Material3.MaterialButtonToggleGroup</item>
        <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.Material3.MaterialCalendar</item>
        <item name="materialCalendarTheme">@style/ThemeOverlay.Material3.MaterialCalendar</item>
        <item name="materialCardViewElevatedStyle">@style/Widget.Material3.CardView.Elevated</item>
        <item name="materialCardViewFilledStyle">@style/Widget.Material3.CardView.Filled</item>
        <item name="materialCardViewOutlinedStyle">@style/Widget.Material3.CardView.Outlined</item>
        <item name="materialCardViewStyle">?attr/materialCardViewOutlinedStyle</item>
        <item name="materialDividerHeavyStyle">@style/Widget.Material3.MaterialDivider.Heavy</item>
        <item name="materialDividerStyle">@style/Widget.Material3.MaterialDivider</item>
        <item name="materialIconButtonFilledStyle">@style/Widget.Material3.Button.IconButton.Filled</item>
        <item name="materialIconButtonFilledTonalStyle">@style/Widget.Material3.Button.IconButton.Filled.Tonal</item>
        <item name="materialIconButtonOutlinedStyle">@style/Widget.Material3.Button.IconButton.Outlined</item>
        <item name="materialIconButtonStyle">@style/Widget.Material3.Button.IconButton</item>
        <item name="materialSearchBarStyle">@style/Widget.Material3.SearchBar</item>
        <item name="materialSearchViewPrefixStyle">@style/Widget.Material3.SearchView.Prefix</item>
        <item name="materialSearchViewStyle">@style/Widget.Material3.SearchView</item>
        <item name="materialSearchViewToolbarHeight">@dimen/m3_searchview_height</item>
        <item name="materialSearchViewToolbarStyle">@style/Widget.Material3.SearchView.Toolbar</item>
        <item name="materialSwitchStyle">@style/Widget.Material3.CompoundButton.MaterialSwitch</item>
        <item name="materialTimePickerTheme">@style/ThemeOverlay.Material3.MaterialTimePicker</item>
        <item name="motionDurationExtraLong1">@integer/m3_sys_motion_duration_extra_long1</item>
        <item name="motionDurationExtraLong2">@integer/m3_sys_motion_duration_extra_long2</item>
        <item name="motionDurationExtraLong3">@integer/m3_sys_motion_duration_extra_long3</item>
        <item name="motionDurationExtraLong4">@integer/m3_sys_motion_duration_extra_long4</item>
        <item name="motionDurationLong1">@integer/m3_sys_motion_duration_long1</item>
        <item name="motionDurationLong2">@integer/m3_sys_motion_duration_long2</item>
        <item name="motionDurationLong3">@integer/m3_sys_motion_duration_long3</item>
        <item name="motionDurationLong4">@integer/m3_sys_motion_duration_long4</item>
        <item name="motionDurationMedium1">@integer/m3_sys_motion_duration_medium1</item>
        <item name="motionDurationMedium2">@integer/m3_sys_motion_duration_medium2</item>
        <item name="motionDurationMedium3">@integer/m3_sys_motion_duration_medium3</item>
        <item name="motionDurationMedium4">@integer/m3_sys_motion_duration_medium4</item>
        <item name="motionDurationShort1">@integer/m3_sys_motion_duration_short1</item>
        <item name="motionDurationShort2">@integer/m3_sys_motion_duration_short2</item>
        <item name="motionDurationShort3">@integer/m3_sys_motion_duration_short3</item>
        <item name="motionDurationShort4">@integer/m3_sys_motion_duration_short4</item>
        <item name="motionEasingAccelerated">@string/m3_sys_motion_easing_legacy_accelerate</item>
        <item name="motionEasingDecelerated">@string/m3_sys_motion_easing_legacy_decelerate</item>
        <item name="motionEasingEmphasized">@string/m3_sys_motion_easing_emphasized</item>
        <item name="motionEasingEmphasizedAccelerateInterpolator">@interpolator/m3_sys_motion_easing_emphasized_accelerate</item>
        <item name="motionEasingEmphasizedDecelerateInterpolator">@interpolator/m3_sys_motion_easing_emphasized_decelerate</item>
        <item name="motionEasingEmphasizedInterpolator">@interpolator/m3_sys_motion_easing_emphasized</item>
        <item name="motionEasingLinear">@string/m3_sys_motion_easing_linear</item>
        <item name="motionEasingLinearInterpolator">@interpolator/m3_sys_motion_easing_linear</item>
        <item name="motionEasingStandard">@string/m3_sys_motion_easing_standard</item>
        <item name="motionEasingStandardAccelerateInterpolator">@interpolator/m3_sys_motion_easing_standard_accelerate</item>
        <item name="motionEasingStandardDecelerateInterpolator">@interpolator/m3_sys_motion_easing_standard_decelerate</item>
        <item name="motionEasingStandardInterpolator">@interpolator/m3_sys_motion_easing_standard</item>
        <item name="motionPath">@integer/m3_sys_motion_path</item>
        <item name="navigationRailStyle">@style/Widget.Material3.NavigationRailView</item>
        <item name="navigationViewStyle">@style/Widget.Material3.NavigationView</item>
        <item name="popupMenuBackground">@drawable/m3_popupmenu_background_overlay</item>
        <item name="popupMenuStyle">@style/Widget.Material3.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.Material3.CompoundButton.RadioButton</item>
        <item name="shapeAppearanceCornerExtraLarge">@style/ShapeAppearance.Material3.Corner.ExtraLarge</item>
        <item name="shapeAppearanceCornerExtraSmall">@style/ShapeAppearance.Material3.Corner.ExtraSmall</item>
        <item name="shapeAppearanceCornerLarge">@style/ShapeAppearance.Material3.Corner.Large</item>
        <item name="shapeAppearanceCornerMedium">@style/ShapeAppearance.Material3.Corner.Medium</item>
        <item name="shapeAppearanceCornerSmall">@style/ShapeAppearance.Material3.Corner.Small</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.Material3.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.Material3.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.Material3.SmallComponent</item>
        <item name="shapeCornerFamily">@integer/m3_sys_shape_corner_full_corner_family</item>
        <item name="sliderStyle">@style/Widget.Material3.Slider</item>
        <item name="snackbarButtonStyle">@style/Widget.Material3.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.Material3.Snackbar</item>
        <item name="snackbarTextViewStyle">@style/Widget.Material3.Snackbar.TextView</item>
        <item name="switchStyle">@style/Widget.Material3.CompoundButton.Switch</item>
        <item name="tabSecondaryStyle">@style/Widget.Material3.TabLayout.Secondary</item>
        <item name="tabStyle">@style/Widget.Material3.TabLayout</item>
        <item name="textAppearanceBodyLarge">@style/TextAppearance.Material3.BodyLarge</item>
        <item name="textAppearanceBodyMedium">@style/TextAppearance.Material3.BodyMedium</item>
        <item name="textAppearanceBodySmall">@style/TextAppearance.Material3.BodySmall</item>
        <item name="textAppearanceDisplayLarge">@style/TextAppearance.Material3.DisplayLarge</item>
        <item name="textAppearanceDisplayMedium">@style/TextAppearance.Material3.DisplayMedium</item>
        <item name="textAppearanceDisplaySmall">@style/TextAppearance.Material3.DisplaySmall</item>
        <item name="textAppearanceHeadlineLarge">@style/TextAppearance.Material3.HeadlineLarge</item>
        <item name="textAppearanceHeadlineMedium">@style/TextAppearance.Material3.HeadlineMedium</item>
        <item name="textAppearanceHeadlineSmall">@style/TextAppearance.Material3.HeadlineSmall</item>
        <item name="textAppearanceLabelLarge">@style/TextAppearance.Material3.LabelLarge</item>
        <item name="textAppearanceLabelMedium">@style/TextAppearance.Material3.LabelMedium</item>
        <item name="textAppearanceLabelSmall">@style/TextAppearance.Material3.LabelSmall</item>
        <item name="textAppearanceLargePopupMenu">?attr/textAppearanceBodyLarge</item>
        <item name="textAppearanceListItem">?attr/textAppearanceTitleMedium</item>
        <item name="textAppearanceListItemSecondary">?attr/textAppearanceBodyMedium</item>
        <item name="textAppearanceListItemSmall">?attr/textAppearanceTitleMedium</item>
        <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceBodyLarge</item>
        <item name="textAppearanceTitleLarge">@style/TextAppearance.Material3.TitleLarge</item>
        <item name="textAppearanceTitleMedium">@style/TextAppearance.Material3.TitleMedium</item>
        <item name="textAppearanceTitleSmall">@style/TextAppearance.Material3.TitleSmall</item>
        <item name="textInputFilledDenseStyle">@style/Widget.Material3.TextInputLayout.FilledBox.Dense</item>
        <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
        <item name="textInputFilledStyle">@style/Widget.Material3.TextInputLayout.FilledBox</item>
        <item name="textInputOutlinedDenseStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox.Dense</item>
        <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
        <item name="textInputOutlinedStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox</item>
        <item name="textInputStyle">?attr/textInputOutlinedStyle</item>
        <item name="toolbarStyle">@style/Widget.Material3.Toolbar</item>
        <item name="toolbarSurfaceStyle">@style/Widget.Material3.Toolbar.Surface</item>
    </style>
    <style name="Base.V14.Theme.Material3.Dark.SideSheetDialog" parent="@style/Theme.Material3.Dark.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowAnimationStyle">@style/Animation.Material3.SideSheetDialog</item>
        <item name="sideSheetModalStyle">@style/Widget.Material3.SideSheet.Modal</item>
    </style>
    <style name="Base.V14.Theme.Material3.Light" parent="@style/Theme.MaterialComponents.Light">
        <item name="android:colorBackground">@color/m3_sys_color_light_background</item>
        <item name="android:backgroundDimAmount">@dimen/m3_comp_scrim_container_opacity</item>
        <item name="android:textColorPrimary">@color/m3_default_color_primary_text</item>
        <item name="android:textColorPrimaryDisableOnly">@color/m3_primary_text_disable_only</item>
        <item name="android:textColorSecondary">@color/m3_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverse">@color/m3_dark_default_color_primary_text</item>
        <item name="android:textColorSecondaryInverse">@color/m3_dark_default_color_secondary_text</item>
        <item name="android:textColorHintInverse">@color/m3_dark_hint_foreground</item>
        <item name="android:windowBackground">?android:attr/colorBackground</item>
        <item name="android:textColorHighlight">@color/m3_highlighted_text</item>
        <item name="android:textColorHint">@color/m3_hint_foreground</item>
        <item name="android:textColorLink">?attr/colorPrimary</item>
        <item name="android:textColorTertiary">@color/m3_default_color_secondary_text</item>
        <item name="android:textColorTertiaryInverse">@color/m3_dark_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_dark_primary_text_disable_only</item>
        <item name="android:textColorAlertDialogListItem">@color/m3_default_color_primary_text</item>
        <item name="android:textColorHighlightInverse">@color/m3_dark_highlighted_text</item>
        <item name="android:textColorLinkInverse">?attr/colorPrimaryInverse</item>
        <item name="android:textAppearanceListItem">?attr/textAppearanceTitleMedium</item>
        <item name="android:textAppearanceListItemSmall">?attr/textAppearanceTitleMedium</item>
        <item name="android:textAppearanceListItemSecondary">?attr/textAppearanceBodyMedium</item>
        <item name="actionBarPopupTheme">@style/ThemeOverlay.Material3.Light</item>
        <item name="actionBarSize">@dimen/m3_appbar_size_compact</item>
        <item name="actionBarStyle">@style/Widget.Material3.Light.ActionBar.Solid</item>
        <item name="actionModeStyle">@style/Widget.Material3.ActionMode</item>
        <item name="actionOverflowMenuStyle">@style/Widget.Material3.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.Material3.Dialog.Alert</item>
        <item name="appBarLayoutStyle">@style/Widget.Material3.AppBarLayout</item>
        <item name="badgeStyle">@style/Widget.Material3.Badge</item>
        <item name="borderlessButtonStyle">@style/Widget.Material3.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.Material3.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.Material3.BottomNavigationView</item>
        <item name="bottomSheetDialogTheme">@style/ThemeOverlay.Material3.BottomSheetDialog</item>
        <item name="bottomSheetDragHandleStyle">@style/Widget.Material3.BottomSheet.DragHandle</item>
        <item name="buttonBarButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog</item>
        <item name="checkboxStyle">@style/Widget.Material3.CompoundButton.CheckBox</item>
        <item name="chipGroupStyle">@style/Widget.Material3.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.Material3.Chip.Input</item>
        <item name="chipStyle">@style/Widget.Material3.Chip.Assist</item>
        <item name="circularProgressIndicatorStyle">@style/Widget.Material3.CircularProgressIndicator</item>
        <item name="collapsingToolbarLayoutLargeSize">@dimen/m3_appbar_size_large</item>
        <item name="collapsingToolbarLayoutLargeStyle">@style/Widget.Material3.CollapsingToolbar.Large</item>
        <item name="collapsingToolbarLayoutMediumSize">@dimen/m3_appbar_size_medium</item>
        <item name="collapsingToolbarLayoutMediumStyle">@style/Widget.Material3.CollapsingToolbar.Medium</item>
        <item name="collapsingToolbarLayoutStyle">@style/Widget.Material3.CollapsingToolbar</item>
        <item name="colorError">@color/m3_sys_color_light_error</item>
        <item name="colorErrorContainer">@color/m3_sys_color_light_error_container</item>
        <item name="colorOnBackground">@color/m3_sys_color_light_on_background</item>
        <item name="colorOnError">@color/m3_sys_color_light_on_error</item>
        <item name="colorOnErrorContainer">@color/m3_sys_color_light_on_error_container</item>
        <item name="colorOnPrimary">@color/m3_sys_color_light_on_primary</item>
        <item name="colorOnPrimaryContainer">@color/m3_sys_color_light_on_primary_container</item>
        <item name="colorOnPrimaryFixed">@color/m3_sys_color_on_primary_fixed</item>
        <item name="colorOnPrimaryFixedVariant">@color/m3_sys_color_on_primary_fixed_variant</item>
        <item name="colorOnSecondary">@color/m3_sys_color_light_on_secondary</item>
        <item name="colorOnSecondaryContainer">@color/m3_sys_color_light_on_secondary_container</item>
        <item name="colorOnSecondaryFixed">@color/m3_sys_color_on_secondary_fixed</item>
        <item name="colorOnSecondaryFixedVariant">@color/m3_sys_color_on_secondary_fixed_variant</item>
        <item name="colorOnSurface">@color/m3_sys_color_light_on_surface</item>
        <item name="colorOnSurfaceInverse">@color/m3_sys_color_light_inverse_on_surface</item>
        <item name="colorOnSurfaceVariant">@color/m3_sys_color_light_on_surface_variant</item>
        <item name="colorOnTertiary">@color/m3_sys_color_light_on_tertiary</item>
        <item name="colorOnTertiaryContainer">@color/m3_sys_color_light_on_tertiary_container</item>
        <item name="colorOnTertiaryFixed">@color/m3_sys_color_on_tertiary_fixed</item>
        <item name="colorOnTertiaryFixedVariant">@color/m3_sys_color_on_tertiary_fixed_variant</item>
        <item name="colorOutline">@color/m3_sys_color_light_outline</item>
        <item name="colorOutlineVariant">@color/m3_sys_color_light_outline_variant</item>
        <item name="colorPrimary">@color/m3_sys_color_light_primary</item>
        <item name="colorPrimaryContainer">@color/m3_sys_color_light_primary_container</item>
        <item name="colorPrimaryDark">?attr/colorPrimary</item>
        <item name="colorPrimaryFixed">@color/m3_sys_color_primary_fixed</item>
        <item name="colorPrimaryFixedDim">@color/m3_sys_color_primary_fixed_dim</item>
        <item name="colorPrimaryInverse">@color/m3_sys_color_light_inverse_primary</item>
        <item name="colorPrimaryVariant">?attr/colorPrimary</item>
        <item name="colorSecondary">@color/m3_sys_color_light_secondary</item>
        <item name="colorSecondaryContainer">@color/m3_sys_color_light_secondary_container</item>
        <item name="colorSecondaryFixed">@color/m3_sys_color_secondary_fixed</item>
        <item name="colorSecondaryFixedDim">@color/m3_sys_color_secondary_fixed_dim</item>
        <item name="colorSecondaryVariant">?attr/colorSecondary</item>
        <item name="colorSurface">@color/m3_sys_color_light_surface</item>
        <item name="colorSurfaceBright">@color/m3_sys_color_light_surface_bright</item>
        <item name="colorSurfaceContainer">@color/m3_sys_color_light_surface_container</item>
        <item name="colorSurfaceContainerHigh">@color/m3_sys_color_light_surface_container_high</item>
        <item name="colorSurfaceContainerHighest">@color/m3_sys_color_light_surface_container_highest</item>
        <item name="colorSurfaceContainerLow">@color/m3_sys_color_light_surface_container_low</item>
        <item name="colorSurfaceContainerLowest">@color/m3_sys_color_light_surface_container_lowest</item>
        <item name="colorSurfaceDim">@color/m3_sys_color_light_surface_dim</item>
        <item name="colorSurfaceInverse">@color/m3_sys_color_light_inverse_surface</item>
        <item name="colorSurfaceVariant">@color/m3_sys_color_light_surface_variant</item>
        <item name="colorTertiary">@color/m3_sys_color_light_tertiary</item>
        <item name="colorTertiaryContainer">@color/m3_sys_color_light_tertiary_container</item>
        <item name="colorTertiaryFixed">@color/m3_sys_color_tertiary_fixed</item>
        <item name="colorTertiaryFixedDim">@color/m3_sys_color_tertiary_fixed_dim</item>
        <item name="drawerLayoutStyle">@style/Widget.Material3.DrawerLayout</item>
        <item name="dynamicColorThemeOverlay">@style/ThemeOverlay.Material3.DynamicColors.Light</item>
        <item name="elevationOverlayColor">?attr/colorPrimary</item>
        <item name="elevationOverlayEnabled">true</item>
        <item name="extendedFloatingActionButtonPrimaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary</item>
        <item name="extendedFloatingActionButtonSecondaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary</item>
        <item name="extendedFloatingActionButtonStyle">?attr/extendedFloatingActionButtonPrimaryStyle</item>
        <item name="extendedFloatingActionButtonSurfaceStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface</item>
        <item name="extendedFloatingActionButtonTertiaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary</item>
        <item name="floatingActionButtonLargePrimaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Primary</item>
        <item name="floatingActionButtonLargeSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Secondary</item>
        <item name="floatingActionButtonLargeStyle">?attr/floatingActionButtonLargePrimaryStyle</item>
        <item name="floatingActionButtonLargeSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Large.Surface</item>
        <item name="floatingActionButtonLargeTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Tertiary</item>
        <item name="floatingActionButtonPrimaryStyle">@style/Widget.Material3.FloatingActionButton.Primary</item>
        <item name="floatingActionButtonSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Secondary</item>
        <item name="floatingActionButtonSmallPrimaryStyle">@style/Widget.Material3.FloatingActionButton.Small.Primary</item>
        <item name="floatingActionButtonSmallSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Small.Secondary</item>
        <item name="floatingActionButtonSmallStyle">?attr/floatingActionButtonSmallPrimaryStyle</item>
        <item name="floatingActionButtonSmallSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Small.Surface</item>
        <item name="floatingActionButtonSmallTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Small.Tertiary</item>
        <item name="floatingActionButtonStyle">?attr/floatingActionButtonPrimaryStyle</item>
        <item name="floatingActionButtonSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Surface</item>
        <item name="floatingActionButtonTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Tertiary</item>
        <item name="isMaterial3Theme">true</item>
        <item name="linearProgressIndicatorStyle">@style/Widget.Material3.LinearProgressIndicator</item>
        <item name="listPopupWindowStyle">@style/Widget.Material3.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.Material3.MaterialAlertDialog</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.Material3.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.Material3.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.Material3.MaterialButtonToggleGroup</item>
        <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.Material3.MaterialCalendar</item>
        <item name="materialCalendarTheme">@style/ThemeOverlay.Material3.MaterialCalendar</item>
        <item name="materialCardViewElevatedStyle">@style/Widget.Material3.CardView.Elevated</item>
        <item name="materialCardViewFilledStyle">@style/Widget.Material3.CardView.Filled</item>
        <item name="materialCardViewOutlinedStyle">@style/Widget.Material3.CardView.Outlined</item>
        <item name="materialCardViewStyle">?attr/materialCardViewOutlinedStyle</item>
        <item name="materialDividerHeavyStyle">@style/Widget.Material3.MaterialDivider.Heavy</item>
        <item name="materialDividerStyle">@style/Widget.Material3.MaterialDivider</item>
        <item name="materialIconButtonFilledStyle">@style/Widget.Material3.Button.IconButton.Filled</item>
        <item name="materialIconButtonFilledTonalStyle">@style/Widget.Material3.Button.IconButton.Filled.Tonal</item>
        <item name="materialIconButtonOutlinedStyle">@style/Widget.Material3.Button.IconButton.Outlined</item>
        <item name="materialIconButtonStyle">@style/Widget.Material3.Button.IconButton</item>
        <item name="materialSearchBarStyle">@style/Widget.Material3.SearchBar</item>
        <item name="materialSearchViewPrefixStyle">@style/Widget.Material3.SearchView.Prefix</item>
        <item name="materialSearchViewStyle">@style/Widget.Material3.SearchView</item>
        <item name="materialSearchViewToolbarHeight">@dimen/m3_searchview_height</item>
        <item name="materialSearchViewToolbarStyle">@style/Widget.Material3.SearchView.Toolbar</item>
        <item name="materialSwitchStyle">@style/Widget.Material3.CompoundButton.MaterialSwitch</item>
        <item name="materialTimePickerTheme">@style/ThemeOverlay.Material3.MaterialTimePicker</item>
        <item name="motionDurationExtraLong1">@integer/m3_sys_motion_duration_extra_long1</item>
        <item name="motionDurationExtraLong2">@integer/m3_sys_motion_duration_extra_long2</item>
        <item name="motionDurationExtraLong3">@integer/m3_sys_motion_duration_extra_long3</item>
        <item name="motionDurationExtraLong4">@integer/m3_sys_motion_duration_extra_long4</item>
        <item name="motionDurationLong1">@integer/m3_sys_motion_duration_long1</item>
        <item name="motionDurationLong2">@integer/m3_sys_motion_duration_long2</item>
        <item name="motionDurationLong3">@integer/m3_sys_motion_duration_long3</item>
        <item name="motionDurationLong4">@integer/m3_sys_motion_duration_long4</item>
        <item name="motionDurationMedium1">@integer/m3_sys_motion_duration_medium1</item>
        <item name="motionDurationMedium2">@integer/m3_sys_motion_duration_medium2</item>
        <item name="motionDurationMedium3">@integer/m3_sys_motion_duration_medium3</item>
        <item name="motionDurationMedium4">@integer/m3_sys_motion_duration_medium4</item>
        <item name="motionDurationShort1">@integer/m3_sys_motion_duration_short1</item>
        <item name="motionDurationShort2">@integer/m3_sys_motion_duration_short2</item>
        <item name="motionDurationShort3">@integer/m3_sys_motion_duration_short3</item>
        <item name="motionDurationShort4">@integer/m3_sys_motion_duration_short4</item>
        <item name="motionEasingAccelerated">@string/m3_sys_motion_easing_legacy_accelerate</item>
        <item name="motionEasingDecelerated">@string/m3_sys_motion_easing_legacy_decelerate</item>
        <item name="motionEasingEmphasized">@string/m3_sys_motion_easing_emphasized</item>
        <item name="motionEasingEmphasizedAccelerateInterpolator">@interpolator/m3_sys_motion_easing_emphasized_accelerate</item>
        <item name="motionEasingEmphasizedDecelerateInterpolator">@interpolator/m3_sys_motion_easing_emphasized_decelerate</item>
        <item name="motionEasingEmphasizedInterpolator">@interpolator/m3_sys_motion_easing_emphasized</item>
        <item name="motionEasingLinear">@string/m3_sys_motion_easing_linear</item>
        <item name="motionEasingLinearInterpolator">@interpolator/m3_sys_motion_easing_linear</item>
        <item name="motionEasingStandard">@string/m3_sys_motion_easing_standard</item>
        <item name="motionEasingStandardAccelerateInterpolator">@interpolator/m3_sys_motion_easing_standard_accelerate</item>
        <item name="motionEasingStandardDecelerateInterpolator">@interpolator/m3_sys_motion_easing_standard_decelerate</item>
        <item name="motionEasingStandardInterpolator">@interpolator/m3_sys_motion_easing_standard</item>
        <item name="motionPath">@integer/m3_sys_motion_path</item>
        <item name="navigationRailStyle">@style/Widget.Material3.NavigationRailView</item>
        <item name="navigationViewStyle">@style/Widget.Material3.NavigationView</item>
        <item name="popupMenuBackground">@drawable/m3_popupmenu_background_overlay</item>
        <item name="popupMenuStyle">@style/Widget.Material3.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.Material3.CompoundButton.RadioButton</item>
        <item name="shapeAppearanceCornerExtraLarge">@style/ShapeAppearance.Material3.Corner.ExtraLarge</item>
        <item name="shapeAppearanceCornerExtraSmall">@style/ShapeAppearance.Material3.Corner.ExtraSmall</item>
        <item name="shapeAppearanceCornerLarge">@style/ShapeAppearance.Material3.Corner.Large</item>
        <item name="shapeAppearanceCornerMedium">@style/ShapeAppearance.Material3.Corner.Medium</item>
        <item name="shapeAppearanceCornerSmall">@style/ShapeAppearance.Material3.Corner.Small</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.Material3.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.Material3.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.Material3.SmallComponent</item>
        <item name="shapeCornerFamily">@integer/m3_sys_shape_corner_full_corner_family</item>
        <item name="sideSheetDialogTheme">@style/ThemeOverlay.Material3.SideSheetDialog</item>
        <item name="sliderStyle">@style/Widget.Material3.Slider</item>
        <item name="snackbarButtonStyle">@style/Widget.Material3.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.Material3.Snackbar</item>
        <item name="snackbarTextViewStyle">@style/Widget.Material3.Snackbar.TextView</item>
        <item name="switchStyle">@style/Widget.Material3.CompoundButton.Switch</item>
        <item name="tabSecondaryStyle">@style/Widget.Material3.TabLayout.Secondary</item>
        <item name="tabStyle">@style/Widget.Material3.TabLayout</item>
        <item name="textAppearanceBodyLarge">@style/TextAppearance.Material3.BodyLarge</item>
        <item name="textAppearanceBodyMedium">@style/TextAppearance.Material3.BodyMedium</item>
        <item name="textAppearanceBodySmall">@style/TextAppearance.Material3.BodySmall</item>
        <item name="textAppearanceDisplayLarge">@style/TextAppearance.Material3.DisplayLarge</item>
        <item name="textAppearanceDisplayMedium">@style/TextAppearance.Material3.DisplayMedium</item>
        <item name="textAppearanceDisplaySmall">@style/TextAppearance.Material3.DisplaySmall</item>
        <item name="textAppearanceHeadlineLarge">@style/TextAppearance.Material3.HeadlineLarge</item>
        <item name="textAppearanceHeadlineMedium">@style/TextAppearance.Material3.HeadlineMedium</item>
        <item name="textAppearanceHeadlineSmall">@style/TextAppearance.Material3.HeadlineSmall</item>
        <item name="textAppearanceLabelLarge">@style/TextAppearance.Material3.LabelLarge</item>
        <item name="textAppearanceLabelMedium">@style/TextAppearance.Material3.LabelMedium</item>
        <item name="textAppearanceLabelSmall">@style/TextAppearance.Material3.LabelSmall</item>
        <item name="textAppearanceLargePopupMenu">?attr/textAppearanceBodyLarge</item>
        <item name="textAppearanceListItem">?attr/textAppearanceTitleMedium</item>
        <item name="textAppearanceListItemSecondary">?attr/textAppearanceBodyMedium</item>
        <item name="textAppearanceListItemSmall">?attr/textAppearanceTitleMedium</item>
        <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceBodyLarge</item>
        <item name="textAppearanceTitleLarge">@style/TextAppearance.Material3.TitleLarge</item>
        <item name="textAppearanceTitleMedium">@style/TextAppearance.Material3.TitleMedium</item>
        <item name="textAppearanceTitleSmall">@style/TextAppearance.Material3.TitleSmall</item>
        <item name="textInputFilledDenseStyle">@style/Widget.Material3.TextInputLayout.FilledBox.Dense</item>
        <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
        <item name="textInputFilledStyle">@style/Widget.Material3.TextInputLayout.FilledBox</item>
        <item name="textInputOutlinedDenseStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox.Dense</item>
        <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
        <item name="textInputOutlinedStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox</item>
        <item name="textInputStyle">?attr/textInputOutlinedStyle</item>
        <item name="toolbarStyle">@style/Widget.Material3.Toolbar</item>
        <item name="toolbarSurfaceStyle">@style/Widget.Material3.Toolbar.Surface</item>
    </style>
    <style name="Base.V14.Theme.Material3.Light.BottomSheetDialog" parent="@style/Theme.Material3.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowAnimationStyle">@style/Animation.Material3.BottomSheetDialog</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
        <item name="bottomSheetStyle">@style/Widget.Material3.BottomSheet.Modal</item>
    </style>
    <style name="Base.V14.Theme.Material3.Light.Dialog" parent="@style/Theme.MaterialComponents.Light.Dialog">
        <item name="android:colorBackground">@color/m3_sys_color_light_background</item>
        <item name="android:backgroundDimAmount">@dimen/m3_comp_scrim_container_opacity</item>
        <item name="android:textColorPrimary">@color/m3_default_color_primary_text</item>
        <item name="android:textColorPrimaryDisableOnly">@color/m3_primary_text_disable_only</item>
        <item name="android:textColorSecondary">@color/m3_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverse">@color/m3_dark_default_color_primary_text</item>
        <item name="android:textColorSecondaryInverse">@color/m3_dark_default_color_secondary_text</item>
        <item name="android:textColorHintInverse">@color/m3_dark_hint_foreground</item>
        <item name="android:textColorHighlight">@color/m3_highlighted_text</item>
        <item name="android:textColorHint">@color/m3_hint_foreground</item>
        <item name="android:textColorLink">?attr/colorPrimary</item>
        <item name="android:textColorTertiary">@color/m3_default_color_secondary_text</item>
        <item name="android:textColorTertiaryInverse">@color/m3_dark_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_dark_primary_text_disable_only</item>
        <item name="android:textColorAlertDialogListItem">@color/m3_default_color_primary_text</item>
        <item name="android:textColorHighlightInverse">@color/m3_dark_highlighted_text</item>
        <item name="android:textColorLinkInverse">?attr/colorPrimaryInverse</item>
        <item name="android:textAppearanceListItem">?attr/textAppearanceTitleMedium</item>
        <item name="android:textAppearanceListItemSmall">?attr/textAppearanceTitleMedium</item>
        <item name="android:textAppearanceListItemSecondary">?attr/textAppearanceBodyMedium</item>
        <item name="actionBarPopupTheme">@style/ThemeOverlay.Material3.Light</item>
        <item name="actionBarSize">@dimen/m3_appbar_size_compact</item>
        <item name="actionBarStyle">@style/Widget.Material3.Light.ActionBar.Solid</item>
        <item name="actionModeStyle">@style/Widget.Material3.ActionMode</item>
        <item name="actionOverflowMenuStyle">@style/Widget.Material3.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.Material3.Dialog.Alert</item>
        <item name="appBarLayoutStyle">@style/Widget.Material3.AppBarLayout</item>
        <item name="badgeStyle">@style/Widget.Material3.Badge</item>
        <item name="borderlessButtonStyle">@style/Widget.Material3.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.Material3.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.Material3.BottomNavigationView</item>
        <item name="bottomSheetDragHandleStyle">@style/Widget.Material3.BottomSheet.DragHandle</item>
        <item name="buttonBarButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog</item>
        <item name="checkboxStyle">@style/Widget.Material3.CompoundButton.CheckBox</item>
        <item name="chipGroupStyle">@style/Widget.Material3.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.Material3.Chip.Input</item>
        <item name="chipStyle">@style/Widget.Material3.Chip.Assist</item>
        <item name="circularProgressIndicatorStyle">@style/Widget.Material3.CircularProgressIndicator</item>
        <item name="collapsingToolbarLayoutLargeSize">@dimen/m3_appbar_size_large</item>
        <item name="collapsingToolbarLayoutLargeStyle">@style/Widget.Material3.CollapsingToolbar.Large</item>
        <item name="collapsingToolbarLayoutMediumSize">@dimen/m3_appbar_size_medium</item>
        <item name="collapsingToolbarLayoutMediumStyle">@style/Widget.Material3.CollapsingToolbar.Medium</item>
        <item name="collapsingToolbarLayoutStyle">@style/Widget.Material3.CollapsingToolbar</item>
        <item name="colorError">@color/m3_sys_color_light_error</item>
        <item name="colorErrorContainer">@color/m3_sys_color_light_error_container</item>
        <item name="colorOnBackground">@color/m3_sys_color_light_on_background</item>
        <item name="colorOnError">@color/m3_sys_color_light_on_error</item>
        <item name="colorOnErrorContainer">@color/m3_sys_color_light_on_error_container</item>
        <item name="colorOnPrimary">@color/m3_sys_color_light_on_primary</item>
        <item name="colorOnPrimaryContainer">@color/m3_sys_color_light_on_primary_container</item>
        <item name="colorOnPrimaryFixed">@color/m3_sys_color_on_primary_fixed</item>
        <item name="colorOnPrimaryFixedVariant">@color/m3_sys_color_on_primary_fixed_variant</item>
        <item name="colorOnSecondary">@color/m3_sys_color_light_on_secondary</item>
        <item name="colorOnSecondaryContainer">@color/m3_sys_color_light_on_secondary_container</item>
        <item name="colorOnSecondaryFixed">@color/m3_sys_color_on_secondary_fixed</item>
        <item name="colorOnSecondaryFixedVariant">@color/m3_sys_color_on_secondary_fixed_variant</item>
        <item name="colorOnSurface">@color/m3_sys_color_light_on_surface</item>
        <item name="colorOnSurfaceInverse">@color/m3_sys_color_light_inverse_on_surface</item>
        <item name="colorOnSurfaceVariant">@color/m3_sys_color_light_on_surface_variant</item>
        <item name="colorOnTertiary">@color/m3_sys_color_light_on_tertiary</item>
        <item name="colorOnTertiaryContainer">@color/m3_sys_color_light_on_tertiary_container</item>
        <item name="colorOnTertiaryFixed">@color/m3_sys_color_on_tertiary_fixed</item>
        <item name="colorOnTertiaryFixedVariant">@color/m3_sys_color_on_tertiary_fixed_variant</item>
        <item name="colorOutline">@color/m3_sys_color_light_outline</item>
        <item name="colorOutlineVariant">@color/m3_sys_color_light_outline_variant</item>
        <item name="colorPrimary">@color/m3_sys_color_light_primary</item>
        <item name="colorPrimaryContainer">@color/m3_sys_color_light_primary_container</item>
        <item name="colorPrimaryDark">?attr/colorPrimary</item>
        <item name="colorPrimaryFixed">@color/m3_sys_color_primary_fixed</item>
        <item name="colorPrimaryFixedDim">@color/m3_sys_color_primary_fixed_dim</item>
        <item name="colorPrimaryInverse">@color/m3_sys_color_light_inverse_primary</item>
        <item name="colorPrimaryVariant">?attr/colorPrimary</item>
        <item name="colorSecondary">@color/m3_sys_color_light_secondary</item>
        <item name="colorSecondaryContainer">@color/m3_sys_color_light_secondary_container</item>
        <item name="colorSecondaryFixed">@color/m3_sys_color_secondary_fixed</item>
        <item name="colorSecondaryFixedDim">@color/m3_sys_color_secondary_fixed_dim</item>
        <item name="colorSecondaryVariant">?attr/colorSecondary</item>
        <item name="colorSurface">@color/m3_sys_color_light_surface</item>
        <item name="colorSurfaceBright">@color/m3_sys_color_light_surface_bright</item>
        <item name="colorSurfaceContainer">@color/m3_sys_color_light_surface_container</item>
        <item name="colorSurfaceContainerHigh">@color/m3_sys_color_light_surface_container_high</item>
        <item name="colorSurfaceContainerHighest">@color/m3_sys_color_light_surface_container_highest</item>
        <item name="colorSurfaceContainerLow">@color/m3_sys_color_light_surface_container_low</item>
        <item name="colorSurfaceContainerLowest">@color/m3_sys_color_light_surface_container_lowest</item>
        <item name="colorSurfaceDim">@color/m3_sys_color_light_surface_dim</item>
        <item name="colorSurfaceInverse">@color/m3_sys_color_light_inverse_surface</item>
        <item name="colorSurfaceVariant">@color/m3_sys_color_light_surface_variant</item>
        <item name="colorTertiary">@color/m3_sys_color_light_tertiary</item>
        <item name="colorTertiaryContainer">@color/m3_sys_color_light_tertiary_container</item>
        <item name="colorTertiaryFixed">@color/m3_sys_color_tertiary_fixed</item>
        <item name="colorTertiaryFixedDim">@color/m3_sys_color_tertiary_fixed_dim</item>
        <item name="drawerLayoutStyle">@style/Widget.Material3.DrawerLayout</item>
        <item name="dynamicColorThemeOverlay">@style/ThemeOverlay.Material3.DynamicColors.Light</item>
        <item name="elevationOverlayColor">?attr/colorPrimary</item>
        <item name="elevationOverlayEnabled">true</item>
        <item name="extendedFloatingActionButtonPrimaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary</item>
        <item name="extendedFloatingActionButtonSecondaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary</item>
        <item name="extendedFloatingActionButtonStyle">?attr/extendedFloatingActionButtonPrimaryStyle</item>
        <item name="extendedFloatingActionButtonSurfaceStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface</item>
        <item name="extendedFloatingActionButtonTertiaryStyle">@style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary</item>
        <item name="floatingActionButtonLargePrimaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Primary</item>
        <item name="floatingActionButtonLargeSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Secondary</item>
        <item name="floatingActionButtonLargeStyle">?attr/floatingActionButtonLargePrimaryStyle</item>
        <item name="floatingActionButtonLargeSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Large.Surface</item>
        <item name="floatingActionButtonLargeTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Large.Tertiary</item>
        <item name="floatingActionButtonPrimaryStyle">@style/Widget.Material3.FloatingActionButton.Primary</item>
        <item name="floatingActionButtonSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Secondary</item>
        <item name="floatingActionButtonSmallPrimaryStyle">@style/Widget.Material3.FloatingActionButton.Small.Primary</item>
        <item name="floatingActionButtonSmallSecondaryStyle">@style/Widget.Material3.FloatingActionButton.Small.Secondary</item>
        <item name="floatingActionButtonSmallStyle">?attr/floatingActionButtonSmallPrimaryStyle</item>
        <item name="floatingActionButtonSmallSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Small.Surface</item>
        <item name="floatingActionButtonSmallTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Small.Tertiary</item>
        <item name="floatingActionButtonStyle">?attr/floatingActionButtonPrimaryStyle</item>
        <item name="floatingActionButtonSurfaceStyle">@style/Widget.Material3.FloatingActionButton.Surface</item>
        <item name="floatingActionButtonTertiaryStyle">@style/Widget.Material3.FloatingActionButton.Tertiary</item>
        <item name="isMaterial3Theme">true</item>
        <item name="linearProgressIndicatorStyle">@style/Widget.Material3.LinearProgressIndicator</item>
        <item name="listPopupWindowStyle">@style/Widget.Material3.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.Material3.MaterialAlertDialog</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.Material3.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.Material3.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.Material3.MaterialButtonToggleGroup</item>
        <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.Material3.MaterialCalendar</item>
        <item name="materialCalendarTheme">@style/ThemeOverlay.Material3.MaterialCalendar</item>
        <item name="materialCardViewElevatedStyle">@style/Widget.Material3.CardView.Elevated</item>
        <item name="materialCardViewFilledStyle">@style/Widget.Material3.CardView.Filled</item>
        <item name="materialCardViewOutlinedStyle">@style/Widget.Material3.CardView.Outlined</item>
        <item name="materialCardViewStyle">?attr/materialCardViewOutlinedStyle</item>
        <item name="materialDividerHeavyStyle">@style/Widget.Material3.MaterialDivider.Heavy</item>
        <item name="materialDividerStyle">@style/Widget.Material3.MaterialDivider</item>
        <item name="materialIconButtonFilledStyle">@style/Widget.Material3.Button.IconButton.Filled</item>
        <item name="materialIconButtonFilledTonalStyle">@style/Widget.Material3.Button.IconButton.Filled.Tonal</item>
        <item name="materialIconButtonOutlinedStyle">@style/Widget.Material3.Button.IconButton.Outlined</item>
        <item name="materialIconButtonStyle">@style/Widget.Material3.Button.IconButton</item>
        <item name="materialSearchBarStyle">@style/Widget.Material3.SearchBar</item>
        <item name="materialSearchViewPrefixStyle">@style/Widget.Material3.SearchView.Prefix</item>
        <item name="materialSearchViewStyle">@style/Widget.Material3.SearchView</item>
        <item name="materialSearchViewToolbarHeight">@dimen/m3_searchview_height</item>
        <item name="materialSearchViewToolbarStyle">@style/Widget.Material3.SearchView.Toolbar</item>
        <item name="materialSwitchStyle">@style/Widget.Material3.CompoundButton.MaterialSwitch</item>
        <item name="materialTimePickerTheme">@style/ThemeOverlay.Material3.MaterialTimePicker</item>
        <item name="motionDurationExtraLong1">@integer/m3_sys_motion_duration_extra_long1</item>
        <item name="motionDurationExtraLong2">@integer/m3_sys_motion_duration_extra_long2</item>
        <item name="motionDurationExtraLong3">@integer/m3_sys_motion_duration_extra_long3</item>
        <item name="motionDurationExtraLong4">@integer/m3_sys_motion_duration_extra_long4</item>
        <item name="motionDurationLong1">@integer/m3_sys_motion_duration_long1</item>
        <item name="motionDurationLong2">@integer/m3_sys_motion_duration_long2</item>
        <item name="motionDurationLong3">@integer/m3_sys_motion_duration_long3</item>
        <item name="motionDurationLong4">@integer/m3_sys_motion_duration_long4</item>
        <item name="motionDurationMedium1">@integer/m3_sys_motion_duration_medium1</item>
        <item name="motionDurationMedium2">@integer/m3_sys_motion_duration_medium2</item>
        <item name="motionDurationMedium3">@integer/m3_sys_motion_duration_medium3</item>
        <item name="motionDurationMedium4">@integer/m3_sys_motion_duration_medium4</item>
        <item name="motionDurationShort1">@integer/m3_sys_motion_duration_short1</item>
        <item name="motionDurationShort2">@integer/m3_sys_motion_duration_short2</item>
        <item name="motionDurationShort3">@integer/m3_sys_motion_duration_short3</item>
        <item name="motionDurationShort4">@integer/m3_sys_motion_duration_short4</item>
        <item name="motionEasingAccelerated">@string/m3_sys_motion_easing_legacy_accelerate</item>
        <item name="motionEasingDecelerated">@string/m3_sys_motion_easing_legacy_decelerate</item>
        <item name="motionEasingEmphasized">@string/m3_sys_motion_easing_emphasized</item>
        <item name="motionEasingEmphasizedAccelerateInterpolator">@interpolator/m3_sys_motion_easing_emphasized_accelerate</item>
        <item name="motionEasingEmphasizedDecelerateInterpolator">@interpolator/m3_sys_motion_easing_emphasized_decelerate</item>
        <item name="motionEasingEmphasizedInterpolator">@interpolator/m3_sys_motion_easing_emphasized</item>
        <item name="motionEasingLinear">@string/m3_sys_motion_easing_linear</item>
        <item name="motionEasingLinearInterpolator">@interpolator/m3_sys_motion_easing_linear</item>
        <item name="motionEasingStandard">@string/m3_sys_motion_easing_standard</item>
        <item name="motionEasingStandardAccelerateInterpolator">@interpolator/m3_sys_motion_easing_standard_accelerate</item>
        <item name="motionEasingStandardDecelerateInterpolator">@interpolator/m3_sys_motion_easing_standard_decelerate</item>
        <item name="motionEasingStandardInterpolator">@interpolator/m3_sys_motion_easing_standard</item>
        <item name="motionPath">@integer/m3_sys_motion_path</item>
        <item name="navigationRailStyle">@style/Widget.Material3.NavigationRailView</item>
        <item name="navigationViewStyle">@style/Widget.Material3.NavigationView</item>
        <item name="popupMenuBackground">@drawable/m3_popupmenu_background_overlay</item>
        <item name="popupMenuStyle">@style/Widget.Material3.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.Material3.CompoundButton.RadioButton</item>
        <item name="shapeAppearanceCornerExtraLarge">@style/ShapeAppearance.Material3.Corner.ExtraLarge</item>
        <item name="shapeAppearanceCornerExtraSmall">@style/ShapeAppearance.Material3.Corner.ExtraSmall</item>
        <item name="shapeAppearanceCornerLarge">@style/ShapeAppearance.Material3.Corner.Large</item>
        <item name="shapeAppearanceCornerMedium">@style/ShapeAppearance.Material3.Corner.Medium</item>
        <item name="shapeAppearanceCornerSmall">@style/ShapeAppearance.Material3.Corner.Small</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.Material3.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.Material3.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.Material3.SmallComponent</item>
        <item name="shapeCornerFamily">@integer/m3_sys_shape_corner_full_corner_family</item>
        <item name="sliderStyle">@style/Widget.Material3.Slider</item>
        <item name="snackbarButtonStyle">@style/Widget.Material3.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.Material3.Snackbar</item>
        <item name="snackbarTextViewStyle">@style/Widget.Material3.Snackbar.TextView</item>
        <item name="switchStyle">@style/Widget.Material3.CompoundButton.Switch</item>
        <item name="tabSecondaryStyle">@style/Widget.Material3.TabLayout.Secondary</item>
        <item name="tabStyle">@style/Widget.Material3.TabLayout</item>
        <item name="textAppearanceBodyLarge">@style/TextAppearance.Material3.BodyLarge</item>
        <item name="textAppearanceBodyMedium">@style/TextAppearance.Material3.BodyMedium</item>
        <item name="textAppearanceBodySmall">@style/TextAppearance.Material3.BodySmall</item>
        <item name="textAppearanceDisplayLarge">@style/TextAppearance.Material3.DisplayLarge</item>
        <item name="textAppearanceDisplayMedium">@style/TextAppearance.Material3.DisplayMedium</item>
        <item name="textAppearanceDisplaySmall">@style/TextAppearance.Material3.DisplaySmall</item>
        <item name="textAppearanceHeadlineLarge">@style/TextAppearance.Material3.HeadlineLarge</item>
        <item name="textAppearanceHeadlineMedium">@style/TextAppearance.Material3.HeadlineMedium</item>
        <item name="textAppearanceHeadlineSmall">@style/TextAppearance.Material3.HeadlineSmall</item>
        <item name="textAppearanceLabelLarge">@style/TextAppearance.Material3.LabelLarge</item>
        <item name="textAppearanceLabelMedium">@style/TextAppearance.Material3.LabelMedium</item>
        <item name="textAppearanceLabelSmall">@style/TextAppearance.Material3.LabelSmall</item>
        <item name="textAppearanceLargePopupMenu">?attr/textAppearanceBodyLarge</item>
        <item name="textAppearanceListItem">?attr/textAppearanceTitleMedium</item>
        <item name="textAppearanceListItemSecondary">?attr/textAppearanceBodyMedium</item>
        <item name="textAppearanceListItemSmall">?attr/textAppearanceTitleMedium</item>
        <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceBodyLarge</item>
        <item name="textAppearanceTitleLarge">@style/TextAppearance.Material3.TitleLarge</item>
        <item name="textAppearanceTitleMedium">@style/TextAppearance.Material3.TitleMedium</item>
        <item name="textAppearanceTitleSmall">@style/TextAppearance.Material3.TitleSmall</item>
        <item name="textInputFilledDenseStyle">@style/Widget.Material3.TextInputLayout.FilledBox.Dense</item>
        <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
        <item name="textInputFilledStyle">@style/Widget.Material3.TextInputLayout.FilledBox</item>
        <item name="textInputOutlinedDenseStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox.Dense</item>
        <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
        <item name="textInputOutlinedStyle">@style/Widget.Material3.TextInputLayout.OutlinedBox</item>
        <item name="textInputStyle">?attr/textInputOutlinedStyle</item>
        <item name="toolbarStyle">@style/Widget.Material3.Toolbar</item>
        <item name="toolbarSurfaceStyle">@style/Widget.Material3.Toolbar.Surface</item>
    </style>
    <style name="Base.V14.Theme.Material3.Light.SideSheetDialog" parent="@style/Theme.Material3.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowAnimationStyle">@style/Animation.Material3.SideSheetDialog</item>
        <item name="sideSheetModalStyle">@style/Widget.Material3.SideSheet.Modal</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents" parent="@style/Base.V14.Theme.MaterialComponents.Bridge">
        <item name="android:colorBackground">@color/design_dark_default_color_background</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:timePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="android:datePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="android:contextPopupMenuStyle">@style/Widget.MaterialComponents.PopupMenu.ContextMenu</item>
        <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Dark</item>
        <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
        <item name="actionBarStyle">@style/Widget.MaterialComponents.ActionBar.Surface</item>
        <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.ActionBar.Surface</item>
        <item name="actionModeStyle">@style/Widget.MaterialComponents.ActionMode</item>
        <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Surface</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="bottomSheetDialogTheme">@style/ThemeOverlay.MaterialComponents.BottomSheetDialog</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
        <item name="colorAccent">?attr/colorSecondary</item>
        <item name="colorError">@color/design_dark_default_color_error</item>
        <item name="colorPrimary">@color/design_dark_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_dark_default_color_primary_dark</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>
        <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>
        <item name="materialCalendarTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar</item>
        <item name="materialTimePickerTheme">@style/ThemeOverlay.MaterialComponents.TimePicker</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
        <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="snackbarTextViewStyle">@style/Widget.MaterialComponents.Snackbar.TextView</item>
        <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textAppearanceLargePopupMenu">?attr/textAppearanceSubtitle1</item>
        <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceSubtitle1</item>
        <item name="textInputFilledDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense</item>
        <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
        <item name="textInputFilledStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="textInputOutlinedDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense</item>
        <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
        <item name="textInputOutlinedStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Bridge" parent="@style/Platform.MaterialComponents">
        <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="circularProgressIndicatorStyle">@style/Widget.MaterialComponents.CircularProgressIndicator</item>
        <item name="colorOnBackground">@color/design_dark_default_color_on_background</item>
        <item name="colorOnError">@color/design_dark_default_color_on_error</item>
        <item name="colorOnPrimary">@color/design_dark_default_color_on_primary</item>
        <item name="colorOnPrimarySurface">?attr/colorOnSurface</item>
        <item name="colorOnSecondary">@color/design_dark_default_color_on_secondary</item>
        <item name="colorOnSurface">@color/design_dark_default_color_on_surface</item>
        <item name="colorPrimarySurface">?attr/colorSurface</item>
        <item name="colorPrimaryVariant">@color/design_dark_default_color_primary_variant</item>
        <item name="colorSecondary">@color/design_dark_default_color_secondary</item>
        <item name="colorSecondaryVariant">@color/design_dark_default_color_secondary_variant</item>
        <item name="colorSurface">@color/design_dark_default_color_surface</item>
        <item name="elevationOverlayColor">?attr/colorOnSurface</item>
        <item name="elevationOverlayEnabled">true</item>
        <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
        <item name="isMaterialTheme">true</item>
        <item name="linearProgressIndicatorStyle">@style/Widget.MaterialComponents.LinearProgressIndicator</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>
        <item name="motionDurationLong1">@integer/material_motion_duration_long_1</item>
        <item name="motionDurationLong2">@integer/material_motion_duration_long_2</item>
        <item name="motionDurationMedium1">@integer/material_motion_duration_medium_1</item>
        <item name="motionDurationMedium2">@integer/material_motion_duration_medium_2</item>
        <item name="motionDurationShort1">@integer/material_motion_duration_short_1</item>
        <item name="motionDurationShort2">@integer/material_motion_duration_short_2</item>
        <item name="motionEasingAccelerated">@string/material_motion_easing_accelerated</item>
        <item name="motionEasingDecelerated">@string/material_motion_easing_decelerated</item>
        <item name="motionEasingEmphasized">@string/material_motion_easing_emphasized</item>
        <item name="motionEasingLinear">@string/material_motion_easing_linear</item>
        <item name="motionEasingStandard">@string/material_motion_easing_standard</item>
        <item name="motionPath">@integer/material_motion_path</item>
        <item name="navigationRailStyle">@style/Widget.MaterialComponents.NavigationRailView</item>
        <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background_overlay</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.MaterialComponents.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.MaterialComponents.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.MaterialComponents.SmallComponent</item>
        <item name="sliderStyle">@style/Widget.MaterialComponents.Slider</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Dialog" parent="@style/Base.V14.Theme.MaterialComponents.Dialog.Bridge">
        <item name="android:colorBackground">@color/design_dark_default_color_background</item>
        <item name="android:contextPopupMenuStyle">@style/Widget.MaterialComponents.PopupMenu.ContextMenu</item>
        <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Dark</item>
        <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
        <item name="actionBarStyle">@style/Widget.MaterialComponents.ActionBar.Surface</item>
        <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.ActionBar.Surface</item>
        <item name="actionModeStyle">@style/Widget.MaterialComponents.ActionMode</item>
        <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Surface</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
        <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
        <item name="colorAccent">?attr/colorSecondary</item>
        <item name="colorError">@color/design_dark_default_color_error</item>
        <item name="colorPrimary">@color/design_dark_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_dark_default_color_primary_dark</item>
        <item name="colorSecondary">@color/design_dark_default_color_secondary</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
        <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="snackbarTextViewStyle">@style/Widget.MaterialComponents.Snackbar.TextView</item>
        <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textAppearanceLargePopupMenu">?attr/textAppearanceSubtitle1</item>
        <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceSubtitle1</item>
        <item name="textInputFilledDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense</item>
        <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
        <item name="textInputFilledStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="textInputOutlinedDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense</item>
        <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
        <item name="textInputOutlinedStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Dialog.Bridge" parent="@style/Platform.MaterialComponents.Dialog">
        <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="circularProgressIndicatorStyle">@style/Widget.MaterialComponents.CircularProgressIndicator</item>
        <item name="colorOnBackground">@color/design_dark_default_color_on_background</item>
        <item name="colorOnError">@color/design_dark_default_color_on_error</item>
        <item name="colorOnPrimary">@color/design_dark_default_color_on_primary</item>
        <item name="colorOnPrimarySurface">?attr/colorOnSurface</item>
        <item name="colorOnSecondary">@color/design_dark_default_color_on_secondary</item>
        <item name="colorOnSurface">@color/design_dark_default_color_on_surface</item>
        <item name="colorPrimarySurface">?attr/colorSurface</item>
        <item name="colorPrimaryVariant">@color/design_dark_default_color_primary_variant</item>
        <item name="colorSecondary">@color/design_dark_default_color_secondary</item>
        <item name="colorSecondaryVariant">@color/design_dark_default_color_secondary_variant</item>
        <item name="colorSurface">@color/design_dark_default_color_surface</item>
        <item name="elevationOverlayColor">?attr/colorOnSurface</item>
        <item name="elevationOverlayEnabled">true</item>
        <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
        <item name="isMaterialTheme">true</item>
        <item name="linearProgressIndicatorStyle">@style/Widget.MaterialComponents.LinearProgressIndicator</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>
        <item name="motionDurationLong1">@integer/material_motion_duration_long_1</item>
        <item name="motionDurationLong2">@integer/material_motion_duration_long_2</item>
        <item name="motionDurationMedium1">@integer/material_motion_duration_medium_1</item>
        <item name="motionDurationMedium2">@integer/material_motion_duration_medium_2</item>
        <item name="motionDurationShort1">@integer/material_motion_duration_short_1</item>
        <item name="motionDurationShort2">@integer/material_motion_duration_short_2</item>
        <item name="motionEasingAccelerated">@string/material_motion_easing_accelerated</item>
        <item name="motionEasingDecelerated">@string/material_motion_easing_decelerated</item>
        <item name="motionEasingEmphasized">@string/material_motion_easing_emphasized</item>
        <item name="motionEasingLinear">@string/material_motion_easing_linear</item>
        <item name="motionEasingStandard">@string/material_motion_easing_standard</item>
        <item name="motionPath">@integer/material_motion_path</item>
        <item name="navigationRailStyle">@style/Widget.MaterialComponents.NavigationRailView</item>
        <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background_overlay</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.MaterialComponents.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.MaterialComponents.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.MaterialComponents.SmallComponent</item>
        <item name="sliderStyle">@style/Widget.MaterialComponents.Slider</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light" parent="@style/Base.V14.Theme.MaterialComponents.Light.Bridge">
        <item name="android:colorBackground">@color/design_default_color_background</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:timePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="android:datePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="android:contextPopupMenuStyle">@style/Widget.MaterialComponents.PopupMenu.ContextMenu</item>
        <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Light</item>
        <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
        <item name="actionBarStyle">@style/Widget.MaterialComponents.Light.ActionBar.Solid</item>
        <item name="actionModeStyle">@style/Widget.MaterialComponents.ActionMode</item>
        <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Primary</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="bottomSheetDialogTheme">@style/ThemeOverlay.MaterialComponents.BottomSheetDialog</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
        <item name="colorAccent">?attr/colorSecondary</item>
        <item name="colorError">@color/design_default_color_error</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>
        <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>
        <item name="materialCalendarTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar</item>
        <item name="materialTimePickerTheme">@style/ThemeOverlay.MaterialComponents.TimePicker</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
        <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="snackbarTextViewStyle">@style/Widget.MaterialComponents.Snackbar.TextView</item>
        <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textAppearanceLargePopupMenu">?attr/textAppearanceSubtitle1</item>
        <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceSubtitle1</item>
        <item name="textInputFilledDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense</item>
        <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
        <item name="textInputFilledStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="textInputOutlinedDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense</item>
        <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
        <item name="textInputOutlinedStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Bridge" parent="@style/Platform.MaterialComponents.Light">
        <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="circularProgressIndicatorStyle">@style/Widget.MaterialComponents.CircularProgressIndicator</item>
        <item name="colorOnBackground">@color/design_default_color_on_background</item>
        <item name="colorOnError">@color/design_default_color_on_error</item>
        <item name="colorOnPrimary">@color/design_default_color_on_primary</item>
        <item name="colorOnPrimarySurface">?attr/colorOnPrimary</item>
        <item name="colorOnSecondary">@color/design_default_color_on_secondary</item>
        <item name="colorOnSurface">@color/design_default_color_on_surface</item>
        <item name="colorPrimarySurface">?attr/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/design_default_color_primary_variant</item>
        <item name="colorSecondary">@color/design_default_color_secondary</item>
        <item name="colorSecondaryVariant">@color/design_default_color_secondary_variant</item>
        <item name="colorSurface">@color/design_default_color_surface</item>
        <item name="elevationOverlayColor">?attr/colorOnSurface</item>
        <item name="elevationOverlayEnabled">false</item>
        <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
        <item name="isMaterialTheme">true</item>
        <item name="linearProgressIndicatorStyle">@style/Widget.MaterialComponents.LinearProgressIndicator</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>
        <item name="motionDurationLong1">@integer/material_motion_duration_long_1</item>
        <item name="motionDurationLong2">@integer/material_motion_duration_long_2</item>
        <item name="motionDurationMedium1">@integer/material_motion_duration_medium_1</item>
        <item name="motionDurationMedium2">@integer/material_motion_duration_medium_2</item>
        <item name="motionDurationShort1">@integer/material_motion_duration_short_1</item>
        <item name="motionDurationShort2">@integer/material_motion_duration_short_2</item>
        <item name="motionEasingAccelerated">@string/material_motion_easing_accelerated</item>
        <item name="motionEasingDecelerated">@string/material_motion_easing_decelerated</item>
        <item name="motionEasingEmphasized">@string/material_motion_easing_emphasized</item>
        <item name="motionEasingLinear">@string/material_motion_easing_linear</item>
        <item name="motionEasingStandard">@string/material_motion_easing_standard</item>
        <item name="motionPath">@integer/material_motion_path</item>
        <item name="navigationRailStyle">@style/Widget.MaterialComponents.NavigationRailView</item>
        <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.MaterialComponents.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.MaterialComponents.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.MaterialComponents.SmallComponent</item>
        <item name="sliderStyle">@style/Widget.MaterialComponents.Slider</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="@style/Theme.AppCompat.Light.DarkActionBar">
        <item name="colorOnBackground">@color/design_default_color_on_background</item>
        <item name="colorOnError">@color/design_default_color_on_error</item>
        <item name="colorOnPrimary">@color/design_default_color_on_primary</item>
        <item name="colorOnPrimarySurface">?attr/colorOnPrimary</item>
        <item name="colorOnSecondary">@color/design_default_color_on_secondary</item>
        <item name="colorOnSurface">@color/design_default_color_on_surface</item>
        <item name="colorPrimarySurface">?attr/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/design_default_color_primary_variant</item>
        <item name="colorSecondary">@color/design_default_color_secondary</item>
        <item name="colorSecondaryVariant">@color/design_default_color_secondary_variant</item>
        <item name="colorSurface">@color/design_default_color_surface</item>
        <item name="elevationOverlayColor">?attr/colorOnSurface</item>
        <item name="elevationOverlayEnabled">false</item>
        <item name="isMaterialTheme">true</item>
        <item name="motionDurationLong1">@integer/material_motion_duration_long_1</item>
        <item name="motionDurationLong2">@integer/material_motion_duration_long_2</item>
        <item name="motionDurationMedium1">@integer/material_motion_duration_medium_1</item>
        <item name="motionDurationMedium2">@integer/material_motion_duration_medium_2</item>
        <item name="motionDurationShort1">@integer/material_motion_duration_short_1</item>
        <item name="motionDurationShort2">@integer/material_motion_duration_short_2</item>
        <item name="motionEasingAccelerated">@string/material_motion_easing_accelerated</item>
        <item name="motionEasingDecelerated">@string/material_motion_easing_decelerated</item>
        <item name="motionEasingEmphasized">@string/material_motion_easing_emphasized</item>
        <item name="motionEasingLinear">@string/material_motion_easing_linear</item>
        <item name="motionEasingStandard">@string/material_motion_easing_standard</item>
        <item name="motionPath">@integer/material_motion_path</item>
        <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.MaterialComponents.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.MaterialComponents.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.MaterialComponents.SmallComponent</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Dialog" parent="@style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge">
        <item name="android:colorBackground">@color/design_default_color_background</item>
        <item name="android:contextPopupMenuStyle">@style/Widget.MaterialComponents.PopupMenu.ContextMenu</item>
        <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Light</item>
        <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
        <item name="actionBarStyle">@style/Widget.MaterialComponents.Light.ActionBar.Solid</item>
        <item name="actionModeStyle">@style/Widget.MaterialComponents.ActionMode</item>
        <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Primary</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
        <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
        <item name="colorAccent">?attr/colorSecondary</item>
        <item name="colorError">@color/design_default_color_error</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="colorSecondary">@color/design_default_color_secondary</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
        <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="snackbarTextViewStyle">@style/Widget.MaterialComponents.Snackbar.TextView</item>
        <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textAppearanceLargePopupMenu">?attr/textAppearanceSubtitle1</item>
        <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceSubtitle1</item>
        <item name="textInputFilledDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense</item>
        <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
        <item name="textInputFilledStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="textInputOutlinedDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense</item>
        <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
        <item name="textInputOutlinedStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge" parent="@style/Platform.MaterialComponents.Light.Dialog">
        <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="circularProgressIndicatorStyle">@style/Widget.MaterialComponents.CircularProgressIndicator</item>
        <item name="colorOnBackground">@color/design_default_color_on_background</item>
        <item name="colorOnError">@color/design_default_color_on_error</item>
        <item name="colorOnPrimary">@color/design_default_color_on_primary</item>
        <item name="colorOnPrimarySurface">?attr/colorOnPrimary</item>
        <item name="colorOnSecondary">@color/design_default_color_on_secondary</item>
        <item name="colorOnSurface">@color/design_default_color_on_surface</item>
        <item name="colorPrimarySurface">?attr/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/design_default_color_primary_variant</item>
        <item name="colorSecondary">@color/design_default_color_secondary</item>
        <item name="colorSecondaryVariant">@color/design_default_color_secondary_variant</item>
        <item name="colorSurface">@color/design_default_color_surface</item>
        <item name="elevationOverlayColor">?attr/colorOnSurface</item>
        <item name="elevationOverlayEnabled">false</item>
        <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
        <item name="isMaterialTheme">true</item>
        <item name="linearProgressIndicatorStyle">@style/Widget.MaterialComponents.LinearProgressIndicator</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>
        <item name="motionDurationLong1">@integer/material_motion_duration_long_1</item>
        <item name="motionDurationLong2">@integer/material_motion_duration_long_2</item>
        <item name="motionDurationMedium1">@integer/material_motion_duration_medium_1</item>
        <item name="motionDurationMedium2">@integer/material_motion_duration_medium_2</item>
        <item name="motionDurationShort1">@integer/material_motion_duration_short_1</item>
        <item name="motionDurationShort2">@integer/material_motion_duration_short_2</item>
        <item name="motionEasingAccelerated">@string/material_motion_easing_accelerated</item>
        <item name="motionEasingDecelerated">@string/material_motion_easing_decelerated</item>
        <item name="motionEasingEmphasized">@string/material_motion_easing_emphasized</item>
        <item name="motionEasingLinear">@string/material_motion_easing_linear</item>
        <item name="motionEasingStandard">@string/material_motion_easing_standard</item>
        <item name="motionPath">@integer/material_motion_path</item>
        <item name="navigationRailStyle">@style/Widget.MaterialComponents.NavigationRailView</item>
        <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.MaterialComponents.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.MaterialComponents.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.MaterialComponents.SmallComponent</item>
        <item name="sliderStyle">@style/Widget.MaterialComponents.Slider</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.ThemeOverlay.Material3.BottomSheetDialog" parent="@style/Base.ThemeOverlay.Material3.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowAnimationStyle">@style/Animation.Material3.BottomSheetDialog</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
        <item name="bottomSheetStyle">@style/Widget.Material3.BottomSheet.Modal</item>
        <item name="enableEdgeToEdge">true</item>
    </style>
    <style name="Base.V14.ThemeOverlay.Material3.SideSheetDialog" parent="@style/Base.ThemeOverlay.Material3.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowAnimationStyle">@style/Animation.Material3.SideSheetDialog</item>
        <item name="sideSheetModalStyle">@style/Widget.Material3.SideSheet.Modal</item>
    </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog" parent="@style/Base.ThemeOverlay.MaterialComponents.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowAnimationStyle">@style/Animation.MaterialComponents.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.MaterialComponents.BottomSheet.Modal</item>
        <item name="enableEdgeToEdge">true</item>
        <item name="paddingBottomSystemWindowInsets">true</item>
        <item name="paddingLeftSystemWindowInsets">true</item>
        <item name="paddingRightSystemWindowInsets">true</item>
        <item name="paddingTopSystemWindowInsets">true</item>
    </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.Dialog" parent="@style/ThemeOverlay.AppCompat.Dialog">
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
    </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert" parent="@style/ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
    </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog" parent="@style/ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="android:backgroundDimAmount">@dimen/m3_comp_scrim_container_opacity</item>
        <item name="android:checkedTextViewStyle">@style/Widget.MaterialComponents.CheckedTextView</item>
        <item name="alertDialogStyle">@style/MaterialAlertDialog.MaterialComponents</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="buttonBarNegativeButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="buttonBarNeutralButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="materialAlertDialogBodyTextStyle">@style/MaterialAlertDialog.MaterialComponents.Body.Text</item>
        <item name="materialAlertDialogButtonSpacerVisibility">@integer/mtrl_view_invisible</item>
    </style>
    <style name="Base.V14.Widget.MaterialComponents.AutoCompleteTextView" parent="@style/Widget.AppCompat.AutoCompleteTextView">
        <item name="android:textAppearance">?attr/textAppearanceSubtitle1</item>
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingTop">17dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:paddingBottom">17dp</item>
        <item name="android:dropDownVerticalOffset">@dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:popupElevation">@dimen/mtrl_exposed_dropdown_menu_popup_elevation</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
    </style>
    <style name="Base.V21.Theme.AppCompat" parent="@style/Base.V7.Theme.AppCompat">
        <item name="android:colorControlNormal">?attr/colorControlNormal</item>
        <item name="android:colorControlActivated">?attr/colorControlActivated</item>
        <item name="android:colorButtonNormal">?attr/colorButtonNormal</item>
        <item name="android:colorControlHighlight">?attr/colorControlHighlight</item>
        <item name="android:colorPrimary">?attr/colorPrimary</item>
        <item name="android:colorPrimaryDark">?attr/colorPrimaryDark</item>
        <item name="android:colorAccent">?attr/colorAccent</item>
        <item name="actionBarDivider">?android:attr/actionBarDivider</item>
        <item name="actionBarItemBackground">@drawable/abc_action_bar_item_background_material</item>
        <item name="actionBarSize">?android:attr/actionBarSize</item>
        <item name="actionButtonStyle">?android:attr/actionButtonStyle</item>
        <item name="actionModeBackground">?android:attr/actionModeBackground</item>
        <item name="actionModeCloseDrawable">?android:attr/actionModeCloseDrawable</item>
        <item name="borderlessButtonStyle">?android:attr/borderlessButtonStyle</item>
        <item name="buttonStyle">?android:attr/buttonStyle</item>
        <item name="buttonStyleSmall">?android:attr/buttonStyleSmall</item>
        <item name="checkboxStyle">?android:attr/checkboxStyle</item>
        <item name="checkedTextViewStyle">?android:attr/checkedTextViewStyle</item>
        <item name="dividerHorizontal">?android:attr/dividerHorizontal</item>
        <item name="dividerVertical">?android:attr/dividerVertical</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/editTextColor</item>
        <item name="homeAsUpIndicator">?android:attr/homeAsUpIndicator</item>
        <item name="listChoiceBackgroundIndicator">?android:attr/listChoiceBackgroundIndicator</item>
        <item name="listPreferredItemHeightSmall">?android:attr/listPreferredItemHeightSmall</item>
        <item name="radioButtonStyle">?android:attr/radioButtonStyle</item>
        <item name="ratingBarStyle">?android:attr/ratingBarStyle</item>
        <item name="selectableItemBackground">?android:attr/selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:attr/selectableItemBackgroundBorderless</item>
        <item name="spinnerStyle">?android:attr/spinnerStyle</item>
        <item name="textAppearanceLargePopupMenu">?android:attr/textAppearanceLargePopupMenu</item>
        <item name="textAppearanceSmallPopupMenu">?android:attr/textAppearanceSmallPopupMenu</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Dialog" parent="@style/Base.V7.Theme.AppCompat.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Light" parent="@style/Base.V7.Theme.AppCompat.Light">
        <item name="android:colorControlNormal">?attr/colorControlNormal</item>
        <item name="android:colorControlActivated">?attr/colorControlActivated</item>
        <item name="android:colorButtonNormal">?attr/colorButtonNormal</item>
        <item name="android:colorControlHighlight">?attr/colorControlHighlight</item>
        <item name="android:colorPrimary">?attr/colorPrimary</item>
        <item name="android:colorPrimaryDark">?attr/colorPrimaryDark</item>
        <item name="android:colorAccent">?attr/colorAccent</item>
        <item name="actionBarDivider">?android:attr/actionBarDivider</item>
        <item name="actionBarItemBackground">@drawable/abc_action_bar_item_background_material</item>
        <item name="actionBarSize">?android:attr/actionBarSize</item>
        <item name="actionButtonStyle">?android:attr/actionButtonStyle</item>
        <item name="actionModeBackground">?android:attr/actionModeBackground</item>
        <item name="actionModeCloseDrawable">?android:attr/actionModeCloseDrawable</item>
        <item name="borderlessButtonStyle">?android:attr/borderlessButtonStyle</item>
        <item name="buttonStyle">?android:attr/buttonStyle</item>
        <item name="buttonStyleSmall">?android:attr/buttonStyleSmall</item>
        <item name="checkboxStyle">?android:attr/checkboxStyle</item>
        <item name="checkedTextViewStyle">?android:attr/checkedTextViewStyle</item>
        <item name="dividerHorizontal">?android:attr/dividerHorizontal</item>
        <item name="dividerVertical">?android:attr/dividerVertical</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/editTextColor</item>
        <item name="homeAsUpIndicator">?android:attr/homeAsUpIndicator</item>
        <item name="listChoiceBackgroundIndicator">?android:attr/listChoiceBackgroundIndicator</item>
        <item name="listPreferredItemHeightSmall">?android:attr/listPreferredItemHeightSmall</item>
        <item name="radioButtonStyle">?android:attr/radioButtonStyle</item>
        <item name="ratingBarStyle">?android:attr/ratingBarStyle</item>
        <item name="selectableItemBackground">?android:attr/selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:attr/selectableItemBackgroundBorderless</item>
        <item name="spinnerStyle">?android:attr/spinnerStyle</item>
        <item name="textAppearanceLargePopupMenu">?android:attr/textAppearanceLargePopupMenu</item>
        <item name="textAppearanceSmallPopupMenu">?android:attr/textAppearanceSmallPopupMenu</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Light.Dialog" parent="@style/Base.V7.Theme.AppCompat.Light.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.V21.Theme.MaterialComponents" parent="@style/Base.V14.Theme.MaterialComponents">
        <item name="android:alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework</item>
    </style>
    <style name="Base.V21.Theme.MaterialComponents.Dialog" parent="@style/Base.V14.Theme.MaterialComponents.Dialog">
        <item name="android:alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework</item>
    </style>
    <style name="Base.V21.Theme.MaterialComponents.Light" parent="@style/Base.V14.Theme.MaterialComponents.Light">
        <item name="android:alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework</item>
    </style>
    <style name="Base.V21.Theme.MaterialComponents.Light.Dialog" parent="@style/Base.V14.Theme.MaterialComponents.Light.Dialog">
        <item name="android:alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework</item>
    </style>
    <style name="Base.V21.ThemeOverlay.AppCompat.Dialog" parent="@style/Base.V7.ThemeOverlay.AppCompat.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.V21.ThemeOverlay.Material3.BottomSheetDialog" parent="@style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog">
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>
    <style name="Base.V21.ThemeOverlay.Material3.SideSheetDialog" parent="@style/Base.V14.ThemeOverlay.Material3.SideSheetDialog">
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>
    <style name="Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog" parent="@style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog">
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>
    <style name="Base.V22.Theme.AppCompat" parent="@style/Base.V21.Theme.AppCompat">
        <item name="actionModeShareDrawable">?android:attr/actionModeShareDrawable</item>
        <item name="editTextBackground">?android:attr/editTextBackground</item>
    </style>
    <style name="Base.V22.Theme.AppCompat.Light" parent="@style/Base.V21.Theme.AppCompat.Light">
        <item name="actionModeShareDrawable">?android:attr/actionModeShareDrawable</item>
        <item name="editTextBackground">?android:attr/editTextBackground</item>
    </style>
    <style name="Base.V23.Theme.AppCompat" parent="@style/Base.V22.Theme.AppCompat">
        <item name="actionBarItemBackground">?android:attr/actionBarItemBackground</item>
        <item name="actionMenuTextAppearance">?android:attr/actionMenuTextAppearance</item>
        <item name="actionMenuTextColor">?android:attr/actionMenuTextColor</item>
        <item name="actionOverflowButtonStyle">?android:attr/actionOverflowButtonStyle</item>
        <item name="controlBackground">@drawable/abc_control_background_material</item>
        <item name="ratingBarStyleIndicator">?android:attr/ratingBarStyleIndicator</item>
        <item name="ratingBarStyleSmall">?android:attr/ratingBarStyleSmall</item>
    </style>
    <style name="Base.V23.Theme.AppCompat.Light" parent="@style/Base.V22.Theme.AppCompat.Light">
        <item name="actionBarItemBackground">?android:attr/actionBarItemBackground</item>
        <item name="actionMenuTextAppearance">?android:attr/actionMenuTextAppearance</item>
        <item name="actionMenuTextColor">?android:attr/actionMenuTextColor</item>
        <item name="actionOverflowButtonStyle">?android:attr/actionOverflowButtonStyle</item>
        <item name="controlBackground">@drawable/abc_control_background_material</item>
        <item name="ratingBarStyleIndicator">?android:attr/ratingBarStyleIndicator</item>
        <item name="ratingBarStyleSmall">?android:attr/ratingBarStyleSmall</item>
    </style>
    <style name="Base.V24.Theme.Material3.Dark" parent="@style/Base.V14.Theme.Material3.Dark">
        <item name="android:contextPopupMenuStyle">@style/Widget.Material3.PopupMenu.ContextMenu</item>
    </style>
    <style name="Base.V24.Theme.Material3.Dark.Dialog" parent="@style/Base.V14.Theme.Material3.Dark.Dialog">
        <item name="android:contextPopupMenuStyle">@style/Widget.Material3.PopupMenu.ContextMenu</item>
    </style>
    <style name="Base.V24.Theme.Material3.Light" parent="@style/Base.V14.Theme.Material3.Light">
        <item name="android:contextPopupMenuStyle">@style/Widget.Material3.PopupMenu.ContextMenu</item>
    </style>
    <style name="Base.V24.Theme.Material3.Light.Dialog" parent="@style/Base.V14.Theme.Material3.Light.Dialog">
        <item name="android:contextPopupMenuStyle">@style/Widget.Material3.PopupMenu.ContextMenu</item>
    </style>
    <style name="Base.V7.Theme.AppCompat" parent="@style/Platform.AppCompat">
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="android:textViewStyle">@style/Widget.AppCompat.TextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionBarPopupTheme">@null</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.ActionBar.Solid</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.ActionBar.TabBar</item>
        <item name="actionBarTabStyle">@style/Widget.AppCompat.ActionBar.TabView</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.ActionBar.TabText</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>
        <item name="actionModeCloseContentDescription">@string/abc_action_mode_done</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionModeTheme">?attr/actionBarTheme</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.PopupMenu.Overflow</item>
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="checkedTextViewStyle">?android:attr/checkedTextViewStyle</item>
        <item name="colorAccent">@color/accent_material_dark</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorError">@color/error_color_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>
        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>
        <item name="isLightTheme">false</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
        <item name="listDividerAlertDialog">@null</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="popupMenuStyle">@style/Widget.AppCompat.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Large</item>
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Small</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="tooltipForegroundColor">@color/foreground_material_light</item>
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_light</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowNoTitle">false</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Dialog" parent="@style/Base.Theme.AppCompat">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">adjustPan</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="listPreferredItemPaddingLeft">24dp</item>
        <item name="listPreferredItemPaddingRight">24dp</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light" parent="@style/Platform.AppCompat.Light">
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="android:textViewStyle">@style/Widget.AppCompat.TextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionBarPopupTheme">@null</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.Light.ActionBar.Solid</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.Light.ActionBar.TabBar</item>
        <item name="actionBarTabStyle">@style/Widget.AppCompat.Light.ActionBar.TabView</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.Light.ActionBar.TabText</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>
        <item name="actionModeCloseContentDescription">@string/abc_action_mode_done</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionModeTheme">?attr/actionBarTheme</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.Light.PopupMenu.Overflow</item>
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat.Light</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="colorAccent">@color/accent_material_light</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorError">@color/error_color_material_light</item>
        <item name="colorPrimary">@color/primary_material_light</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>
        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>
        <item name="isLightTheme">true</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_light</item>
        <item name="listDividerAlertDialog">@null</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="popupMenuStyle">@style/Widget.AppCompat.Light.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.Light.SearchView</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large</item>
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="tooltipForegroundColor">@color/foreground_material_dark</item>
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_dark</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowNoTitle">false</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light.Dialog" parent="@style/Base.Theme.AppCompat.Light">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">adjustPan</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="listPreferredItemPaddingLeft">24dp</item>
        <item name="listPreferredItemPaddingRight">24dp</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
    </style>
    <style name="Base.V7.ThemeOverlay.AppCompat.Dialog" parent="@style/Base.ThemeOverlay.AppCompat">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">adjustPan</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="listPreferredItemPaddingLeft">24dp</item>
        <item name="listPreferredItemPaddingRight">24dp</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.AutoCompleteTextView" parent="@android:style/Widget.AutoCompleteTextView">
        <item name="android:textAppearance">?android:attr/textAppearanceMediumInverse</item>
        <item name="android:textColor">?attr/editTextColor</item>
        <item name="android:background">?attr/editTextBackground</item>
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.EditText" parent="@android:style/Widget.EditText">
        <item name="android:textAppearance">?android:attr/textAppearanceMediumInverse</item>
        <item name="android:textColor">?attr/editTextColor</item>
        <item name="android:background">?attr/editTextBackground</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.Toolbar" parent="@android:style/Widget">
        <item name="android:paddingLeft">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_default_padding_end_material</item>
        <item name="android:minHeight">?attr/actionBarSize</item>
        <item name="android:paddingStart">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingEnd">@dimen/abc_action_bar_default_padding_end_material</item>
        <item name="buttonGravity">top</item>
        <item name="collapseContentDescription">@string/abc_toolbar_collapse_description</item>
        <item name="collapseIcon">?attr/homeAsUpIndicator</item>
        <item name="contentInsetStart">16dp</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="maxButtonHeight">@dimen/abc_action_bar_default_height_material</item>
        <item name="subtitleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle</item>
        <item name="titleMargin">4dp</item>
        <item name="titleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar" parent="">
        <item name="android:gravity">center_vertical</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="background">@null</item>
        <item name="backgroundSplit">@null</item>
        <item name="backgroundStacked">@null</item>
        <item name="contentInsetEnd">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStart">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="displayOptions">showTitle</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="elevation">@dimen/abc_action_bar_elevation_material</item>
        <item name="height">?attr/actionBarSize</item>
        <item name="popupTheme">?attr/actionBarPopupTheme</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.ActionBar">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabBar" parent="">
        <item name="divider">?attr/actionBarDivider</item>
        <item name="dividerPadding">8dp</item>
        <item name="showDividers">middle</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabText" parent="@android:style/Widget.Material.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabView" parent="@android:style/Widget.Material.ActionBar.TabView">
    </style>
    <style name="Base.Widget.AppCompat.ActionButton" parent="@android:style/Widget.Material.ActionButton">
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.CloseMode" parent="@android:style/Widget.Material.ActionButton.CloseMode">
        <item name="android:minWidth">56dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="@android:style/Widget.Material.ActionButton.Overflow">
    </style>
    <style name="Base.Widget.AppCompat.ActionMode" parent="">
        <item name="android:paddingLeft">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_default_padding_end_material</item>
        <item name="android:minHeight">?attr/actionBarSize</item>
        <item name="android:paddingStart">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingEnd">@dimen/abc_action_bar_default_padding_end_material</item>
        <item name="background">?attr/actionModeBackground</item>
        <item name="backgroundSplit">?attr/actionModeSplitBackground</item>
        <item name="buttonGravity">top</item>
        <item name="closeItemLayout">@layout/abc_action_mode_close_item_material</item>
        <item name="contentInsetStart">16dp</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="height">?attr/actionBarSize</item>
        <item name="maxButtonHeight">@dimen/abc_action_bar_default_height_material</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle</item>
        <item name="titleMargin">4dp</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActivityChooserView" parent="">
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/abc_ab_share_pack_mtrl_alpha</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="dividerPadding">6dp</item>
        <item name="showDividers">middle</item>
    </style>
    <style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="@android:style/Widget.Material.AutoCompleteTextView">
        <item name="android:background">?attr/editTextBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.Button" parent="@android:style/Widget.Material.Button">
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless" parent="@android:style/Widget.Material.Button.Borderless">
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless.Colored" parent="@android:style/Widget.Material.Button.Borderless.Colored">
    </style>
    <style name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="@style/Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:minWidth">64dp</item>
        <item name="android:minHeight">@dimen/abc_alert_dialog_button_bar_height</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Colored" parent="@android:style/Widget.Material.Button.Colored">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Colored</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Small" parent="@android:style/Widget.Material.Button.Small">
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar" parent="@android:style/Widget.Material.ButtonBar">
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.ButtonBar">
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="@android:style/Widget.Material.CompoundButton.CheckBox">
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="@android:style/Widget.Material.CompoundButton.RadioButton">
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.Switch" parent="@android:style/Widget.CompoundButton">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:textOn">@string/abc_capital_on</item>
        <item name="android:textOff">@string/abc_capital_off</item>
        <item name="android:thumb">@drawable/abc_switch_thumb_material</item>
        <item name="showText">false</item>
        <item name="switchPadding">@dimen/abc_switch_padding</item>
        <item name="switchTextAppearance">@style/TextAppearance.AppCompat.Widget.Switch</item>
        <item name="track">@drawable/abc_switch_track_mtrl_alpha</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle" parent="@style/Base.Widget.AppCompat.DrawerArrowToggle.Common">
        <item name="barLength">18dp</item>
        <item name="drawableSize">24dp</item>
        <item name="gapBetweenBars">3dp</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle.Common" parent="">
        <item name="arrowHeadLength">8dp</item>
        <item name="arrowShaftLength">16dp</item>
        <item name="color">?android:attr/textColorSecondary</item>
        <item name="spinBars">true</item>
        <item name="thickness">2dp</item>
    </style>
    <style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="@android:style/Widget.Material.DropDownItem.Spinner">
    </style>
    <style name="Base.Widget.AppCompat.EditText" parent="@android:style/Widget.Material.EditText">
        <item name="android:breakStrategy">simple</item>
        <item name="android:hyphenationFrequency">none</item>
    </style>
    <style name="Base.Widget.AppCompat.ImageButton" parent="@android:style/Widget.Material.ImageButton">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar" parent="@style/Base.Widget.AppCompat.ActionBar">
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.Light.ActionBar">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.ActionBar.TabBar">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="@android:style/Widget.Material.Light.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="@android:style/Widget.Material.Light.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="@android:style/Widget.Material.Light.ActionBar.TabView">
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu" parent="@android:style/Widget.Material.Light.PopupMenu">
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.Light.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4dp</item>
        <item name="android:overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ListMenuView" parent="@android:style/Widget">
        <item name="subMenuArrow">@drawable/abc_ic_arrow_drop_right_black_24dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ListPopupWindow" parent="@android:style/Widget.Material.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.ListView" parent="@android:style/Widget.Material.ListView">
    </style>
    <style name="Base.Widget.AppCompat.ListView.DropDown" parent="@android:style/Widget.Material.ListView.DropDown">
    </style>
    <style name="Base.Widget.AppCompat.ListView.Menu" parent="@style/Base.Widget.AppCompat.ListView">
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu" parent="@android:style/Widget.Material.PopupMenu">
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4dp</item>
        <item name="android:overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupWindow" parent="@android:style/Widget.PopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar" parent="@android:style/Widget.Material.ProgressBar">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="@android:style/Widget.Material.ProgressBar.Horizontal">
    </style>
    <style name="Base.Widget.AppCompat.RatingBar" parent="@android:style/Widget.Material.RatingBar">
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Indicator" parent="@android:style/Widget.Material.RatingBar.Indicator">
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Small" parent="@android:style/Widget.Material.RatingBar.Small">
    </style>
    <style name="Base.Widget.AppCompat.SearchView" parent="@android:style/Widget">
        <item name="closeIcon">@drawable/abc_ic_clear_material</item>
        <item name="commitIcon">@drawable/abc_ic_commit_search_api_mtrl_alpha</item>
        <item name="goIcon">@drawable/abc_ic_go_search_api_material</item>
        <item name="layout">@layout/abc_search_view</item>
        <item name="queryBackground">@drawable/abc_textfield_search_material</item>
        <item name="searchHintIcon">@drawable/abc_ic_search_api_material</item>
        <item name="searchIcon">@drawable/abc_ic_search_api_material</item>
        <item name="submitBackground">@drawable/abc_textfield_search_material</item>
        <item name="suggestionRowLayout">@layout/abc_search_dropdown_item_icons_2line</item>
        <item name="voiceIcon">@drawable/abc_ic_voice_search_api_material</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView.ActionBar" parent="@style/Base.Widget.AppCompat.SearchView">
        <item name="defaultQueryHint">@string/abc_search_hint</item>
        <item name="queryBackground">@null</item>
        <item name="searchHintIcon">@null</item>
        <item name="submitBackground">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar" parent="@android:style/Widget.Material.SeekBar">
    </style>
    <style name="Base.Widget.AppCompat.SeekBar.Discrete" parent="@style/Base.Widget.AppCompat.SeekBar">
        <item name="tickMark">@drawable/abc_seekbar_tick_mark_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner" parent="@android:style/Widget.Material.Spinner">
    </style>
    <style name="Base.Widget.AppCompat.Spinner.Underlined" parent="@android:style/Widget.Material.Spinner.Underlined">
    </style>
    <style name="Base.Widget.AppCompat.TextView" parent="@android:style/Widget.Material.TextView">
        <item name="android:breakStrategy">high_quality</item>
        <item name="android:hyphenationFrequency">none</item>
    </style>
    <style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="@android:style/Widget.Material.TextView.SpinnerItem">
    </style>
    <style name="Base.Widget.AppCompat.Toolbar" parent="@style/Base.V7.Widget.AppCompat.Toolbar">
    </style>
    <style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="@android:style/Widget.Material.Toolbar.Button.Navigation">
    </style>
    <style name="Base.Widget.Design.TabLayout" parent="@android:style/Widget">
        <item name="android:background">@null</item>
        <item name="tabIconTint">@null</item>
        <item name="tabIndicator">@drawable/mtrl_tabs_default_indicator</item>
        <item name="tabIndicatorAnimationDuration">@integer/design_tab_indicator_anim_duration_ms</item>
        <item name="tabIndicatorAnimationMode">linear</item>
        <item name="tabIndicatorColor">?attr/colorAccent</item>
        <item name="tabIndicatorGravity">bottom</item>
        <item name="tabMaxWidth">@dimen/design_tab_max_width</item>
        <item name="tabPaddingEnd">12dp</item>
        <item name="tabPaddingStart">12dp</item>
        <item name="tabRippleColor">?attr/colorControlHighlight</item>
        <item name="tabTextAppearance">@style/TextAppearance.Design.Tab</item>
        <item name="tabTextColor">@null</item>
        <item name="tabUnboundedRipple">false</item>
    </style>
    <style name="Base.Widget.Material3.ActionBar.Solid" parent="@style/Widget.AppCompat.ActionBar.Solid">
        <item name="background">?attr/colorSurfaceContainer</item>
        <item name="backgroundSplit">?attr/colorSurfaceContainer</item>
        <item name="backgroundStacked">?attr/colorSurfaceContainer</item>
        <item name="subtitleTextStyle">@style/TextAppearance.Material3.ActionBar.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.Material3.ActionBar.Title</item>
    </style>
    <style name="Base.Widget.Material3.ActionMode" parent="@style/Widget.AppCompat.ActionMode">
        <item name="background">?attr/colorSurfaceContainer</item>
        <item name="backgroundSplit">?attr/colorSurfaceContainer</item>
        <item name="subtitleTextStyle">@style/TextAppearance.Material3.ActionBar.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.Material3.ActionBar.Title</item>
    </style>
    <style name="Base.Widget.Material3.BottomNavigationView" parent="@style/Widget.MaterialComponents.BottomNavigationView">
        <item name="android:background">?attr/colorSurfaceContainer</item>
        <item name="android:minHeight">@dimen/m3_bottom_nav_min_height</item>
        <item name="backgroundTint">@null</item>
        <item name="elevation">@dimen/m3_comp_navigation_bar_container_elevation</item>
        <item name="itemActiveIndicatorStyle">@style/Widget.Material3.BottomNavigationView.ActiveIndicator</item>
        <item name="itemIconSize">@dimen/m3_comp_navigation_bar_icon_size</item>
        <item name="itemIconTint">@color/m3_navigation_bar_item_with_indicator_icon_tint</item>
        <item name="itemPaddingBottom">@dimen/m3_bottom_nav_item_padding_bottom</item>
        <item name="itemPaddingTop">@dimen/m3_bottom_nav_item_padding_top</item>
        <item name="itemRippleColor">@color/m3_navigation_bar_ripple_color_selector</item>
        <item name="itemTextAppearanceActive">?attr/textAppearanceLabelMedium</item>
        <item name="itemTextAppearanceInactive">?attr/textAppearanceLabelMedium</item>
        <item name="itemTextColor">@color/m3_navigation_bar_item_with_indicator_label_tint</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.BottomNavigationView</item>
        <item name="shapeAppearance">@style/ShapeAppearance.M3.Comp.NavigationBar.Container.Shape</item>
    </style>
    <style name="Base.Widget.Material3.CardView" parent="@style/Widget.MaterialComponents.CardView">
        <item name="android:stateListAnimator">@animator/m3_card_state_list_anim</item>
        <item name="cardElevation">@dimen/m3_card_elevation</item>
        <item name="cardForegroundColor">@color/m3_card_foreground_color</item>
        <item name="checkedIconTint">@color/m3_card_stroke_color</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="rippleColor">@color/m3_card_ripple_color</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerMedium</item>
        <item name="strokeColor">@color/m3_card_stroke_color</item>
        <item name="strokeWidth">@dimen/m3_card_stroke_width</item>
    </style>
    <style name="Base.Widget.Material3.Chip" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="android:textAppearance">?attr/textAppearanceLabelLarge</item>
        <item name="android:textColor">@color/m3_chip_text_color</item>
        <item name="android:stateListAnimator">@animator/m3_chip_state_list_anim</item>
        <item name="checkedIcon">@drawable/ic_m3_chip_checked_circle</item>
        <item name="chipBackgroundColor">@color/m3_chip_background_color</item>
        <item name="chipIconSize">@dimen/m3_chip_icon_size</item>
        <item name="chipStrokeColor">@color/m3_chip_stroke_color</item>
        <item name="chipStrokeWidth">1dp</item>
        <item name="chipSurfaceColor">?attr/colorSurface</item>
        <item name="closeIcon">@drawable/ic_m3_chip_close</item>
        <item name="closeIconTint">@color/m3_chip_text_color</item>
        <item name="enforceTextAppearance">false</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Chip</item>
        <item name="rippleColor">@color/m3_chip_ripple_color</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerSmall</item>
        <item name="shapeAppearanceOverlay">@null</item>
    </style>
    <style name="Base.Widget.Material3.CollapsingToolbar" parent="@style/Widget.MaterialComponents.CollapsingToolbar">
        <item name="android:clipToPadding">false</item>
        <item name="collapsedTitleTextAppearance">?attr/textAppearanceTitleLarge</item>
        <item name="collapsedTitleTextColor">?attr/colorOnSurface</item>
        <item name="expandedTitleMarginBottom">@dimen/m3_appbar_expanded_title_margin_bottom</item>
        <item name="expandedTitleMarginEnd">@dimen/m3_appbar_expanded_title_margin_horizontal</item>
        <item name="expandedTitleMarginStart">@dimen/m3_appbar_expanded_title_margin_horizontal</item>
        <item name="expandedTitleTextColor">?attr/colorOnSurface</item>
        <item name="extraMultilineHeightEnabled">true</item>
        <item name="forceApplySystemWindowInsetTop">true</item>
        <item name="scrimAnimationDuration">?attr/motionDurationMedium2</item>
        <item name="titleCollapseMode">fade</item>
    </style>
    <style name="Base.Widget.Material3.CompoundButton.CheckBox" parent="@style/Widget.MaterialComponents.CompoundButton.CheckBox">
        <item name="android:textAppearance">?attr/textAppearanceBodyMedium</item>
        <item name="buttonIcon">@null</item>
        <item name="buttonIconTint">@color/m3_checkbox_button_icon_tint</item>
        <item name="buttonTint">@color/m3_checkbox_button_tint</item>
        <item name="errorAccessibilityLabel">@string/error_a11y_label</item>
    </style>
    <style name="Base.Widget.Material3.CompoundButton.RadioButton" parent="@style/Widget.MaterialComponents.CompoundButton.RadioButton">
        <item name="android:textAppearance">?attr/textAppearanceBodyMedium</item>
        <item name="buttonTint">@color/m3_radiobutton_button_tint</item>
    </style>
    <style name="Base.Widget.Material3.CompoundButton.Switch" parent="@style/Widget.MaterialComponents.CompoundButton.Switch">
        <item name="android:textAppearance">?attr/textAppearanceBodyMedium</item>
        <item name="thumbTint">@color/m3_switch_thumb_tint</item>
        <item name="trackTint">@color/m3_switch_track_tint</item>
        <item name="trackTintMode">src_in</item>
    </style>
    <style name="Base.Widget.Material3.ExtendedFloatingActionButton" parent="@style/Widget.MaterialComponents.ExtendedFloatingActionButton">
        <item name="android:textAppearance">?attr/textAppearanceLabelLarge</item>
        <item name="android:textColor">@color/m3_fab_efab_foreground_color_selector</item>
        <item name="android:minHeight">@dimen/m3_comp_extended_fab_primary_container_height</item>
        <item name="android:stateListAnimator">@animator/m3_extended_fab_state_list_animator</item>
        <item name="backgroundTint">@color/m3_fab_efab_background_color_selector</item>
        <item name="elevation">@dimen/m3_comp_extended_fab_primary_container_elevation</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="extendMotionSpec">@animator/m3_extended_fab_change_size_expand_motion_spec</item>
        <item name="hideMotionSpec">@animator/m3_extended_fab_hide_motion_spec</item>
        <item name="iconSize">@dimen/m3_comp_extended_fab_primary_icon_size</item>
        <item name="iconTint">@color/m3_fab_efab_foreground_color_selector</item>
        <item name="rippleColor">@color/m3_efab_ripple_color_selector</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerLarge</item>
        <item name="shapeAppearanceOverlay">@null</item>
        <item name="showMotionSpec">@animator/m3_extended_fab_show_motion_spec</item>
        <item name="shrinkMotionSpec">@animator/m3_extended_fab_change_size_collapse_motion_spec</item>
    </style>
    <style name="Base.Widget.Material3.ExtendedFloatingActionButton.Icon" parent="@style/Base.Widget.Material3.ExtendedFloatingActionButton">
        <item name="android:paddingLeft">@dimen/m3_extended_fab_start_padding</item>
        <item name="android:paddingTop">@dimen/m3_extended_fab_top_padding</item>
        <item name="android:paddingRight">@dimen/m3_extended_fab_end_padding</item>
        <item name="android:paddingBottom">@dimen/m3_extended_fab_bottom_padding</item>
        <item name="android:paddingStart">@dimen/m3_extended_fab_start_padding</item>
        <item name="android:paddingEnd">@dimen/m3_extended_fab_end_padding</item>
        <item name="iconPadding">@dimen/m3_extended_fab_icon_padding</item>
    </style>
    <style name="Base.Widget.Material3.FloatingActionButton" parent="@style/Widget.MaterialComponents.FloatingActionButton">
        <item name="backgroundTint">@color/m3_fab_efab_background_color_selector</item>
        <item name="borderWidth">@dimen/m3_fab_border_width</item>
        <item name="elevation">@dimen/m3_comp_fab_primary_container_elevation</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="fabCustomSize">@dimen/m3_comp_fab_primary_container_height</item>
        <item name="hideMotionSpec">@null</item>
        <item name="hoveredFocusedTranslationZ">@dimen/m3_comp_fab_primary_hover_container_elevation</item>
        <item name="maxImageSize">@dimen/m3_comp_fab_primary_icon_size</item>
        <item name="pressedTranslationZ">@dimen/m3_comp_fab_primary_pressed_container_elevation</item>
        <item name="rippleColor">@color/m3_fab_ripple_color_selector</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerLarge</item>
        <item name="shapeAppearanceOverlay">@null</item>
        <item name="showMotionSpec">@null</item>
        <item name="tint">@color/m3_fab_efab_foreground_color_selector</item>
    </style>
    <style name="Base.Widget.Material3.FloatingActionButton.Large" parent="@style/Base.Widget.Material3.FloatingActionButton">
        <item name="fabCustomSize">@dimen/m3_comp_fab_primary_large_container_height</item>
        <item name="maxImageSize">@dimen/m3_comp_fab_primary_large_icon_size</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerExtraLarge</item>
        <item name="shapeAppearanceOverlay">@null</item>
    </style>
    <style name="Base.Widget.Material3.FloatingActionButton.Small" parent="@style/Base.Widget.Material3.FloatingActionButton">
        <item name="fabCustomSize">@dimen/m3_comp_fab_primary_small_container_height</item>
        <item name="maxImageSize">@dimen/m3_comp_fab_primary_small_icon_size</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerMedium</item>
        <item name="shapeAppearanceOverlay">@null</item>
    </style>
    <style name="Base.Widget.Material3.Light.ActionBar.Solid" parent="@style/Widget.AppCompat.Light.ActionBar.Solid">
        <item name="background">?attr/colorSurfaceContainer</item>
        <item name="backgroundSplit">?attr/colorSurfaceContainer</item>
        <item name="backgroundStacked">?attr/colorSurfaceContainer</item>
        <item name="subtitleTextStyle">@style/TextAppearance.Material3.ActionBar.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.Material3.ActionBar.Title</item>
    </style>
    <style name="Base.Widget.Material3.MaterialCalendar.NavigationButton" parent="@style/Widget.Material3.Button.TextButton.Dialog.Flush">
        <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
        <item name="iconTint">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="Base.Widget.Material3.Snackbar" parent="@style/Base.Widget.MaterialComponents.Snackbar">
        <item name="actionTextColorAlpha">@dimen/m3_snackbar_action_text_color_alpha</item>
        <item name="backgroundTint">?attr/colorSurfaceInverse</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Snackbar</item>
    </style>
    <style name="Base.Widget.Material3.TabLayout" parent="@style/Widget.MaterialComponents.TabLayout">
        <item name="android:background">?attr/colorSurface</item>
        <item name="enforceTextAppearance">false</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.TabLayout</item>
        <item name="tabIconTint">@color/m3_tabs_icon_color</item>
        <item name="tabIndicator">@drawable/m3_tabs_rounded_line_indicator</item>
        <item name="tabIndicatorAnimationDuration">?attr/motionDurationLong2</item>
        <item name="tabIndicatorAnimationMode">elastic</item>
        <item name="tabIndicatorColor">?attr/colorPrimary</item>
        <item name="tabIndicatorFullWidth">false</item>
        <item name="tabRippleColor">@color/m3_tabs_ripple_color</item>
        <item name="tabTextAppearance">?attr/textAppearanceTitleSmall</item>
        <item name="tabTextColor">@color/m3_tabs_text_color</item>
    </style>
    <style name="Base.Widget.Material3.TabLayout.OnSurface" parent="@style/Widget.Material3.TabLayout">
        <item name="android:background">@android:color/transparent</item>
    </style>
    <style name="Base.Widget.Material3.TabLayout.Secondary" parent="@style/Widget.Material3.TabLayout">
        <item name="android:background">?attr/colorSurface</item>
        <item name="tabIconTint">@color/m3_tabs_icon_color_secondary</item>
        <item name="tabIndicator">@drawable/m3_tabs_line_indicator</item>
        <item name="tabIndicatorColor">?attr/colorPrimary</item>
        <item name="tabIndicatorFullWidth">true</item>
        <item name="tabRippleColor">@color/m3_tabs_ripple_color_secondary</item>
        <item name="tabTextAppearance">?attr/textAppearanceTitleSmall</item>
        <item name="tabTextColor">@color/m3_tabs_text_color_secondary</item>
    </style>
    <style name="Base.Widget.MaterialComponents.AutoCompleteTextView" parent="@style/Base.V14.Widget.MaterialComponents.AutoCompleteTextView">
        <item name="android:popupBackground">@null</item>
    </style>
    <style name="Base.Widget.MaterialComponents.CheckedTextView" parent="@android:style/Widget.Material.CheckedTextView">
    </style>
    <style name="Base.Widget.MaterialComponents.Chip" parent="@android:style/Widget">
        <item name="android:textAppearance">?attr/textAppearanceBody2</item>
        <item name="android:textColor">@color/mtrl_chip_text_color</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:text">@null</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:checkable">false</item>
        <item name="android:stateListAnimator">@animator/mtrl_chip_state_list_anim</item>
        <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_circle</item>
        <item name="checkedIconVisible">true</item>
        <item name="chipBackgroundColor">@color/mtrl_chip_background_color</item>
        <item name="chipEndPadding">6dp</item>
        <item name="chipIcon">@null</item>
        <item name="chipIconSize">24dp</item>
        <item name="chipIconVisible">true</item>
        <item name="chipMinHeight">32dp</item>
        <item name="chipMinTouchTargetSize">48dp</item>
        <item name="chipStartPadding">4dp</item>
        <item name="chipStrokeColor">?attr/colorOnSurface</item>
        <item name="chipStrokeWidth">0dp</item>
        <item name="chipSurfaceColor">@color/mtrl_chip_surface_color</item>
        <item name="closeIcon">@drawable/ic_mtrl_chip_close_circle</item>
        <item name="closeIconEndPadding">2dp</item>
        <item name="closeIconSize">18dp</item>
        <item name="closeIconStartPadding">2dp</item>
        <item name="closeIconTint">@color/mtrl_chip_close_icon_tint</item>
        <item name="closeIconVisible">true</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="ensureMinTouchTargetSize">true</item>
        <item name="iconEndPadding">0dp</item>
        <item name="iconStartPadding">0dp</item>
        <item name="rippleColor">@color/mtrl_on_surface_ripple_color</item>
        <item name="shapeAppearance">?attr/shapeAppearanceSmallComponent</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.Chip</item>
        <item name="textEndPadding">6dp</item>
        <item name="textStartPadding">8dp</item>
    </style>
    <style name="Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton" parent="@style/Widget.AppCompat.ImageButton">
        <item name="android:background">?attr/actionBarItemBackground</item>
        <item name="android:tint">?attr/colorOnPrimary</item>
    </style>
    <style name="Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton" parent="@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush">
        <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
        <item name="iconTint">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="Base.Widget.MaterialComponents.PopupMenu" parent="@style/Widget.AppCompat.PopupMenu">
        <item name="android:dropDownVerticalOffset">1px</item>
        <item name="overlapAnchor">false</item>
    </style>
    <style name="Base.Widget.MaterialComponents.PopupMenu.ContextMenu" parent="@style/Widget.AppCompat.PopupMenu">
        <item name="android:overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow" parent="@style/Widget.AppCompat.ListPopupWindow">
        <item name="android:popupBackground">?attr/popupMenuBackground</item>
        <item name="android:dropDownVerticalOffset">1dp</item>
        <item name="android:popupElevation">8dp</item>
    </style>
    <style name="Base.Widget.MaterialComponents.PopupMenu.Overflow" parent="@style/Widget.AppCompat.PopupMenu.Overflow">
        <item name="android:dropDownVerticalOffset">1px</item>
    </style>
    <style name="Base.Widget.MaterialComponents.Slider" parent="@android:style/Widget">
        <item name="haloColor">@color/material_slider_halo_color</item>
        <item name="haloRadius">@dimen/mtrl_slider_halo_radius</item>
        <item name="labelStyle">@style/Widget.MaterialComponents.Tooltip</item>
        <item name="minSeparation">0dp</item>
        <item name="thumbColor">@color/material_slider_thumb_color</item>
        <item name="thumbElevation">@dimen/mtrl_slider_thumb_elevation</item>
        <item name="thumbRadius">@dimen/mtrl_slider_thumb_radius</item>
        <item name="tickColorActive">@color/material_slider_active_tick_marks_color</item>
        <item name="tickColorInactive">@color/material_slider_inactive_tick_marks_color</item>
        <item name="tickRadiusActive">@dimen/mtrl_slider_tick_radius</item>
        <item name="tickRadiusInactive">@dimen/mtrl_slider_tick_radius</item>
        <item name="trackColorActive">@color/material_slider_active_track_color</item>
        <item name="trackColorInactive">@color/material_slider_inactive_track_color</item>
        <item name="trackHeight">@dimen/mtrl_slider_track_height</item>
    </style>
    <style name="Base.Widget.MaterialComponents.Snackbar" parent="@style/Widget.Design.Snackbar">
        <item name="android:paddingLeft">@dimen/mtrl_snackbar_padding_horizontal</item>
        <item name="android:paddingRight">@dimen/mtrl_snackbar_padding_horizontal</item>
        <item name="actionTextColorAlpha">@dimen/mtrl_snackbar_action_text_color_alpha</item>
        <item name="backgroundOverlayColorAlpha">@dimen/mtrl_snackbar_background_overlay_color_alpha</item>
    </style>
    <style name="Base.Widget.MaterialComponents.TextInputEditText" parent="@style/Widget.Design.TextInputEditText">
        <item name="android:textAppearance">?attr/textAppearanceSubtitle1</item>
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingTop">17dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:paddingBottom">17dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="textInputLayoutFocusedRectEnabled">true</item>
    </style>
    <style name="Base.Widget.MaterialComponents.TextInputLayout" parent="@style/Widget.Design.TextInputLayout">
        <item name="android:textColorHint">@color/mtrl_indicator_text_color</item>
        <item name="android:maxWidth">@dimen/material_textinput_max_width</item>
        <item name="android:minWidth">@dimen/material_textinput_min_width</item>
        <item name="boxBackgroundColor">@null</item>
        <item name="boxBackgroundMode">outline</item>
        <item name="boxCollapsedPaddingTop">0dp</item>
        <item name="boxStrokeColor">@color/mtrl_outlined_stroke_color</item>
        <item name="boxStrokeErrorColor">@color/mtrl_error</item>
        <item name="boxStrokeWidth">@dimen/mtrl_textinput_box_stroke_width_default</item>
        <item name="boxStrokeWidthFocused">@dimen/mtrl_textinput_box_stroke_width_focused</item>
        <item name="counterOverflowTextAppearance">?attr/textAppearanceCaption</item>
        <item name="counterOverflowTextColor">@color/mtrl_error</item>
        <item name="counterTextAppearance">?attr/textAppearanceCaption</item>
        <item name="counterTextColor">@color/mtrl_indicator_text_color</item>
        <item name="cursorColor">@null</item>
        <item name="cursorErrorColor">@color/mtrl_error</item>
        <item name="endIconTint">@color/mtrl_outlined_icon_tint</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="errorIconDrawable">@drawable/mtrl_ic_error</item>
        <item name="errorIconTint">@color/mtrl_error</item>
        <item name="errorTextAppearance">?attr/textAppearanceCaption</item>
        <item name="errorTextColor">@color/mtrl_error</item>
        <item name="helperTextTextAppearance">?attr/textAppearanceCaption</item>
        <item name="helperTextTextColor">@color/mtrl_indicator_text_color</item>
        <item name="hintTextAppearance">?attr/textAppearanceCaption</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
        <item name="placeholderTextAppearance">?attr/textAppearanceSubtitle1</item>
        <item name="placeholderTextColor">@color/mtrl_indicator_text_color</item>
        <item name="prefixTextAppearance">?attr/textAppearanceSubtitle1</item>
        <item name="prefixTextColor">@color/mtrl_indicator_text_color</item>
        <item name="shapeAppearance">?attr/shapeAppearanceSmallComponent</item>
        <item name="shapeAppearanceOverlay">@null</item>
        <item name="startIconTint">@color/mtrl_outlined_icon_tint</item>
        <item name="suffixTextAppearance">?attr/textAppearanceSubtitle1</item>
        <item name="suffixTextColor">@color/mtrl_indicator_text_color</item>
    </style>
    <style name="Base.Widget.MaterialComponents.TextView" parent="@style/Widget.AppCompat.TextView">
    </style>
    <style name="CardView" parent="@style/Base.CardView">
        <item name="cardBackgroundColor">?android:attr/colorBackgroundFloating</item>
    </style>
    <style name="CardView.Dark" parent="@style/CardView">
        <item name="cardBackgroundColor">@color/cardview_dark_background</item>
    </style>
    <style name="CardView.Light" parent="@style/CardView">
        <item name="cardBackgroundColor">@color/cardview_light_background</item>
    </style>
    <style name="MaterialAlertDialog.Material3" parent="@style/MaterialAlertDialog.MaterialComponents">
        <item name="android:layout">@layout/m3_alert_dialog</item>
        <item name="backgroundTint">?attr/colorSurfaceContainerHigh</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerExtraLarge</item>
    </style>
    <style name="MaterialAlertDialog.Material3.Animation" parent="@style/MaterialAlertDialog.Material3">
        <item name="android:windowEnterAnimation">@anim/m3_motion_fade_enter</item>
        <item name="android:windowExitAnimation">@anim/m3_motion_fade_exit</item>
    </style>
    <style name="MaterialAlertDialog.Material3.Body.Text" parent="">
        <item name="android:textAppearance">?attr/textAppearanceBodyMedium</item>
        <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="MaterialAlertDialog.Material3.Body.Text.CenterStacked" parent="@style/MaterialAlertDialog.Material3.Body.Text">
        <item name="android:gravity">center</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:textAlignment">gravity</item>
    </style>
    <style name="MaterialAlertDialog.Material3.Title.Icon" parent="@style/MaterialAlertDialog.MaterialComponents.Title.Icon">
        <item name="android:theme">@style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon</item>
        <item name="android:layout_width">@dimen/m3_alert_dialog_icon_size</item>
        <item name="android:layout_height">@dimen/m3_alert_dialog_icon_size</item>
        <item name="android:layout_marginRight">@dimen/m3_alert_dialog_icon_margin</item>
        <item name="android:layout_marginEnd">@dimen/m3_alert_dialog_icon_margin</item>
    </style>
    <style name="MaterialAlertDialog.Material3.Title.Icon.CenterStacked" parent="@style/MaterialAlertDialog.Material3.Title.Icon">
        <item name="android:layout_gravity">center</item>
        <item name="android:layout_marginRight">0dp</item>
        <item name="android:layout_marginBottom">@dimen/m3_alert_dialog_icon_margin</item>
        <item name="android:layout_marginEnd">0dp</item>
    </style>
    <style name="MaterialAlertDialog.Material3.Title.Panel" parent="@style/MaterialAlertDialog.MaterialComponents.Title.Panel">
    </style>
    <style name="MaterialAlertDialog.Material3.Title.Panel.CenterStacked" parent="@style/MaterialAlertDialog.Material3.Title.Panel">
        <item name="android:orientation">vertical</item>
    </style>
    <style name="MaterialAlertDialog.Material3.Title.Text" parent="@style/MaterialAlertDialog.MaterialComponents.Title.Text">
        <item name="android:textAppearance">?attr/textAppearanceHeadlineSmall</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
    </style>
    <style name="MaterialAlertDialog.Material3.Title.Text.CenterStacked" parent="@style/MaterialAlertDialog.Material3.Title.Text">
        <item name="android:gravity">center</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:textAlignment">gravity</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents" parent="@style/AlertDialog.AppCompat">
        <item name="android:layout">@layout/mtrl_alert_dialog</item>
        <item name="backgroundInsetBottom">@dimen/mtrl_alert_dialog_background_inset_bottom</item>
        <item name="backgroundInsetEnd">@dimen/mtrl_alert_dialog_background_inset_end</item>
        <item name="backgroundInsetStart">@dimen/mtrl_alert_dialog_background_inset_start</item>
        <item name="backgroundInsetTop">@dimen/mtrl_alert_dialog_background_inset_top</item>
        <item name="backgroundTint">?attr/colorSurface</item>
        <item name="listItemLayout">@layout/mtrl_alert_select_dialog_item</item>
        <item name="multiChoiceItemLayout">@layout/mtrl_alert_select_dialog_multichoice</item>
        <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
        <item name="singleChoiceItemLayout">@layout/mtrl_alert_select_dialog_singlechoice</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Body.Text" parent="@style/TextAppearance.MaterialComponents.Body2">
        <item name="android:textAppearance">?attr/textAppearanceBody2</item>
        <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar" parent="@android:style/Widget.DeviceDefault.DatePicker">
        <item name="android:headerBackground">?attr/colorPrimary</item>
        <item name="android:headerMonthTextAppearance">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text</item>
        <item name="android:headerDayOfMonthTextAppearance">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day</item>
        <item name="android:headerYearTextAppearance">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text</item>
        <item name="android:calendarTextColor">?attr/colorOnSurface</item>
        <item name="android:datePickerMode">calendar</item>
        <item name="backgroundInsetBottom">@dimen/mtrl_alert_dialog_picker_background_inset</item>
        <item name="backgroundInsetTop">@dimen/mtrl_alert_dialog_picker_background_inset</item>
        <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner" parent="@android:style/Widget.DeviceDefault.DatePicker">
        <item name="android:datePickerMode">spinner</item>
        <item name="backgroundInsetBottom">@dimen/mtrl_alert_dialog_picker_background_inset</item>
        <item name="backgroundInsetTop">@dimen/mtrl_alert_dialog_picker_background_inset</item>
        <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Icon" parent="@style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon">
        <item name="android:layout_gravity">start|center_vertical</item>
        <item name="android:layout_marginRight">8dp</item>
        <item name="android:layout_marginEnd">8dp</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked" parent="@style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon">
        <item name="android:layout_gravity">center</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Panel" parent="@style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel">
        <item name="android:orientation">horizontal</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked" parent="@style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel">
        <item name="android:orientation">vertical</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Text" parent="@style/Base.MaterialAlertDialog.MaterialComponents.Title.Text">
        <item name="android:layout_gravity">start|center_vertical</item>
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked" parent="@style/Base.MaterialAlertDialog.MaterialComponents.Title.Text">
        <item name="android:layout_gravity">center</item>
        <item name="android:textAlignment">center</item>
    </style>
    <style name="Platform.AppCompat" parent="@style/Platform.V21.AppCompat">
    </style>
    <style name="Platform.AppCompat.Light" parent="@style/Platform.V21.AppCompat.Light">
    </style>
    <style name="Platform.MaterialComponents" parent="@style/Theme.AppCompat">
    </style>
    <style name="Platform.MaterialComponents.Dialog" parent="@style/Theme.AppCompat.Dialog">
    </style>
    <style name="Platform.MaterialComponents.Light" parent="@style/Theme.AppCompat.Light">
    </style>
    <style name="Platform.MaterialComponents.Light.Dialog" parent="@style/Theme.AppCompat.Light.Dialog">
    </style>
    <style name="Platform.ThemeOverlay.AppCompat" parent="">
        <item name="android:colorControlNormal">?attr/colorControlNormal</item>
        <item name="android:colorControlActivated">?attr/colorControlActivated</item>
        <item name="android:colorButtonNormal">?attr/colorButtonNormal</item>
        <item name="android:colorControlHighlight">?attr/colorControlHighlight</item>
        <item name="android:colorPrimary">?attr/colorPrimary</item>
        <item name="android:colorPrimaryDark">?attr/colorPrimaryDark</item>
        <item name="android:colorAccent">?attr/colorAccent</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Dark" parent="@style/Platform.ThemeOverlay.AppCompat">
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Light" parent="@style/Platform.ThemeOverlay.AppCompat">
    </style>
    <style name="Platform.V21.AppCompat" parent="@android:style/Theme.Material.NoActionBar">
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorLink">?android:attr/colorAccent</item>
        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:textColorLinkInverse">?android:attr/colorAccent</item>
    </style>
    <style name="Platform.V21.AppCompat.Light" parent="@android:style/Theme.Material.Light.NoActionBar">
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorLink">?android:attr/colorAccent</item>
        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:textColorLinkInverse">?android:attr/colorAccent</item>
    </style>
    <style name="Platform.Widget.AppCompat.Spinner" parent="@android:style/Widget.Holo.Spinner">
    </style>
    <style name="RtlOverlay.DialogWindowTitle.AppCompat" parent="@style/Base.DialogWindowTitle.AppCompat">
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" parent="@android:style/Widget">
        <item name="android:layout_gravity">start|center_vertical</item>
        <item name="android:paddingEnd">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" parent="@android:style/Widget">
        <item name="android:layout_marginEnd">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem" parent="@android:style/Widget">
        <item name="android:paddingEnd">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" parent="@android:style/Widget">
        <item name="android:layout_marginStart">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" parent="@android:style/Widget">
        <item name="android:textAlignment">viewEnd</item>
        <item name="android:layout_marginStart">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" parent="@android:style/Widget">
        <item name="android:layout_marginStart">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" parent="@android:style/Widget">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:layout_alignParentStart">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" parent="@android:style/Widget">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:layout_marginStart">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown" parent="@android:style/Widget">
        <item name="android:paddingStart">@dimen/abc_dropdownitem_text_padding_left</item>
        <item name="android:paddingEnd">4dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" parent="@android:style/Widget">
        <item name="android:layout_alignParentStart">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" parent="@android:style/Widget">
        <item name="android:layout_toStartOf">@id/edit_query</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" parent="@android:style/Widget">
        <item name="android:layout_alignParentEnd">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" parent="@style/Base.Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:layout_toStartOf">@android:id/icon2</item>
        <item name="android:layout_toEndOf">@android:id/icon1</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" parent="@android:style/Widget">
        <item name="android:layout_marginStart">@dimen/abc_dropdownitem_text_padding_left</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton" parent="@android:style/Widget">
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" parent="@style/Base.Widget.AppCompat.ActionButton">
        <item name="android:paddingStart">@dimen/abc_action_bar_overflow_padding_start_material</item>
        <item name="android:paddingEnd">@dimen/abc_action_bar_overflow_padding_end_material</item>
    </style>
    <style name="ShapeAppearance.M3.Comp.Badge.Large.Shape" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.Full">
    </style>
    <style name="ShapeAppearance.M3.Comp.Badge.Shape" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.Full">
    </style>
    <style name="ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.None">
    </style>
    <style name="ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.Full">
    </style>
    <style name="ShapeAppearance.M3.Comp.FilledButton.Container.Shape" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.Full">
    </style>
    <style name="ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.Full">
    </style>
    <style name="ShapeAppearance.M3.Comp.NavigationBar.Container.Shape" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.None">
    </style>
    <style name="ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.Full">
    </style>
    <style name="ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.Full">
    </style>
    <style name="ShapeAppearance.M3.Comp.NavigationRail.Container.Shape" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.None">
    </style>
    <style name="ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.Full">
    </style>
    <style name="ShapeAppearance.M3.Comp.SearchBar.Container.Shape" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.Full">
    </style>
    <style name="ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.None">
    </style>
    <style name="ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.None">
    </style>
    <style name="ShapeAppearance.M3.Comp.Switch.Handle.Shape" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.Full">
    </style>
    <style name="ShapeAppearance.M3.Comp.Switch.StateLayer.Shape" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.Full">
    </style>
    <style name="ShapeAppearance.M3.Comp.Switch.Track.Shape" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.Full">
    </style>
    <style name="ShapeAppearance.M3.Comp.TextButton.Container.Shape" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.Full">
    </style>
    <style name="ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge" parent="">
        <item name="cornerFamily">?attr/shapeCornerFamily</item>
        <item name="cornerSize">28dp</item>
    </style>
    <style name="ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall" parent="">
        <item name="cornerFamily">?attr/shapeCornerFamily</item>
        <item name="cornerSize">4dp</item>
    </style>
    <style name="ShapeAppearance.M3.Sys.Shape.Corner.Full" parent="">
        <item name="cornerFamily">?attr/shapeCornerFamily</item>
        <item name="cornerSize">50%</item>
    </style>
    <style name="ShapeAppearance.M3.Sys.Shape.Corner.Large" parent="">
        <item name="cornerFamily">?attr/shapeCornerFamily</item>
        <item name="cornerSize">16dp</item>
    </style>
    <style name="ShapeAppearance.M3.Sys.Shape.Corner.Medium" parent="">
        <item name="cornerFamily">?attr/shapeCornerFamily</item>
        <item name="cornerSize">12dp</item>
    </style>
    <style name="ShapeAppearance.M3.Sys.Shape.Corner.None" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">0dp</item>
    </style>
    <style name="ShapeAppearance.M3.Sys.Shape.Corner.Small" parent="">
        <item name="cornerFamily">?attr/shapeCornerFamily</item>
        <item name="cornerSize">8dp</item>
    </style>
    <style name="ShapeAppearance.Material3.Corner.ExtraLarge" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge">
    </style>
    <style name="ShapeAppearance.Material3.Corner.ExtraSmall" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall">
    </style>
    <style name="ShapeAppearance.Material3.Corner.Full" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.Full">
    </style>
    <style name="ShapeAppearance.Material3.Corner.Large" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.Large">
    </style>
    <style name="ShapeAppearance.Material3.Corner.Medium" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.Medium">
    </style>
    <style name="ShapeAppearance.Material3.Corner.None" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.None">
    </style>
    <style name="ShapeAppearance.Material3.Corner.Small" parent="@style/ShapeAppearance.M3.Sys.Shape.Corner.Small">
    </style>
    <style name="ShapeAppearance.Material3.LargeComponent" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style>
    <style name="ShapeAppearance.Material3.MediumComponent" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style>
    <style name="ShapeAppearance.Material3.NavigationBarView.ActiveIndicator" parent="@style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape">
    </style>
    <style name="ShapeAppearance.Material3.SmallComponent" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">4dp</item>
    </style>
    <style name="ShapeAppearance.Material3.Tooltip" parent="@style/ShapeAppearance.Material3.Corner.Full">
    </style>
    <style name="ShapeAppearance.MaterialComponents" parent="">
        <item name="cornerFamily">rounded</item>
    </style>
    <style name="ShapeAppearance.MaterialComponents.Badge" parent="@style/ShapeAppearance.MaterialComponents">
        <item name="cornerFamily">?attr/shapeCornerFamily</item>
        <item name="cornerSize">50%</item>
    </style>
    <style name="ShapeAppearance.MaterialComponents.LargeComponent" parent="@style/ShapeAppearance.MaterialComponents">
        <item name="cornerSize">@dimen/mtrl_shape_corner_size_large_component</item>
    </style>
    <style name="ShapeAppearance.MaterialComponents.MediumComponent" parent="@style/ShapeAppearance.MaterialComponents">
        <item name="cornerSize">@dimen/mtrl_shape_corner_size_medium_component</item>
    </style>
    <style name="ShapeAppearance.MaterialComponents.SmallComponent" parent="@style/ShapeAppearance.MaterialComponents">
        <item name="cornerSize">@dimen/mtrl_shape_corner_size_small_component</item>
    </style>
    <style name="ShapeAppearance.MaterialComponents.Tooltip" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">4dp</item>
    </style>
    <style name="ShapeAppearanceOverlay.Material3.Button" parent="">
        <item name="cornerSize">50%</item>
    </style>
    <style name="ShapeAppearanceOverlay.Material3.Chip" parent="">
        <item name="cornerSize">8dp</item>
    </style>
    <style name="ShapeAppearanceOverlay.Material3.Corner.Bottom" parent="">
        <item name="cornerSizeTopLeft">0dp</item>
        <item name="cornerSizeTopRight">0dp</item>
    </style>
    <style name="ShapeAppearanceOverlay.Material3.Corner.Left" parent="">
        <item name="cornerSizeBottomRight">0dp</item>
        <item name="cornerSizeTopRight">0dp</item>
    </style>
    <style name="ShapeAppearanceOverlay.Material3.Corner.Right" parent="">
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeTopLeft">0dp</item>
    </style>
    <style name="ShapeAppearanceOverlay.Material3.Corner.Top" parent="">
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
    </style>
    <style name="ShapeAppearanceOverlay.Material3.FloatingActionButton" parent="">
        <item name="cornerSize">@dimen/m3_fab_corner_size</item>
    </style>
    <style name="ShapeAppearanceOverlay.Material3.NavigationView.Item" parent="@style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape">
    </style>
    <style name="ShapeAppearanceOverlay.Material3.SearchBar" parent="@style/ShapeAppearance.M3.Comp.SearchBar.Container.Shape">
    </style>
    <style name="ShapeAppearanceOverlay.Material3.SearchView" parent="@style/ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape">
    </style>
    <style name="ShapeAppearanceOverlay.MaterialAlertDialog.Material3" parent="">
        <item name="cornerSize">@dimen/m3_alert_dialog_corner_size</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.BottomSheet" parent="">
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.Chip" parent="">
        <item name="cornerSize">50%</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton" parent="">
        <item name="cornerSize">@null</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton" parent="">
        <item name="cornerSize">50%</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day" parent="">
        <item name="cornerSize">@dimen/mtrl_calendar_day_corner</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen" parent="">
        <item name="cornerSize">0dp</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year" parent="">
        <item name="cornerSize">@dimen/mtrl_calendar_year_corner</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox" parent="">
        <item name="cornerSizeBottomLeft">@dimen/mtrl_textinput_box_corner_radius_small</item>
        <item name="cornerSizeBottomRight">@dimen/mtrl_textinput_box_corner_radius_small</item>
    </style>
    <style name="TextAppearance.AppCompat" parent="@style/Base.TextAppearance.AppCompat">
    </style>
    <style name="TextAppearance.AppCompat.Body1" parent="@style/Base.TextAppearance.AppCompat.Body1">
    </style>
    <style name="TextAppearance.AppCompat.Body2" parent="@style/Base.TextAppearance.AppCompat.Body2">
    </style>
    <style name="TextAppearance.AppCompat.Button" parent="@style/Base.TextAppearance.AppCompat.Button">
    </style>
    <style name="TextAppearance.AppCompat.Caption" parent="@style/Base.TextAppearance.AppCompat.Caption">
    </style>
    <style name="TextAppearance.AppCompat.Display1" parent="@style/Base.TextAppearance.AppCompat.Display1">
    </style>
    <style name="TextAppearance.AppCompat.Display2" parent="@style/Base.TextAppearance.AppCompat.Display2">
    </style>
    <style name="TextAppearance.AppCompat.Display3" parent="@style/Base.TextAppearance.AppCompat.Display3">
    </style>
    <style name="TextAppearance.AppCompat.Display4" parent="@style/Base.TextAppearance.AppCompat.Display4">
    </style>
    <style name="TextAppearance.AppCompat.Headline" parent="@style/Base.TextAppearance.AppCompat.Headline">
    </style>
    <style name="TextAppearance.AppCompat.Inverse" parent="@style/Base.TextAppearance.AppCompat.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Large" parent="@style/Base.TextAppearance.AppCompat.Large">
    </style>
    <style name="TextAppearance.AppCompat.Large.Inverse" parent="@style/Base.TextAppearance.AppCompat.Large.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" parent="@style/TextAppearance.AppCompat.SearchResult.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Light.SearchResult.Title" parent="@style/TextAppearance.AppCompat.SearchResult.Title">
    </style>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="@style/TextAppearance.AppCompat.Widget.PopupMenu.Large">
    </style>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="@style/TextAppearance.AppCompat.Widget.PopupMenu.Small">
    </style>
    <style name="TextAppearance.AppCompat.Medium" parent="@style/Base.TextAppearance.AppCompat.Medium">
    </style>
    <style name="TextAppearance.AppCompat.Medium.Inverse" parent="@style/Base.TextAppearance.AppCompat.Medium.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Menu" parent="@style/Base.TextAppearance.AppCompat.Menu">
    </style>
    <style name="TextAppearance.AppCompat.SearchResult.Subtitle" parent="@style/Base.TextAppearance.AppCompat.SearchResult.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.SearchResult.Title" parent="@style/Base.TextAppearance.AppCompat.SearchResult.Title">
    </style>
    <style name="TextAppearance.AppCompat.Small" parent="@style/Base.TextAppearance.AppCompat.Small">
    </style>
    <style name="TextAppearance.AppCompat.Small.Inverse" parent="@style/Base.TextAppearance.AppCompat.Small.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Subhead" parent="@style/Base.TextAppearance.AppCompat.Subhead">
    </style>
    <style name="TextAppearance.AppCompat.Subhead.Inverse" parent="@style/Base.TextAppearance.AppCompat.Subhead.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Title" parent="@style/Base.TextAppearance.AppCompat.Title">
    </style>
    <style name="TextAppearance.AppCompat.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Title.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Tooltip" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" parent="@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" parent="@style/TextAppearance.AppCompat.Widget.ActionMode.Title">
    </style>
    <style name="TextAppearance.AppCompat.Widget.Button" parent="@style/Base.TextAppearance.AppCompat.Widget.Button">
    </style>
    <style name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="@style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored">
    </style>
    <style name="TextAppearance.AppCompat.Widget.Button.Colored" parent="@style/Base.TextAppearance.AppCompat.Widget.Button.Colored">
    </style>
    <style name="TextAppearance.AppCompat.Widget.Button.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.Button.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.DropDownItem" parent="@style/Base.TextAppearance.AppCompat.Widget.DropDownItem">
    </style>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header">
    </style>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large">
    </style>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small">
    </style>
    <style name="TextAppearance.AppCompat.Widget.Switch" parent="@style/Base.TextAppearance.AppCompat.Widget.Switch">
    </style>
    <style name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="@style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem">
    </style>
    <style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.Material.Notification">
    </style>
    <style name="TextAppearance.Compat.Notification.Info" parent="@android:style/TextAppearance.Material.Notification.Info">
    </style>
    <style name="TextAppearance.Compat.Notification.Line2" parent="@style/TextAppearance.Compat.Notification.Info">
    </style>
    <style name="TextAppearance.Compat.Notification.Time" parent="@android:style/TextAppearance.Material.Notification.Time">
    </style>
    <style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.Material.Notification.Title">
    </style>
    <style name="TextAppearance.Design.CollapsingToolbar.Expanded" parent="@style/TextAppearance.AppCompat.Display1">
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="TextAppearance.Design.Counter" parent="@style/TextAppearance.AppCompat.Caption">
    </style>
    <style name="TextAppearance.Design.Counter.Overflow" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textColor">@color/design_error</item>
    </style>
    <style name="TextAppearance.Design.Error" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textColor">@color/design_error</item>
    </style>
    <style name="TextAppearance.Design.HelperText" parent="@style/TextAppearance.AppCompat.Caption">
    </style>
    <style name="TextAppearance.Design.Hint" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textColor">?attr/colorControlActivated</item>
    </style>
    <style name="TextAppearance.Design.Placeholder" parent="@style/TextAppearance.AppCompat.Body1">
    </style>
    <style name="TextAppearance.Design.Prefix" parent="@style/TextAppearance.AppCompat.Body1">
    </style>
    <style name="TextAppearance.Design.Snackbar.Message" parent="@android:style/TextAppearance">
        <item name="android:textSize">@dimen/design_snackbar_text_size</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="TextAppearance.Design.Suffix" parent="@style/TextAppearance.AppCompat.Body1">
    </style>
    <style name="TextAppearance.Design.Tab" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textSize">@dimen/design_tab_text_size</item>
        <item name="android:textColor">@color/mtrl_tabs_legacy_text_color_selector</item>
        <item name="textAllCaps">true</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.BodyLarge" parent="@style/TextAppearance.AppCompat.Body2">
        <item name="android:textSize">16sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_plain_regular</item>
        <item name="android:letterSpacing">0.0312</item>
        <item name="fontFamily">@string/m3_ref_typeface_plain_regular</item>
        <item name="lineHeight">24sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.BodyMedium" parent="@style/TextAppearance.AppCompat.Body1">
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_plain_regular</item>
        <item name="android:letterSpacing">0.0179</item>
        <item name="fontFamily">@string/m3_ref_typeface_plain_regular</item>
        <item name="lineHeight">20sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.BodySmall" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">12sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_plain_regular</item>
        <item name="android:letterSpacing">0.0333</item>
        <item name="fontFamily">@string/m3_ref_typeface_plain_regular</item>
        <item name="lineHeight">16sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.DisplayLarge" parent="@style/TextAppearance.AppCompat.Display3">
        <item name="android:textSize">57sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="android:letterSpacing">-0.0044</item>
        <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="lineHeight">64sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.DisplayMedium" parent="@style/TextAppearance.AppCompat.Display2">
        <item name="android:textSize">45sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="android:letterSpacing">0</item>
        <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="lineHeight">52sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.DisplaySmall" parent="@style/TextAppearance.AppCompat.Display1">
        <item name="android:textSize">36sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="android:letterSpacing">0</item>
        <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="lineHeight">44sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.HeadlineLarge" parent="@style/TextAppearance.AppCompat.Display1">
        <item name="android:textSize">32sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="android:letterSpacing">0</item>
        <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="lineHeight">40sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.HeadlineMedium" parent="@style/TextAppearance.AppCompat.Headline">
        <item name="android:textSize">28sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="android:letterSpacing">0</item>
        <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="lineHeight">36sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.HeadlineSmall" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">24sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="android:letterSpacing">0</item>
        <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="lineHeight">32sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.LabelLarge" parent="@style/TextAppearance.AppCompat.Body1">
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="android:letterSpacing">0.0071</item>
        <item name="fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="lineHeight">20sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.LabelMedium" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">12sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="android:letterSpacing">0.0417</item>
        <item name="fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="lineHeight">16sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.LabelSmall" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">11sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="android:letterSpacing">0.0455</item>
        <item name="fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="lineHeight">16sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.TitleLarge" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">22sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="android:letterSpacing">0</item>
        <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="lineHeight">28sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.TitleMedium" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">16sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="android:letterSpacing">0.0094</item>
        <item name="fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="lineHeight">24sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.TitleSmall" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="android:letterSpacing">0.0071</item>
        <item name="fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="lineHeight">20sp</item>
    </style>
    <style name="TextAppearance.Material3.ActionBar.Subtitle" parent="@style/TextAppearance.Material3.TitleMedium">
        <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="TextAppearance.Material3.ActionBar.Title" parent="@style/TextAppearance.Material3.TitleLarge">
        <item name="android:textColor">?attr/colorOnSurface</item>
    </style>
    <style name="TextAppearance.Material3.BodyLarge" parent="@style/TextAppearance.M3.Sys.Typescale.BodyLarge">
    </style>
    <style name="TextAppearance.Material3.BodyMedium" parent="@style/TextAppearance.M3.Sys.Typescale.BodyMedium">
    </style>
    <style name="TextAppearance.Material3.BodySmall" parent="@style/TextAppearance.M3.Sys.Typescale.BodySmall">
    </style>
    <style name="TextAppearance.Material3.DisplayLarge" parent="@style/TextAppearance.M3.Sys.Typescale.DisplayLarge">
    </style>
    <style name="TextAppearance.Material3.DisplayMedium" parent="@style/TextAppearance.M3.Sys.Typescale.DisplayMedium">
    </style>
    <style name="TextAppearance.Material3.DisplaySmall" parent="@style/TextAppearance.M3.Sys.Typescale.DisplaySmall">
    </style>
    <style name="TextAppearance.Material3.HeadlineLarge" parent="@style/TextAppearance.M3.Sys.Typescale.HeadlineLarge">
    </style>
    <style name="TextAppearance.Material3.HeadlineMedium" parent="@style/TextAppearance.M3.Sys.Typescale.HeadlineMedium">
    </style>
    <style name="TextAppearance.Material3.HeadlineSmall" parent="@style/TextAppearance.M3.Sys.Typescale.HeadlineSmall">
    </style>
    <style name="TextAppearance.Material3.LabelLarge" parent="@style/TextAppearance.M3.Sys.Typescale.LabelLarge">
    </style>
    <style name="TextAppearance.Material3.LabelMedium" parent="@style/TextAppearance.M3.Sys.Typescale.LabelMedium">
    </style>
    <style name="TextAppearance.Material3.LabelSmall" parent="@style/TextAppearance.M3.Sys.Typescale.LabelSmall">
    </style>
    <style name="TextAppearance.Material3.MaterialTimePicker.Title" parent="">
        <item name="android:textAppearance">?attr/textAppearanceLabelMedium</item>
        <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="TextAppearance.Material3.SearchBar" parent="@style/Base.TextAppearance.Material3.Search">
        <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="android:textColorHint">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="TextAppearance.Material3.SearchView" parent="@style/Base.TextAppearance.Material3.Search">
        <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="android:textColorHint">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="TextAppearance.Material3.SearchView.Prefix" parent="@style/TextAppearance.Material3.SearchView">
        <item name="android:textColor">?android:attr/textColorTertiary</item>
    </style>
    <style name="TextAppearance.Material3.TitleLarge" parent="@style/TextAppearance.M3.Sys.Typescale.TitleLarge">
    </style>
    <style name="TextAppearance.Material3.TitleMedium" parent="@style/TextAppearance.M3.Sys.Typescale.TitleMedium">
    </style>
    <style name="TextAppearance.Material3.TitleSmall" parent="@style/TextAppearance.M3.Sys.Typescale.TitleSmall">
    </style>
    <style name="TextAppearance.MaterialComponents.Badge" parent="@style/Base.TextAppearance.MaterialComponents.Badge">
        <item name="android:textStyle">normal</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Body1" parent="@style/TextAppearance.AppCompat.Body2">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0312</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Body2" parent="@style/TextAppearance.AppCompat.Body1">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0179</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Button" parent="@style/Base.TextAppearance.MaterialComponents.Button">
        <item name="android:textStyle">normal</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Caption" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0333</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Chip" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/mtrl_chip_text_size</item>
        <item name="android:textColor">@color/mtrl_chip_text_color</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline1" parent="@style/TextAppearance.AppCompat.Display4">
        <item name="android:textSize">96sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:letterSpacing">-0.0156</item>
        <item name="fontFamily">sans-serif-light</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline2" parent="@style/TextAppearance.AppCompat.Display3">
        <item name="android:textSize">60sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:letterSpacing">-0.0083</item>
        <item name="fontFamily">sans-serif-light</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline3" parent="@style/TextAppearance.AppCompat.Display2">
        <item name="android:textSize">48sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline4" parent="@style/TextAppearance.AppCompat.Display1">
        <item name="android:textSize">34sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0074</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline5" parent="@style/TextAppearance.AppCompat.Headline">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline6" parent="@style/Base.TextAppearance.MaterialComponents.Headline6">
        <item name="android:textStyle">normal</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Overline" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">10sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.1667</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Subtitle1" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0094</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Subtitle2" parent="@style/Base.TextAppearance.MaterialComponents.Subtitle2">
        <item name="android:textStyle">normal</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.TimePicker.Title" parent="@style/TextAppearance.MaterialComponents.Overline">
        <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Tooltip" parent="@style/TextAppearance.MaterialComponents.Body2">
        <item name="android:textColor">?attr/colorOnPrimary</item>
    </style>
    <style name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="@style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item">
    </style>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="@style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle">
    </style>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Title" parent="@style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title">
    </style>
    <style name="Theme.ARDrawSketch" parent="@style/Base.Theme.ARDrawSketch">
    </style>
    <style name="Theme.AppCompat" parent="@style/Base.Theme.AppCompat">
    </style>
    <style name="Theme.AppCompat.CompactMenu" parent="@style/Base.Theme.AppCompat.CompactMenu">
    </style>
    <style name="Theme.AppCompat.DayNight" parent="@style/Theme.AppCompat.Light">
    </style>
    <style name="Theme.AppCompat.DayNight.DarkActionBar" parent="@style/Theme.AppCompat.Light.DarkActionBar">
    </style>
    <style name="Theme.AppCompat.DayNight.Dialog" parent="@style/Theme.AppCompat.Light.Dialog">
    </style>
    <style name="Theme.AppCompat.DayNight.Dialog.Alert" parent="@style/Theme.AppCompat.Light.Dialog.Alert">
    </style>
    <style name="Theme.AppCompat.DayNight.Dialog.MinWidth" parent="@style/Theme.AppCompat.Light.Dialog.MinWidth">
    </style>
    <style name="Theme.AppCompat.DayNight.DialogWhenLarge" parent="@style/Theme.AppCompat.Light.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.DayNight.NoActionBar" parent="@style/Theme.AppCompat.Light.NoActionBar">
    </style>
    <style name="Theme.AppCompat.Dialog" parent="@style/Base.Theme.AppCompat.Dialog">
    </style>
    <style name="Theme.AppCompat.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Dialog.Alert">
    </style>
    <style name="Theme.AppCompat.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Dialog.MinWidth">
    </style>
    <style name="Theme.AppCompat.DialogWhenLarge" parent="@style/Base.Theme.AppCompat.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.Empty" parent="">
    </style>
    <style name="Theme.AppCompat.Light" parent="@style/Base.Theme.AppCompat.Light">
    </style>
    <style name="Theme.AppCompat.Light.DarkActionBar" parent="@style/Base.Theme.AppCompat.Light.DarkActionBar">
    </style>
    <style name="Theme.AppCompat.Light.Dialog" parent="@style/Base.Theme.AppCompat.Light.Dialog">
    </style>
    <style name="Theme.AppCompat.Light.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Light.Dialog.Alert">
    </style>
    <style name="Theme.AppCompat.Light.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Light.Dialog.MinWidth">
    </style>
    <style name="Theme.AppCompat.Light.DialogWhenLarge" parent="@style/Base.Theme.AppCompat.Light.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.Light.NoActionBar" parent="@style/Theme.AppCompat.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.AppCompat.NoActionBar" parent="@style/Theme.AppCompat">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Design" parent="@style/Theme.AppCompat">
    </style>
    <style name="Theme.Design.BottomSheetDialog" parent="@style/Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
    </style>
    <style name="Theme.Design.Light" parent="@style/Theme.AppCompat.Light">
    </style>
    <style name="Theme.Design.Light.BottomSheetDialog" parent="@style/Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
    </style>
    <style name="Theme.Design.Light.NoActionBar" parent="@style/Theme.Design.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Design.NoActionBar" parent="@style/Theme.Design">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Material3.Dark" parent="@style/Base.Theme.Material3.Dark">
    </style>
    <style name="Theme.Material3.Dark.BottomSheetDialog" parent="@style/Base.Theme.Material3.Dark.BottomSheetDialog">
    </style>
    <style name="Theme.Material3.Dark.Dialog" parent="@style/Base.Theme.Material3.Dark.Dialog">
    </style>
    <style name="Theme.Material3.Dark.Dialog.Alert" parent="@style/Base.Theme.Material3.Dark.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Theme.Material3.Dark.Dialog.MinWidth" parent="@style/Base.Theme.Material3.Dark.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Theme.Material3.Dark.DialogWhenLarge" parent="@style/Base.Theme.Material3.Dark.DialogWhenLarge">
    </style>
    <style name="Theme.Material3.Dark.NoActionBar" parent="@style/Theme.Material3.Dark">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Material3.Dark.SideSheetDialog" parent="@style/Base.Theme.Material3.Dark.SideSheetDialog">
    </style>
    <style name="Theme.Material3.DayNight" parent="@style/Theme.Material3.Light">
    </style>
    <style name="Theme.Material3.DayNight.BottomSheetDialog" parent="@style/Theme.Material3.Light.BottomSheetDialog">
    </style>
    <style name="Theme.Material3.DayNight.Dialog" parent="@style/Theme.Material3.Light.Dialog">
    </style>
    <style name="Theme.Material3.DayNight.Dialog.Alert" parent="@style/Theme.Material3.Light.Dialog.Alert">
    </style>
    <style name="Theme.Material3.DayNight.Dialog.MinWidth" parent="@style/Theme.Material3.Light.Dialog.MinWidth">
    </style>
    <style name="Theme.Material3.DayNight.DialogWhenLarge" parent="@style/Theme.Material3.Light.DialogWhenLarge">
    </style>
    <style name="Theme.Material3.DayNight.NoActionBar" parent="@style/Theme.Material3.Light.NoActionBar">
    </style>
    <style name="Theme.Material3.DayNight.SideSheetDialog" parent="@style/Theme.Material3.Light.SideSheetDialog">
    </style>
    <style name="Theme.Material3.DynamicColors.Dark" parent="@style/Theme.Material3.Dark">
    </style>
    <style name="Theme.Material3.DynamicColors.Dark.NoActionBar" parent="@style/Theme.Material3.DynamicColors.Dark">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Material3.DynamicColors.DayNight" parent="@style/Theme.Material3.DynamicColors.Light">
    </style>
    <style name="Theme.Material3.DynamicColors.DayNight.NoActionBar" parent="@style/Theme.Material3.DynamicColors.Light.NoActionBar">
    </style>
    <style name="Theme.Material3.DynamicColors.Light" parent="@style/Theme.Material3.Light">
    </style>
    <style name="Theme.Material3.DynamicColors.Light.NoActionBar" parent="@style/Theme.Material3.DynamicColors.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Material3.Light" parent="@style/Base.Theme.Material3.Light">
    </style>
    <style name="Theme.Material3.Light.BottomSheetDialog" parent="@style/Base.Theme.Material3.Light.BottomSheetDialog">
    </style>
    <style name="Theme.Material3.Light.Dialog" parent="@style/Base.Theme.Material3.Light.Dialog">
    </style>
    <style name="Theme.Material3.Light.Dialog.Alert" parent="@style/Base.Theme.Material3.Light.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Theme.Material3.Light.Dialog.MinWidth" parent="@style/Base.Theme.Material3.Light.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Theme.Material3.Light.DialogWhenLarge" parent="@style/Base.Theme.Material3.Light.DialogWhenLarge">
    </style>
    <style name="Theme.Material3.Light.NoActionBar" parent="@style/Theme.Material3.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Material3.Light.SideSheetDialog" parent="@style/Base.Theme.Material3.Light.SideSheetDialog">
    </style>
    <style name="Theme.MaterialComponents" parent="@style/Base.Theme.MaterialComponents">
    </style>
    <style name="Theme.MaterialComponents.BottomSheetDialog" parent="@style/Theme.MaterialComponents.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.MaterialComponents.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.MaterialComponents.BottomSheet.Modal</item>
    </style>
    <style name="Theme.MaterialComponents.Bridge" parent="@style/Base.Theme.MaterialComponents.Bridge">
    </style>
    <style name="Theme.MaterialComponents.CompactMenu" parent="@style/Base.Theme.MaterialComponents.CompactMenu">
    </style>
    <style name="Theme.MaterialComponents.DayNight" parent="@style/Theme.MaterialComponents.Light">
    </style>
    <style name="Theme.MaterialComponents.DayNight.BottomSheetDialog" parent="@style/Theme.MaterialComponents.Light.BottomSheetDialog">
    </style>
    <style name="Theme.MaterialComponents.DayNight.Bridge" parent="@style/Theme.MaterialComponents.Light.Bridge">
    </style>
    <style name="Theme.MaterialComponents.DayNight.DarkActionBar" parent="@style/Theme.MaterialComponents.Light.DarkActionBar">
    </style>
    <style name="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge" parent="@style/Theme.MaterialComponents.Light.DarkActionBar.Bridge">
    </style>
    <style name="Theme.MaterialComponents.DayNight.Dialog" parent="@style/Theme.MaterialComponents.Light.Dialog">
    </style>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Alert" parent="@style/Theme.MaterialComponents.Light.Dialog.Alert">
    </style>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge" parent="@style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge">
    </style>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Bridge" parent="@style/Theme.MaterialComponents.Light.Dialog.Bridge">
    </style>
    <style name="Theme.MaterialComponents.DayNight.Dialog.FixedSize" parent="@style/Theme.MaterialComponents.Light.Dialog.FixedSize">
    </style>
    <style name="Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge" parent="@style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge">
    </style>
    <style name="Theme.MaterialComponents.DayNight.Dialog.MinWidth" parent="@style/Theme.MaterialComponents.Light.Dialog.MinWidth">
    </style>
    <style name="Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge" parent="@style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge">
    </style>
    <style name="Theme.MaterialComponents.DayNight.DialogWhenLarge" parent="@style/Theme.MaterialComponents.Light.DialogWhenLarge">
    </style>
    <style name="Theme.MaterialComponents.DayNight.NoActionBar" parent="@style/Theme.MaterialComponents.Light.NoActionBar">
    </style>
    <style name="Theme.MaterialComponents.DayNight.NoActionBar.Bridge" parent="@style/Theme.MaterialComponents.Light.NoActionBar.Bridge">
    </style>
    <style name="Theme.MaterialComponents.Dialog" parent="@style/Base.Theme.MaterialComponents.Dialog">
    </style>
    <style name="Theme.MaterialComponents.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Dialog.Alert">
    </style>
    <style name="Theme.MaterialComponents.Dialog.Alert.Bridge" parent="@style/Base.Theme.MaterialComponents.Dialog.Bridge">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Theme.MaterialComponents.Dialog.Bridge" parent="@style/Base.Theme.MaterialComponents.Dialog.Bridge">
    </style>
    <style name="Theme.MaterialComponents.Dialog.FixedSize" parent="@style/Base.Theme.MaterialComponents.Dialog.FixedSize">
    </style>
    <style name="Theme.MaterialComponents.Dialog.FixedSize.Bridge" parent="@style/Base.Theme.MaterialComponents.Dialog.Bridge">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Theme.MaterialComponents.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Dialog.MinWidth">
    </style>
    <style name="Theme.MaterialComponents.Dialog.MinWidth.Bridge" parent="@style/Base.Theme.MaterialComponents.Dialog.Bridge">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Theme.MaterialComponents.DialogWhenLarge" parent="@style/Base.Theme.MaterialComponents.DialogWhenLarge">
    </style>
    <style name="Theme.MaterialComponents.Light" parent="@style/Base.Theme.MaterialComponents.Light">
    </style>
    <style name="Theme.MaterialComponents.Light.BottomSheetDialog" parent="@style/Theme.MaterialComponents.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.MaterialComponents.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.MaterialComponents.BottomSheet.Modal</item>
    </style>
    <style name="Theme.MaterialComponents.Light.Bridge" parent="@style/Base.Theme.MaterialComponents.Light.Bridge">
    </style>
    <style name="Theme.MaterialComponents.Light.DarkActionBar" parent="@style/Base.Theme.MaterialComponents.Light.DarkActionBar">
    </style>
    <style name="Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="@style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge">
    </style>
    <style name="Theme.MaterialComponents.Light.Dialog" parent="@style/Base.Theme.MaterialComponents.Light.Dialog">
    </style>
    <style name="Theme.MaterialComponents.Light.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.Alert">
    </style>
    <style name="Theme.MaterialComponents.Light.Dialog.Alert.Bridge" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.Bridge">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Theme.MaterialComponents.Light.Dialog.Bridge" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.Bridge">
    </style>
    <style name="Theme.MaterialComponents.Light.Dialog.FixedSize" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize">
    </style>
    <style name="Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.Bridge">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Theme.MaterialComponents.Light.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth">
    </style>
    <style name="Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.Bridge">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Theme.MaterialComponents.Light.DialogWhenLarge" parent="@style/Base.Theme.MaterialComponents.Light.DialogWhenLarge">
    </style>
    <style name="Theme.MaterialComponents.Light.NoActionBar" parent="@style/Theme.MaterialComponents.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents.Light.NoActionBar.Bridge" parent="@style/Theme.MaterialComponents.Light.Bridge">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents.NoActionBar" parent="@style/Theme.MaterialComponents">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents.NoActionBar.Bridge" parent="@style/Theme.MaterialComponents.Bridge">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Transparent.ImagePicker" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowActionBar">false</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="ThemeOverlay.AppCompat" parent="@style/Base.ThemeOverlay.AppCompat">
    </style>
    <style name="ThemeOverlay.AppCompat.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.ActionBar">
    </style>
    <style name="ThemeOverlay.AppCompat.Dark" parent="@style/Base.ThemeOverlay.AppCompat.Dark">
    </style>
    <style name="ThemeOverlay.AppCompat.Dark.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.Dark.ActionBar">
    </style>
    <style name="ThemeOverlay.AppCompat.DayNight" parent="@style/ThemeOverlay.AppCompat.Light">
    </style>
    <style name="ThemeOverlay.AppCompat.DayNight.ActionBar" parent="@style/ThemeOverlay.AppCompat.DayNight">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="ThemeOverlay.AppCompat.Dialog" parent="@style/Base.ThemeOverlay.AppCompat.Dialog">
    </style>
    <style name="ThemeOverlay.AppCompat.Dialog.Alert" parent="@style/Base.ThemeOverlay.AppCompat.Dialog.Alert">
    </style>
    <style name="ThemeOverlay.AppCompat.Light" parent="@style/Base.ThemeOverlay.AppCompat.Light">
    </style>
    <style name="ThemeOverlay.Design.TextInputEditText" parent="">
    </style>
    <style name="ThemeOverlay.Material3" parent="@style/ThemeOverlay.MaterialComponents">
    </style>
    <style name="ThemeOverlay.Material3.ActionBar" parent="@style/ThemeOverlay.MaterialComponents.ActionBar">
    </style>
    <style name="ThemeOverlay.Material3.AutoCompleteTextView" parent="@style/Base.ThemeOverlay.Material3.AutoCompleteTextView">
        <item name="colorControlNormal">@color/m3_textfield_indicator_text_color</item>
    </style>
    <style name="ThemeOverlay.Material3.AutoCompleteTextView.FilledBox" parent="@style/ThemeOverlay.Material3.AutoCompleteTextView">
        <item name="autoCompleteTextViewStyle">@style/Widget.Material3.AutoCompleteTextView.FilledBox</item>
    </style>
    <style name="ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense" parent="@style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox">
        <item name="autoCompleteTextViewStyle">@style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense</item>
    </style>
    <style name="ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox" parent="@style/ThemeOverlay.Material3.AutoCompleteTextView">
        <item name="autoCompleteTextViewStyle">@style/Widget.Material3.AutoCompleteTextView.OutlinedBox</item>
    </style>
    <style name="ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense" parent="@style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox">
        <item name="autoCompleteTextViewStyle">@style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense</item>
    </style>
    <style name="ThemeOverlay.Material3.BottomAppBar" parent="">
        <item name="actionMenuTextColor">?attr/colorOnSurface</item>
        <item name="colorControlNormal">?attr/colorOnSurface</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.Material3.BottomAppBar.Button.Navigation</item>
    </style>
    <style name="ThemeOverlay.Material3.BottomAppBar.Legacy" parent="">
        <item name="actionMenuTextColor">?attr/colorOnSurface</item>
        <item name="colorControlNormal">?attr/colorOnSurface</item>
    </style>
    <style name="ThemeOverlay.Material3.BottomNavigationView" parent="">
        <item name="badgeStyle">@style/Widget.Material3.BottomNavigation.Badge</item>
    </style>
    <style name="ThemeOverlay.Material3.BottomSheetDialog" parent="@style/Base.ThemeOverlay.Material3.BottomSheetDialog">
    </style>
    <style name="ThemeOverlay.Material3.Button" parent="">
        <item name="colorContainer">?attr/colorPrimary</item>
        <item name="colorOnContainer">?attr/colorOnPrimary</item>
    </style>
    <style name="ThemeOverlay.Material3.Button.ElevatedButton" parent="@style/ThemeOverlay.Material3.Button.TextButton">
        <item name="colorContainer">?attr/colorSurfaceContainerLow</item>
    </style>
    <style name="ThemeOverlay.Material3.Button.IconButton" parent="@style/ThemeOverlay.Material3.Button.TextButton">
        <item name="colorOnContainer">?attr/colorPrimary</item>
        <item name="colorOnContainerUnchecked">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="ThemeOverlay.Material3.Button.IconButton.Filled" parent="@style/ThemeOverlay.Material3.Button">
        <item name="colorContainer">?attr/colorPrimary</item>
        <item name="colorOnContainer">?attr/colorOnPrimary</item>
        <item name="colorOnContainerUnchecked">?attr/colorPrimary</item>
    </style>
    <style name="ThemeOverlay.Material3.Button.IconButton.Filled.Tonal" parent="@style/ThemeOverlay.Material3.Button.TonalButton">
        <item name="colorContainer">?attr/colorSecondaryContainer</item>
        <item name="colorOnContainer">?attr/colorOnSecondaryContainer</item>
        <item name="colorOnContainerUnchecked">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="ThemeOverlay.Material3.Button.TextButton" parent="">
        <item name="colorContainer">@android:color/transparent</item>
        <item name="colorOnContainer">?attr/colorPrimary</item>
    </style>
    <style name="ThemeOverlay.Material3.Button.TextButton.Snackbar" parent="@style/ThemeOverlay.Material3.Button.TextButton">
        <item name="colorOnContainer">?attr/colorPrimaryInverse</item>
    </style>
    <style name="ThemeOverlay.Material3.Button.TonalButton" parent="@style/ThemeOverlay.Material3.Button">
        <item name="colorContainer">?attr/colorSecondaryContainer</item>
        <item name="colorOnContainer">?attr/colorOnSecondaryContainer</item>
    </style>
    <style name="ThemeOverlay.Material3.Chip" parent="">
        <item name="colorControlNormal">@color/m3_chip_text_color</item>
    </style>
    <style name="ThemeOverlay.Material3.Chip.Assist" parent="">
        <item name="colorControlNormal">@color/m3_assist_chip_icon_tint_color</item>
    </style>
    <style name="ThemeOverlay.Material3.Dark" parent="@style/ThemeOverlay.MaterialComponents.Dark">
        <item name="android:colorBackground">@color/m3_sys_color_dark_background</item>
        <item name="android:textColorPrimary">@color/m3_dark_default_color_primary_text</item>
        <item name="android:textColorPrimaryDisableOnly">@color/m3_dark_primary_text_disable_only</item>
        <item name="android:textColorSecondary">@color/m3_dark_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverse">@color/m3_default_color_primary_text</item>
        <item name="android:textColorSecondaryInverse">@color/m3_default_color_secondary_text</item>
        <item name="android:textColorHintInverse">@color/m3_hint_foreground</item>
        <item name="android:textColorHighlight">@color/m3_dark_highlighted_text</item>
        <item name="android:textColorHint">@color/m3_dark_hint_foreground</item>
        <item name="android:textColorLink">?attr/colorPrimary</item>
        <item name="android:textColorTertiary">@color/m3_dark_default_color_secondary_text</item>
        <item name="android:textColorTertiaryInverse">@color/m3_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_primary_text_disable_only</item>
        <item name="android:textColorAlertDialogListItem">@color/m3_dark_default_color_primary_text</item>
        <item name="android:textColorHighlightInverse">@color/m3_highlighted_text</item>
        <item name="android:textColorLinkInverse">?attr/colorPrimaryInverse</item>
        <item name="colorError">@color/m3_sys_color_dark_error</item>
        <item name="colorErrorContainer">@color/m3_sys_color_dark_error_container</item>
        <item name="colorOnBackground">@color/m3_sys_color_dark_on_background</item>
        <item name="colorOnError">@color/m3_sys_color_dark_on_error</item>
        <item name="colorOnErrorContainer">@color/m3_sys_color_dark_on_error_container</item>
        <item name="colorOnSurface">@color/m3_sys_color_dark_on_surface</item>
        <item name="colorOnSurfaceInverse">@color/m3_sys_color_dark_inverse_on_surface</item>
        <item name="colorOnSurfaceVariant">@color/m3_sys_color_dark_on_surface_variant</item>
        <item name="colorOutline">@color/m3_sys_color_dark_outline</item>
        <item name="colorOutlineVariant">@color/m3_sys_color_dark_outline_variant</item>
        <item name="colorSurface">@color/m3_sys_color_dark_surface</item>
        <item name="colorSurfaceBright">@color/m3_sys_color_dark_surface_bright</item>
        <item name="colorSurfaceContainer">@color/m3_sys_color_dark_surface_container</item>
        <item name="colorSurfaceContainerHigh">@color/m3_sys_color_dark_surface_container_high</item>
        <item name="colorSurfaceContainerHighest">@color/m3_sys_color_dark_surface_container_highest</item>
        <item name="colorSurfaceContainerLow">@color/m3_sys_color_dark_surface_container_low</item>
        <item name="colorSurfaceContainerLowest">@color/m3_sys_color_dark_surface_container_lowest</item>
        <item name="colorSurfaceDim">@color/m3_sys_color_dark_surface_dim</item>
        <item name="colorSurfaceInverse">@color/m3_sys_color_dark_inverse_surface</item>
        <item name="colorSurfaceVariant">@color/m3_sys_color_dark_surface_variant</item>
    </style>
    <style name="ThemeOverlay.Material3.Dark.ActionBar" parent="@style/ThemeOverlay.MaterialComponents.Dark.ActionBar">
    </style>
    <style name="ThemeOverlay.Material3.DayNight.BottomSheetDialog" parent="@style/ThemeOverlay.Material3.BottomSheetDialog">
    </style>
    <style name="ThemeOverlay.Material3.DayNight.SideSheetDialog" parent="@style/ThemeOverlay.Material3.SideSheetDialog">
    </style>
    <style name="ThemeOverlay.Material3.Dialog" parent="@style/Base.ThemeOverlay.Material3.Dialog">
    </style>
    <style name="ThemeOverlay.Material3.Dialog.Alert" parent="@style/ThemeOverlay.MaterialComponents.Dialog.Alert">
        <item name="android:windowElevation">@dimen/m3_alert_dialog_elevation</item>
        <item name="buttonBarButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog.Flush</item>
    </style>
    <style name="ThemeOverlay.Material3.Dialog.Alert.Framework" parent="@style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework">
        <item name="android:buttonBarButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog.Flush</item>
        <item name="android:windowElevation">@dimen/m3_alert_dialog_elevation</item>
    </style>
    <style name="ThemeOverlay.Material3.DynamicColors.Dark" parent="@style/ThemeOverlay.Material3.Dark">
    </style>
    <style name="ThemeOverlay.Material3.DynamicColors.DayNight" parent="@style/ThemeOverlay.Material3.DynamicColors.Light">
    </style>
    <style name="ThemeOverlay.Material3.DynamicColors.Light" parent="@style/ThemeOverlay.Material3.Light">
    </style>
    <style name="ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary" parent="">
        <item name="colorContainer">?attr/colorPrimaryContainer</item>
        <item name="colorOnContainer">?attr/colorOnPrimaryContainer</item>
    </style>
    <style name="ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary" parent="">
        <item name="colorContainer">?attr/colorSecondaryContainer</item>
        <item name="colorOnContainer">?attr/colorOnSecondaryContainer</item>
    </style>
    <style name="ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface" parent="">
        <item name="colorContainer">?attr/colorSurfaceContainerHigh</item>
        <item name="colorOnContainer">?attr/colorPrimary</item>
    </style>
    <style name="ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary" parent="">
        <item name="colorContainer">?attr/colorTertiaryContainer</item>
        <item name="colorOnContainer">?attr/colorOnTertiaryContainer</item>
    </style>
    <style name="ThemeOverlay.Material3.FloatingActionButton.Primary" parent="">
        <item name="colorContainer">?attr/colorPrimaryContainer</item>
        <item name="colorOnContainer">?attr/colorOnPrimaryContainer</item>
    </style>
    <style name="ThemeOverlay.Material3.FloatingActionButton.Secondary" parent="">
        <item name="colorContainer">?attr/colorSecondaryContainer</item>
        <item name="colorOnContainer">?attr/colorOnSecondaryContainer</item>
    </style>
    <style name="ThemeOverlay.Material3.FloatingActionButton.Surface" parent="">
        <item name="colorContainer">?attr/colorSurfaceContainerHigh</item>
        <item name="colorOnContainer">?attr/colorPrimary</item>
    </style>
    <style name="ThemeOverlay.Material3.FloatingActionButton.Tertiary" parent="">
        <item name="colorContainer">?attr/colorTertiaryContainer</item>
        <item name="colorOnContainer">?attr/colorOnTertiaryContainer</item>
    </style>
    <style name="ThemeOverlay.Material3.HarmonizedColors" parent="">
        <item name="colorError">@color/material_harmonized_color_error</item>
        <item name="colorErrorContainer">@color/material_harmonized_color_error_container</item>
        <item name="colorOnError">@color/material_harmonized_color_on_error</item>
        <item name="colorOnErrorContainer">@color/material_harmonized_color_on_error_container</item>
    </style>
    <style name="ThemeOverlay.Material3.HarmonizedColors.Empty" parent="">
    </style>
    <style name="ThemeOverlay.Material3.Light" parent="@style/ThemeOverlay.MaterialComponents.Light">
        <item name="android:colorBackground">@color/m3_sys_color_light_background</item>
        <item name="android:textColorPrimary">@color/m3_default_color_primary_text</item>
        <item name="android:textColorPrimaryDisableOnly">@color/m3_primary_text_disable_only</item>
        <item name="android:textColorSecondary">@color/m3_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverse">@color/m3_dark_default_color_primary_text</item>
        <item name="android:textColorSecondaryInverse">@color/m3_dark_default_color_secondary_text</item>
        <item name="android:textColorHintInverse">@color/m3_dark_hint_foreground</item>
        <item name="android:textColorHighlight">@color/m3_highlighted_text</item>
        <item name="android:textColorHint">@color/m3_hint_foreground</item>
        <item name="android:textColorLink">?attr/colorPrimary</item>
        <item name="android:textColorTertiary">@color/m3_default_color_secondary_text</item>
        <item name="android:textColorTertiaryInverse">@color/m3_dark_default_color_secondary_text</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/m3_dark_primary_text_disable_only</item>
        <item name="android:textColorAlertDialogListItem">@color/m3_default_color_primary_text</item>
        <item name="android:textColorHighlightInverse">@color/m3_dark_highlighted_text</item>
        <item name="android:textColorLinkInverse">?attr/colorPrimaryInverse</item>
        <item name="colorError">@color/m3_sys_color_light_error</item>
        <item name="colorErrorContainer">@color/m3_sys_color_light_error_container</item>
        <item name="colorOnBackground">@color/m3_sys_color_light_on_background</item>
        <item name="colorOnError">@color/m3_sys_color_light_on_error</item>
        <item name="colorOnErrorContainer">@color/m3_sys_color_light_on_error_container</item>
        <item name="colorOnSurface">@color/m3_sys_color_light_on_surface</item>
        <item name="colorOnSurfaceInverse">@color/m3_sys_color_light_inverse_on_surface</item>
        <item name="colorOnSurfaceVariant">@color/m3_sys_color_light_on_surface_variant</item>
        <item name="colorOutline">@color/m3_sys_color_light_outline</item>
        <item name="colorOutlineVariant">@color/m3_sys_color_light_outline_variant</item>
        <item name="colorSurface">@color/m3_sys_color_light_surface</item>
        <item name="colorSurfaceBright">@color/m3_sys_color_light_surface_bright</item>
        <item name="colorSurfaceContainer">@color/m3_sys_color_light_surface_container</item>
        <item name="colorSurfaceContainerHigh">@color/m3_sys_color_light_surface_container_high</item>
        <item name="colorSurfaceContainerHighest">@color/m3_sys_color_light_surface_container_highest</item>
        <item name="colorSurfaceContainerLow">@color/m3_sys_color_light_surface_container_low</item>
        <item name="colorSurfaceContainerLowest">@color/m3_sys_color_light_surface_container_lowest</item>
        <item name="colorSurfaceDim">@color/m3_sys_color_light_surface_dim</item>
        <item name="colorSurfaceInverse">@color/m3_sys_color_light_inverse_surface</item>
        <item name="colorSurfaceVariant">@color/m3_sys_color_light_surface_variant</item>
    </style>
    <style name="ThemeOverlay.Material3.Light.Dialog.Alert.Framework" parent="@style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework">
        <item name="android:buttonBarButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog.Flush</item>
        <item name="android:windowElevation">@dimen/m3_alert_dialog_elevation</item>
    </style>
    <style name="ThemeOverlay.Material3.MaterialAlertDialog" parent="@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog">
        <item name="android:windowAnimationStyle">@style/MaterialAlertDialog.Material3.Animation</item>
        <item name="android:checkedTextViewStyle">@style/Widget.Material3.CheckedTextView</item>
        <item name="android:windowElevation">@dimen/m3_alert_dialog_elevation</item>
        <item name="alertDialogStyle">@style/MaterialAlertDialog.Material3</item>
        <item name="buttonBarButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog</item>
        <item name="buttonBarNegativeButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog</item>
        <item name="buttonBarNeutralButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog.Flush</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog</item>
        <item name="materialAlertDialogBodyTextStyle">@style/MaterialAlertDialog.Material3.Body.Text</item>
        <item name="materialAlertDialogTitleIconStyle">@style/MaterialAlertDialog.Material3.Title.Icon</item>
        <item name="materialAlertDialogTitlePanelStyle">@style/MaterialAlertDialog.Material3.Title.Panel</item>
        <item name="materialAlertDialogTitleTextStyle">@style/MaterialAlertDialog.Material3.Title.Text</item>
    </style>
    <style name="ThemeOverlay.Material3.MaterialAlertDialog.Centered" parent="@style/ThemeOverlay.Material3.MaterialAlertDialog">
        <item name="materialAlertDialogTitleIconStyle">@style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked</item>
        <item name="materialAlertDialogTitlePanelStyle">@style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked</item>
        <item name="materialAlertDialogTitleTextStyle">@style/MaterialAlertDialog.Material3.Title.Text.CenterStacked</item>
    </style>
    <style name="ThemeOverlay.Material3.MaterialCalendar" parent="@style/ThemeOverlay.Material3.Dialog">
        <item name="android:windowElevation">@dimen/m3_datepicker_elevation</item>
        <item name="buttonBarNegativeButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog</item>
        <item name="materialCalendarDay">@style/Widget.Material3.MaterialCalendar.DayTextView</item>
        <item name="materialCalendarDayOfWeekLabel">@style/Widget.Material3.MaterialCalendar.DayOfWeekLabel</item>
        <item name="materialCalendarHeaderCancelButton">@style/Widget.Material3.MaterialCalendar.HeaderCancelButton</item>
        <item name="materialCalendarHeaderConfirmButton">@style/Widget.Material3.Button.TextButton</item>
        <item name="materialCalendarHeaderDivider">@style/Widget.Material3.MaterialCalendar.HeaderDivider</item>
        <item name="materialCalendarHeaderLayout">@style/Widget.Material3.MaterialCalendar.HeaderLayout</item>
        <item name="materialCalendarHeaderSelection">@style/Widget.Material3.MaterialCalendar.HeaderSelection</item>
        <item name="materialCalendarHeaderTitle">@style/Widget.Material3.MaterialCalendar.HeaderTitle</item>
        <item name="materialCalendarHeaderToggleButton">@style/Widget.Material3.MaterialCalendar.HeaderToggleButton</item>
        <item name="materialCalendarMonth">@style/Widget.Material3.MaterialCalendar.MonthTextView</item>
        <item name="materialCalendarMonthNavigationButton">@style/Widget.Material3.MaterialCalendar.MonthNavigationButton</item>
        <item name="materialCalendarStyle">@style/Widget.Material3.MaterialCalendar</item>
        <item name="materialCalendarYearNavigationButton">@style/Widget.Material3.MaterialCalendar.YearNavigationButton</item>
    </style>
    <style name="ThemeOverlay.Material3.MaterialCalendar.Fullscreen" parent="@style/ThemeOverlay.Material3.MaterialCalendar">
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowElevation">@dimen/m3_datepicker_elevation</item>
        <item name="materialCalendarHeaderLayout">@style/Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen</item>
        <item name="materialCalendarHeaderSelection">@style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.Material3.MaterialCalendar.Fullscreen</item>
    </style>
    <style name="ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton" parent="@style/ThemeOverlay.Material3.Button.TextButton">
        <item name="colorOnContainer">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="ThemeOverlay.Material3.MaterialTimePicker" parent="@style/ThemeOverlay.Material3.Dialog">
        <item name="android:windowElevation">@dimen/m3_timepicker_window_elevation</item>
        <item name="chipStyle">@style/Widget.Material3.MaterialTimePicker.Display</item>
        <item name="imageButtonStyle">@style/Widget.Material3.MaterialTimePicker.ImageButton</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.Material3.MaterialTimePicker.Button</item>
        <item name="materialClockStyle">@style/Widget.Material3.MaterialTimePicker.Clock</item>
        <item name="materialDisplayDividerStyle">@style/Widget.Material3.MaterialTimePicker.Display.Divider</item>
        <item name="materialTimePickerStyle">@style/Widget.Material3.MaterialTimePicker</item>
        <item name="materialTimePickerTitleStyle">@style/TextAppearance.Material3.MaterialTimePicker.Title</item>
        <item name="textInputStyle">@style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout</item>
    </style>
    <style name="ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText" parent="@style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox">
        <item name="android:textViewStyle">@style/Widget.Material3.MaterialTimePicker.Display.HelperText</item>
        <item name="editTextStyle">@style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText</item>
    </style>
    <style name="ThemeOverlay.Material3.NavigationRailView" parent="">
        <item name="badgeStyle">@style/Widget.Material3.NavigationRailView.Badge</item>
    </style>
    <style name="ThemeOverlay.Material3.NavigationView" parent="">
        <item name="android:listDivider">?attr/colorOutline</item>
    </style>
    <style name="ThemeOverlay.Material3.PersonalizedColors" parent="">
        <item name="android:colorBackground">@color/material_personalized_color_background</item>
        <item name="android:textColorPrimary">@color/material_personalized_color_primary_text</item>
        <item name="android:textColorPrimaryDisableOnly">@color/material_personalized_primary_text_disable_only</item>
        <item name="android:textColorSecondary">@color/material_personalized_color_secondary_text</item>
        <item name="android:textColorPrimaryInverse">@color/material_personalized_color_primary_text_inverse</item>
        <item name="android:textColorSecondaryInverse">@color/material_personalized_color_secondary_text_inverse</item>
        <item name="android:textColorHintInverse">@color/material_personalized_hint_foreground_inverse</item>
        <item name="android:textColorHighlight">@color/material_personalized__highlighted_text</item>
        <item name="android:textColorHint">@color/material_personalized_hint_foreground</item>
        <item name="android:textColorTertiary">@color/material_personalized_color_secondary_text</item>
        <item name="android:textColorTertiaryInverse">@color/material_personalized_color_secondary_text_inverse</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/material_personalized_primary_inverse_text_disable_only</item>
        <item name="android:textColorAlertDialogListItem">@color/material_personalized_color_primary_text</item>
        <item name="android:textColorHighlightInverse">@color/material_personalized__highlighted_text_inverse</item>
        <item name="android:colorControlNormal">@color/material_personalized_color_control_normal</item>
        <item name="android:colorControlActivated">@color/material_personalized_color_control_activated</item>
        <item name="android:colorControlHighlight">@color/material_personalized_color_control_highlight</item>
        <item name="colorError">@color/material_personalized_color_error</item>
        <item name="colorErrorContainer">@color/material_personalized_color_error_container</item>
        <item name="colorOnBackground">@color/material_personalized_color_on_background</item>
        <item name="colorOnError">@color/material_personalized_color_on_error</item>
        <item name="colorOnErrorContainer">@color/material_personalized_color_on_error_container</item>
        <item name="colorOnPrimary">@color/material_personalized_color_on_primary</item>
        <item name="colorOnPrimaryContainer">@color/material_personalized_color_on_primary_container</item>
        <item name="colorOnSecondary">@color/material_personalized_color_on_secondary</item>
        <item name="colorOnSecondaryContainer">@color/material_personalized_color_on_secondary_container</item>
        <item name="colorOnSurface">@color/material_personalized_color_on_surface</item>
        <item name="colorOnSurfaceInverse">@color/material_personalized_color_on_surface_inverse</item>
        <item name="colorOnSurfaceVariant">@color/material_personalized_color_on_surface_variant</item>
        <item name="colorOnTertiary">@color/material_personalized_color_on_tertiary</item>
        <item name="colorOnTertiaryContainer">@color/material_personalized_color_on_tertiary_container</item>
        <item name="colorOutline">@color/material_personalized_color_outline</item>
        <item name="colorOutlineVariant">@color/material_personalized_color_outline_variant</item>
        <item name="colorPrimary">@color/material_personalized_color_primary</item>
        <item name="colorPrimaryContainer">@color/material_personalized_color_primary_container</item>
        <item name="colorPrimaryInverse">@color/material_personalized_color_primary_inverse</item>
        <item name="colorSecondary">@color/material_personalized_color_secondary</item>
        <item name="colorSecondaryContainer">@color/material_personalized_color_secondary_container</item>
        <item name="colorSurface">@color/material_personalized_color_surface</item>
        <item name="colorSurfaceBright">@color/material_personalized_color_surface_bright</item>
        <item name="colorSurfaceContainer">@color/material_personalized_color_surface_container</item>
        <item name="colorSurfaceContainerHigh">@color/material_personalized_color_surface_container_high</item>
        <item name="colorSurfaceContainerHighest">@color/material_personalized_color_surface_container_highest</item>
        <item name="colorSurfaceContainerLow">@color/material_personalized_color_surface_container_low</item>
        <item name="colorSurfaceContainerLowest">@color/material_personalized_color_surface_container_lowest</item>
        <item name="colorSurfaceDim">@color/material_personalized_color_surface_dim</item>
        <item name="colorSurfaceInverse">@color/material_personalized_color_surface_inverse</item>
        <item name="colorSurfaceVariant">@color/material_personalized_color_surface_variant</item>
        <item name="colorTertiary">@color/material_personalized_color_tertiary</item>
        <item name="colorTertiaryContainer">@color/material_personalized_color_tertiary_container</item>
    </style>
    <style name="ThemeOverlay.Material3.Search" parent="">
        <item name="actionMenuTextColor">?attr/colorOnSurfaceVariant</item>
        <item name="actionOverflowButtonStyle">@style/Widget.Material3.Search.ActionButton.Overflow</item>
        <item name="colorControlNormal">?attr/colorOnSurfaceVariant</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.Material3.Search.Toolbar.Button.Navigation</item>
    </style>
    <style name="ThemeOverlay.Material3.SideSheetDialog" parent="@style/Base.ThemeOverlay.Material3.SideSheetDialog">
    </style>
    <style name="ThemeOverlay.Material3.Snackbar" parent="">
        <item name="colorOnSurface">?attr/colorOnBackground</item>
    </style>
    <style name="ThemeOverlay.Material3.TabLayout" parent="">
        <item name="badgeStyle">@style/Widget.Material3.Badge.AdjustToBounds</item>
    </style>
    <style name="ThemeOverlay.Material3.TextInputEditText" parent="@style/Base.ThemeOverlay.Material3.TextInputEditText">
        <item name="colorControlNormal">@color/m3_textfield_indicator_text_color</item>
    </style>
    <style name="ThemeOverlay.Material3.TextInputEditText.FilledBox" parent="@style/ThemeOverlay.Material3.TextInputEditText">
        <item name="editTextStyle">@style/Widget.Material3.TextInputEditText.FilledBox</item>
    </style>
    <style name="ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense" parent="@style/ThemeOverlay.Material3.TextInputEditText.FilledBox">
        <item name="editTextStyle">@style/Widget.Material3.TextInputEditText.FilledBox.Dense</item>
    </style>
    <style name="ThemeOverlay.Material3.TextInputEditText.OutlinedBox" parent="@style/ThemeOverlay.Material3.TextInputEditText">
        <item name="editTextStyle">@style/Widget.Material3.TextInputEditText.OutlinedBox</item>
    </style>
    <style name="ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense" parent="@style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox">
        <item name="editTextStyle">@style/Widget.Material3.TextInputEditText.OutlinedBox.Dense</item>
    </style>
    <style name="ThemeOverlay.Material3.Toolbar.Surface" parent="">
        <item name="actionMenuTextColor">?attr/colorOnSurfaceVariant</item>
        <item name="colorControlNormal">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon" parent="">
        <item name="colorControlNormal">?attr/colorSecondary</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents" parent="@style/ThemeOverlay.AppCompat">
    </style>
    <style name="ThemeOverlay.MaterialComponents.ActionBar" parent="@style/ThemeOverlay.AppCompat.ActionBar">
    </style>
    <style name="ThemeOverlay.MaterialComponents.ActionBar.Primary" parent="@style/ThemeOverlay.AppCompat.ActionBar">
        <item name="android:colorBackground">?attr/colorPrimary</item>
        <item name="android:textColorPrimary">?attr/colorOnPrimary</item>
        <item name="android:textColorSecondary">@color/material_on_primary_emphasis_medium</item>
        <item name="actionMenuTextColor">?attr/colorOnPrimary</item>
        <item name="colorControlNormal">?attr/colorOnPrimary</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.ActionBar.Surface" parent="@style/ThemeOverlay.AppCompat.ActionBar">
        <item name="android:colorBackground">?attr/colorSurface</item>
        <item name="android:textColorPrimary">@color/material_on_surface_emphasis_high_type</item>
        <item name="android:textColorSecondary">@color/material_on_surface_emphasis_medium</item>
        <item name="actionMenuTextColor">@color/material_on_surface_emphasis_medium</item>
        <item name="colorControlNormal">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView" parent="">
        <item name="colorControlActivated">?attr/colorPrimary</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox" parent="@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView">
        <item name="autoCompleteTextViewStyle">@style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense" parent="@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox">
        <item name="autoCompleteTextViewStyle">@style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox" parent="@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView">
        <item name="autoCompleteTextViewStyle">@style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense" parent="@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox">
        <item name="autoCompleteTextViewStyle">@style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.BottomAppBar.Primary" parent="">
        <item name="actionMenuTextColor">?attr/colorOnPrimary</item>
        <item name="colorControlNormal">?attr/colorOnPrimary</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.BottomAppBar.Surface" parent="">
        <item name="actionMenuTextColor">@color/material_on_surface_emphasis_medium</item>
        <item name="colorControlNormal">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.BottomSheetDialog" parent="@style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dark" parent="@style/ThemeOverlay.AppCompat.Dark">
        <item name="android:colorBackground">@color/design_dark_default_color_background</item>
        <item name="colorError">@color/design_dark_default_color_error</item>
        <item name="colorOnBackground">@color/design_dark_default_color_on_background</item>
        <item name="colorOnError">@color/design_dark_default_color_on_error</item>
        <item name="colorOnSurface">@color/design_dark_default_color_on_surface</item>
        <item name="colorSurface">@color/design_dark_default_color_surface</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dark.ActionBar" parent="@style/ThemeOverlay.AppCompat.Dark.ActionBar">
    </style>
    <style name="ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog" parent="@style/ThemeOverlay.MaterialComponents.BottomSheetDialog">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dialog" parent="@style/Base.ThemeOverlay.MaterialComponents.Dialog">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dialog.Alert" parent="@style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dialog.Alert.Framework" parent="@style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Light" parent="@style/ThemeOverlay.AppCompat.Light">
        <item name="android:colorBackground">@color/design_default_color_background</item>
        <item name="colorError">@color/design_default_color_error</item>
        <item name="colorOnBackground">@color/design_default_color_on_background</item>
        <item name="colorOnError">@color/design_default_color_on_error</item>
        <item name="colorOnSurface">@color/design_default_color_on_surface</item>
        <item name="colorSurface">@color/design_default_color_surface</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework" parent="@style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework">
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog" parent="@style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog">
        <item name="materialAlertDialogTitleIconStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Icon</item>
        <item name="materialAlertDialogTitlePanelStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Panel</item>
        <item name="materialAlertDialogTitleTextStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Text</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered" parent="@style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog">
        <item name="materialAlertDialogTitleIconStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked</item>
        <item name="materialAlertDialogTitlePanelStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked</item>
        <item name="materialAlertDialogTitleTextStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date" parent="@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog">
        <item name="android:buttonBarPositiveButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="android:buttonBarNegativeButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar" parent="@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date">
        <item name="android:datePickerStyle">@style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text" parent="@style/TextAppearance.MaterialComponents.Headline6">
        <item name="android:textColor">?attr/colorOnPrimary</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day" parent="@style/TextAppearance.MaterialComponents.Headline1">
        <item name="android:textColor">?attr/colorOnPrimary</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner" parent="@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date">
        <item name="android:datePickerStyle">@style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialCalendar" parent="@style/ThemeOverlay.MaterialComponents.Dialog">
        <item name="buttonBarNegativeButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="materialCalendarDay">@style/Widget.MaterialComponents.MaterialCalendar.DayTextView</item>
        <item name="materialCalendarDayOfWeekLabel">@style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel</item>
        <item name="materialCalendarHeaderCancelButton">@style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton</item>
        <item name="materialCalendarHeaderConfirmButton">@style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton</item>
        <item name="materialCalendarHeaderDivider">@style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider</item>
        <item name="materialCalendarHeaderLayout">@style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout</item>
        <item name="materialCalendarHeaderSelection">@style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection</item>
        <item name="materialCalendarHeaderTitle">@style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle</item>
        <item name="materialCalendarHeaderToggleButton">@style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton</item>
        <item name="materialCalendarMonth">@style/Widget.MaterialComponents.MaterialCalendar.MonthTextView</item>
        <item name="materialCalendarMonthNavigationButton">@style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton</item>
        <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>
        <item name="materialCalendarYearNavigationButton">@style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen" parent="@style/ThemeOverlay.MaterialComponents.MaterialCalendar">
        <item name="android:windowIsFloating">false</item>
        <item name="materialCalendarHeaderLayout">@style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen</item>
        <item name="materialCalendarHeaderSelection">@style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar.Fullscreen</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText" parent="@style/ThemeOverlay.Design.TextInputEditText">
        <item name="android:editTextBackground">@null</item>
        <item name="colorControlActivated">?attr/colorPrimary</item>
        <item name="editTextBackground">@null</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.FilledBox</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TimePicker" parent="@style/ThemeOverlay.MaterialComponents.Dialog">
        <item name="chipStyle">@style/Widget.MaterialComponents.TimePicker.Display</item>
        <item name="elevationOverlayEnabled">false</item>
        <item name="imageButtonStyle">@style/Widget.MaterialComponents.TimePicker.ImageButton</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.TimePicker.Button</item>
        <item name="materialClockStyle">@style/Widget.MaterialComponents.TimePicker.Clock</item>
        <item name="materialDisplayDividerStyle">@style/Widget.MaterialComponents.TimePicker.Display.Divider</item>
        <item name="materialTimePickerTitleStyle">@style/TextAppearance.MaterialComponents.TimePicker.Title</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TimePicker.Display" parent="">
        <item name="elevationOverlayEnabled">false</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox">
        <item name="android:textViewStyle">@style/Widget.MaterialComponents.TimePicker.Display.HelperText</item>
        <item name="editTextStyle">@style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary" parent="">
        <item name="colorControlNormal">?attr/colorOnSurface</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.Toolbar.Primary" parent="">
        <item name="actionMenuTextColor">?attr/colorOnPrimary</item>
        <item name="colorControlNormal">?attr/colorOnPrimary</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.Toolbar.Surface" parent="">
        <item name="actionMenuTextColor">@color/material_on_surface_emphasis_medium</item>
        <item name="colorControlNormal">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="TitleCollapseAppBar" parent="">
        <item name="android:textSize">24sp</item>
        <item name="android:textColor">@color/text_headline_color</item>
        <item name="fontFamily">@font/poppins_semibold</item>
    </style>
    <style name="Widget.AppCompat.ActionBar" parent="@style/Base.Widget.AppCompat.ActionBar">
    </style>
    <style name="Widget.AppCompat.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.ActionBar.TabText">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.ActionButton" parent="@style/Base.Widget.AppCompat.ActionButton">
    </style>
    <style name="Widget.AppCompat.ActionButton.CloseMode" parent="@style/Base.Widget.AppCompat.ActionButton.CloseMode">
    </style>
    <style name="Widget.AppCompat.ActionButton.Overflow" parent="@style/Base.Widget.AppCompat.ActionButton.Overflow">
    </style>
    <style name="Widget.AppCompat.ActionMode" parent="@style/Base.Widget.AppCompat.ActionMode">
    </style>
    <style name="Widget.AppCompat.ActivityChooserView" parent="@style/Base.Widget.AppCompat.ActivityChooserView">
    </style>
    <style name="Widget.AppCompat.AutoCompleteTextView" parent="@style/Base.Widget.AppCompat.AutoCompleteTextView">
    </style>
    <style name="Widget.AppCompat.Button" parent="@style/Base.Widget.AppCompat.Button">
    </style>
    <style name="Widget.AppCompat.Button.Borderless" parent="@style/Base.Widget.AppCompat.Button.Borderless">
    </style>
    <style name="Widget.AppCompat.Button.Borderless.Colored" parent="@style/Base.Widget.AppCompat.Button.Borderless.Colored">
    </style>
    <style name="Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog">
    </style>
    <style name="Widget.AppCompat.Button.Colored" parent="@style/Base.Widget.AppCompat.Button.Colored">
    </style>
    <style name="Widget.AppCompat.Button.Small" parent="@style/Base.Widget.AppCompat.Button.Small">
    </style>
    <style name="Widget.AppCompat.ButtonBar" parent="@style/Base.Widget.AppCompat.ButtonBar">
    </style>
    <style name="Widget.AppCompat.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.ButtonBar.AlertDialog">
    </style>
    <style name="Widget.AppCompat.CompoundButton.CheckBox" parent="@style/Base.Widget.AppCompat.CompoundButton.CheckBox">
    </style>
    <style name="Widget.AppCompat.CompoundButton.RadioButton" parent="@style/Base.Widget.AppCompat.CompoundButton.RadioButton">
    </style>
    <style name="Widget.AppCompat.CompoundButton.Switch" parent="@style/Base.Widget.AppCompat.CompoundButton.Switch">
    </style>
    <style name="Widget.AppCompat.DrawerArrowToggle" parent="@style/Base.Widget.AppCompat.DrawerArrowToggle">
        <item name="color">?attr/colorControlNormal</item>
    </style>
    <style name="Widget.AppCompat.DropDownItem.Spinner" parent="@style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text">
    </style>
    <style name="Widget.AppCompat.EditText" parent="@style/Base.Widget.AppCompat.EditText">
    </style>
    <style name="Widget.AppCompat.ImageButton" parent="@style/Base.Widget.AppCompat.ImageButton">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar" parent="@style/Base.Widget.AppCompat.Light.ActionBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.Light.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.Solid.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabView.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.Light.ActionButton" parent="@style/Widget.AppCompat.ActionButton">
    </style>
    <style name="Widget.AppCompat.Light.ActionButton.CloseMode" parent="@style/Widget.AppCompat.ActionButton.CloseMode">
    </style>
    <style name="Widget.AppCompat.Light.ActionButton.Overflow" parent="@style/Widget.AppCompat.ActionButton.Overflow">
    </style>
    <style name="Widget.AppCompat.Light.ActionMode.Inverse" parent="@style/Widget.AppCompat.ActionMode">
    </style>
    <style name="Widget.AppCompat.Light.ActivityChooserView" parent="@style/Widget.AppCompat.ActivityChooserView">
    </style>
    <style name="Widget.AppCompat.Light.AutoCompleteTextView" parent="@style/Widget.AppCompat.AutoCompleteTextView">
    </style>
    <style name="Widget.AppCompat.Light.DropDownItem.Spinner" parent="@style/Widget.AppCompat.DropDownItem.Spinner">
    </style>
    <style name="Widget.AppCompat.Light.ListPopupWindow" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Widget.AppCompat.Light.ListView.DropDown" parent="@style/Widget.AppCompat.ListView.DropDown">
    </style>
    <style name="Widget.AppCompat.Light.PopupMenu" parent="@style/Base.Widget.AppCompat.Light.PopupMenu">
    </style>
    <style name="Widget.AppCompat.Light.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.Light.PopupMenu.Overflow">
    </style>
    <style name="Widget.AppCompat.Light.SearchView" parent="@style/Widget.AppCompat.SearchView">
    </style>
    <style name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" parent="@style/Widget.AppCompat.Spinner.DropDown.ActionBar">
    </style>
    <style name="Widget.AppCompat.ListMenuView" parent="@style/Base.Widget.AppCompat.ListMenuView">
    </style>
    <style name="Widget.AppCompat.ListPopupWindow" parent="@style/Base.Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Widget.AppCompat.ListView" parent="@style/Base.Widget.AppCompat.ListView">
    </style>
    <style name="Widget.AppCompat.ListView.DropDown" parent="@style/Base.Widget.AppCompat.ListView.DropDown">
    </style>
    <style name="Widget.AppCompat.ListView.Menu" parent="@style/Base.Widget.AppCompat.ListView.Menu">
    </style>
    <style name="Widget.AppCompat.PopupMenu" parent="@style/Base.Widget.AppCompat.PopupMenu">
    </style>
    <style name="Widget.AppCompat.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.PopupMenu.Overflow">
    </style>
    <style name="Widget.AppCompat.PopupWindow" parent="@style/Base.Widget.AppCompat.PopupWindow">
    </style>
    <style name="Widget.AppCompat.ProgressBar" parent="@style/Base.Widget.AppCompat.ProgressBar">
    </style>
    <style name="Widget.AppCompat.ProgressBar.Horizontal" parent="@style/Base.Widget.AppCompat.ProgressBar.Horizontal">
    </style>
    <style name="Widget.AppCompat.RatingBar" parent="@style/Base.Widget.AppCompat.RatingBar">
    </style>
    <style name="Widget.AppCompat.RatingBar.Indicator" parent="@style/Base.Widget.AppCompat.RatingBar.Indicator">
    </style>
    <style name="Widget.AppCompat.RatingBar.Small" parent="@style/Base.Widget.AppCompat.RatingBar.Small">
    </style>
    <style name="Widget.AppCompat.SearchView" parent="@style/Base.Widget.AppCompat.SearchView">
    </style>
    <style name="Widget.AppCompat.SearchView.ActionBar" parent="@style/Base.Widget.AppCompat.SearchView.ActionBar">
    </style>
    <style name="Widget.AppCompat.SeekBar" parent="@style/Base.Widget.AppCompat.SeekBar">
    </style>
    <style name="Widget.AppCompat.SeekBar.Discrete" parent="@style/Base.Widget.AppCompat.SeekBar.Discrete">
    </style>
    <style name="Widget.AppCompat.Spinner" parent="@style/Base.Widget.AppCompat.Spinner">
    </style>
    <style name="Widget.AppCompat.Spinner.DropDown" parent="@style/Widget.AppCompat.Spinner">
    </style>
    <style name="Widget.AppCompat.Spinner.DropDown.ActionBar" parent="@style/Widget.AppCompat.Spinner.DropDown">
    </style>
    <style name="Widget.AppCompat.Spinner.Underlined" parent="@style/Base.Widget.AppCompat.Spinner.Underlined">
    </style>
    <style name="Widget.AppCompat.TextView" parent="@style/Base.Widget.AppCompat.TextView">
    </style>
    <style name="Widget.AppCompat.TextView.SpinnerItem" parent="@style/Base.Widget.AppCompat.TextView.SpinnerItem">
    </style>
    <style name="Widget.AppCompat.Toolbar" parent="@style/Base.Widget.AppCompat.Toolbar">
    </style>
    <style name="Widget.AppCompat.Toolbar.Button.Navigation" parent="@style/Base.Widget.AppCompat.Toolbar.Button.Navigation">
    </style>
    <style name="Widget.Compat.NotificationActionContainer" parent="">
        <item name="android:background">@drawable/notification_action_background</item>
    </style>
    <style name="Widget.Compat.NotificationActionText" parent="">
        <item name="android:textAppearance">?android:attr/textAppearanceButton</item>
        <item name="android:textSize">@dimen/notification_action_text_size</item>
        <item name="android:textColor">@color/androidx_core_secondary_text_default_material_light</item>
    </style>
    <style name="Widget.Design.AppBarLayout" parent="@android:style/Widget">
        <item name="android:background">?attr/colorPrimary</item>
        <item name="android:stateListAnimator">@animator/design_appbar_state_list_animator</item>
        <item name="android:touchscreenBlocksFocus">true</item>
    </style>
    <style name="Widget.Design.BottomNavigationView" parent="">
        <item name="android:minHeight">@dimen/design_bottom_navigation_height</item>
        <item name="compatShadowEnabled">true</item>
        <item name="elevation">@dimen/design_bottom_navigation_elevation</item>
        <item name="enforceMaterialTheme">false</item>
        <item name="enforceTextAppearance">false</item>
        <item name="itemActiveIndicatorStyle">@null</item>
        <item name="itemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="itemHorizontalTranslationEnabled">true</item>
        <item name="itemIconSize">@dimen/design_bottom_navigation_icon_size</item>
        <item name="itemPaddingBottom">@dimen/design_bottom_navigation_label_padding</item>
        <item name="itemPaddingTop">@dimen/design_bottom_navigation_margin</item>
        <item name="labelVisibilityMode">auto</item>
    </style>
    <style name="Widget.Design.BottomSheet.Modal" parent="@android:style/Widget">
        <item name="android:background">?android:attr/colorBackground</item>
        <item name="android:elevation">@dimen/design_bottom_sheet_modal_elevation</item>
        <item name="backgroundTint">@null</item>
        <item name="behavior_hideable">true</item>
        <item name="behavior_peekHeight">auto</item>
        <item name="behavior_skipCollapsed">false</item>
        <item name="enforceMaterialTheme">false</item>
        <item name="shapeAppearance">@null</item>
        <item name="shapeAppearanceOverlay">@null</item>
    </style>
    <style name="Widget.Design.CollapsingToolbar" parent="@android:style/Widget">
        <item name="expandedTitleMargin">32dp</item>
        <item name="statusBarScrim">?attr/colorPrimaryDark</item>
    </style>
    <style name="Widget.Design.FloatingActionButton" parent="@android:style/Widget">
        <item name="android:background">@drawable/design_fab_background</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="backgroundTint">?attr/colorAccent</item>
        <item name="borderWidth">@dimen/design_fab_border_width</item>
        <item name="elevation">@dimen/design_fab_elevation</item>
        <item name="fabSize">auto</item>
        <item name="hideMotionSpec">@animator/design_fab_hide_motion_spec</item>
        <item name="hoveredFocusedTranslationZ">@dimen/design_fab_translation_z_hovered_focused</item>
        <item name="maxImageSize">@dimen/design_fab_image_size</item>
        <item name="pressedTranslationZ">@dimen/design_fab_translation_z_pressed</item>
        <item name="rippleColor">?attr/colorControlHighlight</item>
        <item name="showMotionSpec">@animator/design_fab_show_motion_spec</item>
    </style>
    <style name="Widget.Design.NavigationView" parent="@style/Widget.Design.ScrimInsetsFrameLayout">
        <item name="android:background">?android:attr/windowBackground</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:maxWidth">@dimen/design_navigation_max_width</item>
        <item name="drawerLayoutCornerSize">0dp</item>
        <item name="elevation">@dimen/design_navigation_elevation</item>
        <item name="itemHorizontalPadding">@dimen/design_navigation_item_horizontal_padding</item>
        <item name="itemIconPadding">@dimen/design_navigation_item_icon_padding</item>
        <item name="subheaderInsetEnd">?attr/listPreferredItemPaddingRight</item>
        <item name="subheaderInsetStart">?attr/listPreferredItemPaddingLeft</item>
    </style>
    <style name="Widget.Design.ScrimInsetsFrameLayout" parent="">
        <item name="insetForeground">#0000</item>
    </style>
    <style name="Widget.Design.Snackbar" parent="@android:style/Widget">
        <item name="android:background">@drawable/design_snackbar_background</item>
        <item name="android:paddingLeft">@dimen/design_snackbar_padding_horizontal</item>
        <item name="android:paddingRight">@dimen/design_snackbar_padding_horizontal</item>
        <item name="android:maxWidth">@dimen/design_snackbar_max_width</item>
        <item name="android:minWidth">@dimen/design_snackbar_min_width</item>
        <item name="actionTextColorAlpha">@dimen/design_snackbar_action_text_color_alpha</item>
        <item name="animationMode">slide</item>
        <item name="elevation">@dimen/design_snackbar_elevation</item>
        <item name="maxActionInlineWidth">@dimen/design_snackbar_action_inline_max_width</item>
    </style>
    <style name="Widget.Design.TabLayout" parent="@style/Base.Widget.Design.TabLayout">
        <item name="tabGravity">fill</item>
        <item name="tabIndicatorFullWidth">true</item>
        <item name="tabMode">fixed</item>
    </style>
    <style name="Widget.Design.TextInputEditText" parent="@style/Widget.AppCompat.EditText">
        <item name="enforceMaterialTheme">false</item>
        <item name="enforceTextAppearance">false</item>
    </style>
    <style name="Widget.Design.TextInputLayout" parent="@android:style/Widget">
        <item name="boxBackgroundMode">none</item>
        <item name="boxStrokeColor">@color/design_box_stroke_color</item>
        <item name="counterOverflowTextAppearance">@style/TextAppearance.Design.Counter.Overflow</item>
        <item name="counterOverflowTextColor">@null</item>
        <item name="counterTextAppearance">@style/TextAppearance.Design.Counter</item>
        <item name="counterTextColor">@null</item>
        <item name="endIconTint">@color/design_icon_tint</item>
        <item name="enforceMaterialTheme">false</item>
        <item name="enforceTextAppearance">false</item>
        <item name="errorIconDrawable">@null</item>
        <item name="errorTextAppearance">@style/TextAppearance.Design.Error</item>
        <item name="errorTextColor">@null</item>
        <item name="helperTextTextAppearance">@style/TextAppearance.Design.HelperText</item>
        <item name="helperTextTextColor">@null</item>
        <item name="hintTextAppearance">@style/TextAppearance.Design.Hint</item>
        <item name="hintTextColor">@null</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Design.TextInputEditText</item>
        <item name="passwordToggleContentDescription">@string/password_toggle_content_description</item>
        <item name="passwordToggleDrawable">@drawable/design_password_eye</item>
        <item name="passwordToggleTint">@color/design_icon_tint</item>
        <item name="placeholderTextAppearance">@style/TextAppearance.Design.Placeholder</item>
        <item name="placeholderTextColor">@null</item>
        <item name="prefixTextAppearance">@style/TextAppearance.Design.Prefix</item>
        <item name="prefixTextColor">@null</item>
        <item name="shapeAppearance">@null</item>
        <item name="shapeAppearanceOverlay">@null</item>
        <item name="startIconTint">@color/design_icon_tint</item>
        <item name="suffixTextAppearance">@style/TextAppearance.Design.Suffix</item>
        <item name="suffixTextColor">@null</item>
    </style>
    <style name="Widget.Material3.ActionBar.Solid" parent="@style/Base.Widget.Material3.ActionBar.Solid">
    </style>
    <style name="Widget.Material3.ActionMode" parent="@style/Base.Widget.Material3.ActionMode">
    </style>
    <style name="Widget.Material3.AppBarLayout" parent="@style/Widget.MaterialComponents.AppBarLayout.Surface">
        <item name="android:background">?attr/colorSurface</item>
        <item name="android:stateListAnimator">@animator/m3_appbar_state_list_animator</item>
        <item name="liftOnScroll">true</item>
        <item name="liftOnScrollColor">?attr/colorSurfaceContainer</item>
    </style>
    <style name="Widget.Material3.AutoCompleteTextView.FilledBox" parent="@style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox">
        <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
        <item name="android:popupElevation">@dimen/m3_comp_filled_autocomplete_menu_container_elevation</item>
        <item name="dropDownBackgroundTint">?attr/colorSurfaceContainer</item>
        <item name="simpleItemLayout">@layout/m3_auto_complete_simple_item</item>
        <item name="simpleItemSelectedColor">?attr/colorSecondaryContainer</item>
        <item name="simpleItemSelectedRippleColor">@color/m3_simple_item_ripple_color</item>
    </style>
    <style name="Widget.Material3.AutoCompleteTextView.FilledBox.Dense" parent="@style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense">
        <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
        <item name="android:popupElevation">@dimen/m3_comp_filled_autocomplete_menu_container_elevation</item>
        <item name="dropDownBackgroundTint">?attr/colorSurfaceContainer</item>
        <item name="simpleItemLayout">@layout/m3_auto_complete_simple_item</item>
        <item name="simpleItemSelectedColor">?attr/colorSecondaryContainer</item>
        <item name="simpleItemSelectedRippleColor">@color/m3_simple_item_ripple_color</item>
    </style>
    <style name="Widget.Material3.AutoCompleteTextView.OutlinedBox" parent="@style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox">
        <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
        <item name="android:popupElevation">@dimen/m3_comp_outlined_autocomplete_menu_container_elevation</item>
        <item name="dropDownBackgroundTint">?attr/colorSurfaceContainer</item>
        <item name="simpleItemLayout">@layout/m3_auto_complete_simple_item</item>
        <item name="simpleItemSelectedColor">?attr/colorSecondaryContainer</item>
        <item name="simpleItemSelectedRippleColor">@color/m3_simple_item_ripple_color</item>
    </style>
    <style name="Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense" parent="@style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense">
        <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
        <item name="android:popupElevation">@dimen/m3_comp_outlined_autocomplete_menu_container_elevation</item>
        <item name="dropDownBackgroundTint">?attr/colorSurfaceContainer</item>
        <item name="simpleItemLayout">@layout/m3_auto_complete_simple_item</item>
        <item name="simpleItemSelectedColor">?attr/colorSecondaryContainer</item>
        <item name="simpleItemSelectedRippleColor">@color/m3_simple_item_ripple_color</item>
    </style>
    <style name="Widget.Material3.Badge" parent="@style/Widget.MaterialComponents.Badge">
        <item name="backgroundColor">?attr/colorError</item>
        <item name="badgeHeight">@dimen/m3_badge_size</item>
        <item name="badgeShapeAppearance">@style/ShapeAppearance.M3.Comp.Badge.Shape</item>
        <item name="badgeTextAppearance">?attr/textAppearanceLabelSmall</item>
        <item name="badgeTextColor">?attr/colorOnError</item>
        <item name="badgeVerticalPadding">@dimen/m3_badge_with_text_vertical_padding</item>
        <item name="badgeWidth">@dimen/m3_badge_size</item>
        <item name="badgeWithTextHeight">@dimen/m3_badge_with_text_size</item>
        <item name="badgeWithTextShapeAppearance">@style/ShapeAppearance.M3.Comp.Badge.Shape</item>
        <item name="badgeWithTextWidth">@dimen/m3_badge_with_text_size</item>
        <item name="horizontalOffset">@dimen/m3_badge_offset</item>
        <item name="horizontalOffsetWithText">@dimen/m3_badge_with_text_offset</item>
        <item name="offsetAlignmentMode">edge</item>
        <item name="verticalOffset">@dimen/m3_badge_offset</item>
        <item name="verticalOffsetWithText">@dimen/m3_badge_with_text_offset</item>
    </style>
    <style name="Widget.Material3.Badge.AdjustToBounds" parent="@style/Widget.Material3.Badge">
        <item name="autoAdjustToWithinGrandparentBounds">true</item>
    </style>
    <style name="Widget.Material3.BottomAppBar" parent="@style/Widget.MaterialComponents.BottomAppBar">
        <item name="android:paddingLeft">@dimen/m3_bottomappbar_horizontal_padding</item>
        <item name="android:paddingRight">@dimen/m3_bottomappbar_horizontal_padding</item>
        <item name="android:minHeight">@dimen/m3_bottomappbar_height</item>
        <item name="android:paddingStart">@dimen/m3_bottomappbar_horizontal_padding</item>
        <item name="android:paddingEnd">@dimen/m3_bottomappbar_horizontal_padding</item>
        <item name="addElevationShadow">false</item>
        <item name="backgroundTint">?attr/colorSurfaceContainer</item>
        <item name="elevation">@dimen/m3_comp_bottom_app_bar_container_elevation</item>
        <item name="fabAlignmentMode">end</item>
        <item name="fabAlignmentModeEndMargin">@dimen/m3_bottomappbar_fab_end_margin</item>
        <item name="fabAnchorMode">embed</item>
        <item name="fabAnimationMode">slide</item>
        <item name="fabCradleMargin">@dimen/m3_bottomappbar_fab_cradle_margin</item>
        <item name="fabCradleRoundedCornerRadius">@dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius</item>
        <item name="fabCradleVerticalOffset">@dimen/m3_bottomappbar_fab_cradle_vertical_offset</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.BottomAppBar</item>
        <item name="maxButtonHeight">@dimen/m3_bottomappbar_height</item>
        <item name="menuAlignmentMode">start</item>
        <item name="navigationIconTint">?attr/colorOnSurfaceVariant</item>
        <item name="removeEmbeddedFabElevation">true</item>
    </style>
    <style name="Widget.Material3.BottomAppBar.Button.Navigation" parent="@style/Widget.AppCompat.Toolbar.Button.Navigation">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:scaleType">center</item>
        <item name="android:minWidth">@dimen/mtrl_min_touch_target_size</item>
    </style>
    <style name="Widget.Material3.BottomAppBar.Legacy" parent="@style/Widget.MaterialComponents.BottomAppBar">
        <item name="backgroundTint">?attr/colorSurfaceContainer</item>
        <item name="elevation">@dimen/m3_comp_bottom_app_bar_container_elevation</item>
        <item name="fabAlignmentMode">center</item>
        <item name="fabAlignmentModeEndMargin">@null</item>
        <item name="fabAnchorMode">cradle</item>
        <item name="fabAnimationMode">slide</item>
        <item name="fabCradleMargin">@dimen/m3_bottomappbar_fab_cradle_margin</item>
        <item name="fabCradleRoundedCornerRadius">@dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius</item>
        <item name="fabCradleVerticalOffset">@dimen/m3_bottomappbar_fab_cradle_vertical_offset</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.BottomAppBar.Legacy</item>
        <item name="menuAlignmentMode">auto</item>
        <item name="navigationIconTint">?attr/colorOnSurfaceVariant</item>
        <item name="removeEmbeddedFabElevation">false</item>
    </style>
    <style name="Widget.Material3.BottomNavigation.Badge" parent="@style/Widget.Material3.Badge.AdjustToBounds">
        <item name="verticalOffsetWithText">@dimen/m3_nav_badge_with_text_vertical_offset</item>
    </style>
    <style name="Widget.Material3.BottomNavigationView" parent="@style/Base.Widget.Material3.BottomNavigationView">
    </style>
    <style name="Widget.Material3.BottomNavigationView.ActiveIndicator" parent="">
        <item name="android:height">@dimen/m3_bottom_nav_item_active_indicator_height</item>
        <item name="android:width">@dimen/m3_bottom_nav_item_active_indicator_width</item>
        <item name="android:color">?attr/colorSecondaryContainer</item>
        <item name="marginHorizontal">@dimen/m3_bottom_nav_item_active_indicator_margin_horizontal</item>
        <item name="shapeAppearance">@style/ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape</item>
    </style>
    <style name="Widget.Material3.BottomSheet" parent="@style/Widget.MaterialComponents.BottomSheet">
        <item name="android:elevation">@dimen/m3_bottom_sheet_elevation</item>
        <item name="backgroundTint">?attr/colorSurfaceContainerLow</item>
        <item name="marginLeftSystemWindowInsets">true</item>
        <item name="marginRightSystemWindowInsets">true</item>
        <item name="paddingBottomSystemWindowInsets">true</item>
        <item name="paddingLeftSystemWindowInsets">false</item>
        <item name="paddingRightSystemWindowInsets">false</item>
        <item name="paddingTopSystemWindowInsets">true</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerExtraLarge</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.Material3.Corner.Top</item>
        <item name="shouldRemoveExpandedCorners">false</item>
    </style>
    <style name="Widget.Material3.BottomSheet.DragHandle" parent="">
        <item name="android:paddingBottom">@dimen/m3_bottom_sheet_drag_handle_bottom_padding</item>
        <item name="android:scaleType">center</item>
        <item name="android:minWidth">@dimen/mtrl_min_touch_target_size</item>
        <item name="android:minHeight">@dimen/mtrl_min_touch_target_size</item>
        <item name="android:contentDescription">@string/bottomsheet_drag_handle_content_description</item>
        <item name="srcCompat">@drawable/m3_bottom_sheet_drag_handle</item>
        <item name="tint">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="Widget.Material3.BottomSheet.Modal" parent="@style/Widget.Material3.BottomSheet">
        <item name="android:elevation">@dimen/m3_bottom_sheet_modal_elevation</item>
    </style>
    <style name="Widget.Material3.Button" parent="@style/Widget.MaterialComponents.Button">
        <item name="android:textAppearance">?attr/textAppearanceLabelLarge</item>
        <item name="android:textColor">@color/m3_button_foreground_color_selector</item>
        <item name="android:paddingLeft">@dimen/m3_btn_padding_left</item>
        <item name="android:paddingTop">@dimen/m3_btn_padding_top</item>
        <item name="android:paddingRight">@dimen/m3_btn_padding_right</item>
        <item name="android:paddingBottom">@dimen/m3_btn_padding_bottom</item>
        <item name="android:maxWidth">@dimen/m3_btn_max_width</item>
        <item name="android:insetTop">@dimen/m3_btn_inset</item>
        <item name="android:insetBottom">@dimen/m3_btn_inset</item>
        <item name="android:stateListAnimator">@animator/m3_btn_state_list_anim</item>
        <item name="backgroundTint">@color/m3_button_background_color_selector</item>
        <item name="elevation">@dimen/m3_comp_filled_button_container_elevation</item>
        <item name="enforceTextAppearance">false</item>
        <item name="iconSize">@dimen/m3_comp_filled_button_with_icon_icon_size</item>
        <item name="iconTint">@color/m3_button_foreground_color_selector</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Button</item>
        <item name="rippleColor">@color/m3_button_ripple_color_selector</item>
        <item name="shapeAppearance">@style/ShapeAppearance.M3.Comp.FilledButton.Container.Shape</item>
    </style>
    <style name="Widget.Material3.Button.ElevatedButton" parent="@style/Widget.Material3.Button">
        <item name="android:textColor">@color/m3_text_button_foreground_color_selector</item>
        <item name="android:stateListAnimator">@animator/m3_btn_elevated_btn_state_list_anim</item>
        <item name="elevation">@dimen/m3_btn_elevated_btn_elevation</item>
        <item name="iconTint">@color/m3_text_button_foreground_color_selector</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Button.ElevatedButton</item>
        <item name="rippleColor">@color/m3_text_button_ripple_color_selector</item>
    </style>
    <style name="Widget.Material3.Button.ElevatedButton.Icon" parent="@style/Widget.Material3.Button.ElevatedButton">
        <item name="android:paddingLeft">@dimen/m3_btn_icon_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/m3_btn_icon_btn_padding_right</item>
    </style>
    <style name="Widget.Material3.Button.Icon" parent="@style/Widget.Material3.Button">
        <item name="android:paddingLeft">@dimen/m3_btn_icon_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/m3_btn_icon_btn_padding_right</item>
    </style>
    <style name="Widget.Material3.Button.IconButton" parent="@style/Widget.Material3.Button.TextButton">
        <item name="android:paddingLeft">@dimen/m3_btn_icon_only_default_padding</item>
        <item name="android:paddingTop">@dimen/m3_btn_icon_only_default_padding</item>
        <item name="android:paddingRight">@dimen/m3_btn_icon_only_default_padding</item>
        <item name="android:paddingBottom">@dimen/m3_btn_icon_only_default_padding</item>
        <item name="android:minWidth">@dimen/m3_btn_icon_only_min_width</item>
        <item name="android:insetLeft">@dimen/m3_btn_inset</item>
        <item name="android:insetRight">@dimen/m3_btn_inset</item>
        <item name="iconPadding">@dimen/m3_btn_icon_only_icon_padding</item>
        <item name="iconSize">@dimen/m3_btn_icon_only_default_size</item>
        <item name="iconTint">@color/m3_icon_button_icon_color_selector</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Button.IconButton</item>
    </style>
    <style name="Widget.Material3.Button.IconButton.Filled" parent="@style/Widget.Material3.Button.IconButton">
        <item name="backgroundTint">@color/m3_filled_icon_button_container_color_selector</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Button.IconButton.Filled</item>
    </style>
    <style name="Widget.Material3.Button.IconButton.Filled.Tonal" parent="@style/Widget.Material3.Button.IconButton.Filled">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Button.IconButton.Filled.Tonal</item>
        <item name="rippleColor">@color/m3_tonal_button_ripple_color_selector</item>
    </style>
    <style name="Widget.Material3.Button.IconButton.Outlined" parent="@style/Widget.Material3.Button.IconButton">
        <item name="strokeColor">@color/m3_button_outline_color_selector</item>
        <item name="strokeWidth">@dimen/m3_comp_outlined_icon_button_unselected_outline_width</item>
    </style>
    <style name="Widget.Material3.Button.OutlinedButton" parent="@style/Widget.Material3.Button.TextButton">
        <item name="android:paddingLeft">@dimen/m3_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/m3_btn_padding_right</item>
        <item name="strokeColor">@color/m3_button_outline_color_selector</item>
        <item name="strokeWidth">@dimen/m3_comp_outlined_button_outline_width</item>
    </style>
    <style name="Widget.Material3.Button.OutlinedButton.Icon" parent="@style/Widget.Material3.Button.OutlinedButton">
        <item name="android:paddingLeft">@dimen/m3_btn_icon_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/m3_btn_icon_btn_padding_right</item>
    </style>
    <style name="Widget.Material3.Button.TextButton" parent="@style/Widget.MaterialComponents.Button.TextButton">
        <item name="android:textAppearance">?attr/textAppearanceLabelLarge</item>
        <item name="android:textColor">@color/m3_text_button_foreground_color_selector</item>
        <item name="android:paddingLeft">@dimen/m3_btn_text_btn_padding_left</item>
        <item name="android:paddingTop">@dimen/m3_btn_padding_top</item>
        <item name="android:paddingRight">@dimen/m3_btn_text_btn_padding_right</item>
        <item name="android:paddingBottom">@dimen/m3_btn_padding_bottom</item>
        <item name="android:maxWidth">@dimen/m3_btn_max_width</item>
        <item name="android:insetTop">@dimen/m3_btn_inset</item>
        <item name="android:insetBottom">@dimen/m3_btn_inset</item>
        <item name="backgroundTint">@color/m3_text_button_background_color_selector</item>
        <item name="enforceTextAppearance">false</item>
        <item name="iconTint">@color/m3_text_button_foreground_color_selector</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Button.TextButton</item>
        <item name="rippleColor">@color/m3_text_button_ripple_color_selector</item>
        <item name="shapeAppearance">@style/ShapeAppearance.M3.Comp.TextButton.Container.Shape</item>
    </style>
    <style name="Widget.Material3.Button.TextButton.Dialog" parent="@style/Widget.Material3.Button.TextButton">
        <item name="android:ellipsize">end</item>
        <item name="android:layout_marginLeft">@dimen/m3_btn_dialog_btn_spacing</item>
        <item name="android:minWidth">@dimen/m3_btn_dialog_btn_min_width</item>
        <item name="android:lines">1</item>
        <item name="android:singleLine">true</item>
        <item name="android:layout_marginStart">@dimen/m3_btn_dialog_btn_spacing</item>
    </style>
    <style name="Widget.Material3.Button.TextButton.Dialog.Flush" parent="@style/Widget.Material3.Button.TextButton.Dialog">
        <item name="android:layout_marginLeft">0dp</item>
        <item name="android:layout_marginStart">0dp</item>
    </style>
    <style name="Widget.Material3.Button.TextButton.Dialog.Icon" parent="@style/Widget.Material3.Button.TextButton.Dialog">
        <item name="android:paddingLeft">@dimen/m3_btn_text_btn_icon_padding_left</item>
        <item name="android:paddingRight">@dimen/m3_btn_text_btn_icon_padding_right</item>
    </style>
    <style name="Widget.Material3.Button.TextButton.Icon" parent="@style/Widget.Material3.Button.TextButton">
        <item name="android:paddingLeft">@dimen/m3_btn_text_btn_icon_padding_left</item>
        <item name="android:paddingRight">@dimen/m3_btn_text_btn_icon_padding_right</item>
    </style>
    <style name="Widget.Material3.Button.TextButton.Snackbar" parent="@style/Widget.Material3.Button.TextButton">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Button.TextButton.Snackbar</item>
    </style>
    <style name="Widget.Material3.Button.TonalButton" parent="@style/Widget.Material3.Button">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Button.TonalButton</item>
        <item name="rippleColor">@color/m3_tonal_button_ripple_color_selector</item>
    </style>
    <style name="Widget.Material3.Button.TonalButton.Icon" parent="@style/Widget.Material3.Button.TonalButton">
        <item name="android:paddingLeft">@dimen/m3_btn_icon_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/m3_btn_icon_btn_padding_right</item>
    </style>
    <style name="Widget.Material3.Button.UnelevatedButton" parent="@style/Widget.Material3.Button">
        <item name="android:stateListAnimator">@animator/mtrl_btn_unelevated_state_list_anim</item>
        <item name="elevation">0dp</item>
    </style>
    <style name="Widget.Material3.CardView.Elevated" parent="@style/Base.Widget.Material3.CardView">
        <item name="android:stateListAnimator">@animator/m3_card_elevated_state_list_anim</item>
        <item name="cardBackgroundColor">?attr/colorSurfaceContainerLow</item>
        <item name="cardElevation">@dimen/m3_card_elevated_elevation</item>
        <item name="checkedIconSize">@dimen/m3_comp_elevated_card_icon_size</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerMedium</item>
        <item name="strokeWidth">0dp</item>
    </style>
    <style name="Widget.Material3.CardView.Filled" parent="@style/Base.Widget.Material3.CardView">
        <item name="cardBackgroundColor">?attr/colorSurfaceContainerHighest</item>
        <item name="cardElevation">@dimen/m3_comp_filled_card_container_elevation</item>
        <item name="checkedIconSize">@dimen/m3_comp_filled_card_icon_size</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerMedium</item>
        <item name="strokeWidth">0dp</item>
    </style>
    <style name="Widget.Material3.CardView.Outlined" parent="@style/Base.Widget.Material3.CardView">
        <item name="cardBackgroundColor">?attr/colorSurface</item>
        <item name="cardElevation">@dimen/m3_comp_outlined_card_container_elevation</item>
        <item name="checkedIconSize">@dimen/m3_comp_outlined_card_icon_size</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerMedium</item>
        <item name="strokeWidth">@dimen/m3_comp_outlined_card_outline_width</item>
    </style>
    <style name="Widget.Material3.CheckedTextView" parent="@style/Widget.MaterialComponents.CheckedTextView">
        <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
        <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="Widget.Material3.Chip.Assist" parent="@style/Base.Widget.Material3.Chip">
        <item name="android:textAppearance">?attr/textAppearanceLabelLarge</item>
        <item name="android:textColor">@color/m3_chip_assist_text_color</item>
        <item name="android:checkable">false</item>
        <item name="android:elevation">@dimen/m3_comp_assist_chip_flat_container_elevation</item>
        <item name="checkedIconVisible">false</item>
        <item name="chipEndPadding">8dp</item>
        <item name="chipIconSize">@dimen/m3_comp_assist_chip_with_icon_icon_size</item>
        <item name="chipIconVisible">true</item>
        <item name="chipMinHeight">@dimen/m3_comp_assist_chip_container_height</item>
        <item name="chipStartPadding">8dp</item>
        <item name="chipStrokeWidth">@dimen/m3_comp_assist_chip_flat_outline_width</item>
        <item name="closeIconEndPadding">0dp</item>
        <item name="closeIconStartPadding">0dp</item>
        <item name="closeIconVisible">false</item>
        <item name="iconEndPadding">0dp</item>
        <item name="iconStartPadding">0dp</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Chip.Assist</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerSmall</item>
        <item name="textEndPadding">8dp</item>
        <item name="textStartPadding">8dp</item>
    </style>
    <style name="Widget.Material3.Chip.Assist.Elevated" parent="@style/Widget.Material3.Chip.Assist">
        <item name="android:elevation">@dimen/m3_comp_assist_chip_elevated_container_elevation</item>
        <item name="android:stateListAnimator">@animator/m3_elevated_chip_state_list_anim</item>
        <item name="chipBackgroundColor">@color/m3_elevated_chip_background_color</item>
        <item name="chipStrokeColor">@android:color/transparent</item>
    </style>
    <style name="Widget.Material3.Chip.Filter" parent="@style/Base.Widget.Material3.Chip">
        <item name="android:textAppearance">?attr/textAppearanceLabelLarge</item>
        <item name="android:checkable">true</item>
        <item name="android:elevation">@dimen/m3_comp_filter_chip_flat_container_elevation</item>
        <item name="checkedIcon">@drawable/ic_m3_chip_check</item>
        <item name="chipEndPadding">10dp</item>
        <item name="chipIconSize">@dimen/m3_comp_filter_chip_with_icon_icon_size</item>
        <item name="chipIconVisible">false</item>
        <item name="chipMinHeight">@dimen/m3_comp_filter_chip_container_height</item>
        <item name="chipStartPadding">8dp</item>
        <item name="chipStrokeWidth">@dimen/m3_comp_filter_chip_flat_unselected_outline_width</item>
        <item name="closeIconEndPadding">0dp</item>
        <item name="closeIconStartPadding">-2dp</item>
        <item name="closeIconVisible">false</item>
        <item name="iconEndPadding">-2dp</item>
        <item name="iconStartPadding">0dp</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerSmall</item>
        <item name="textEndPadding">6dp</item>
        <item name="textStartPadding">8dp</item>
    </style>
    <style name="Widget.Material3.Chip.Filter.Elevated" parent="@style/Widget.Material3.Chip.Filter">
        <item name="android:elevation">@dimen/m3_comp_filter_chip_elevated_container_elevation</item>
        <item name="android:stateListAnimator">@animator/m3_elevated_chip_state_list_anim</item>
        <item name="chipBackgroundColor">@color/m3_elevated_chip_background_color</item>
        <item name="chipStrokeColor">@android:color/transparent</item>
    </style>
    <style name="Widget.Material3.Chip.Input" parent="@style/Base.Widget.Material3.Chip">
        <item name="android:textAppearance">?attr/textAppearanceLabelLarge</item>
        <item name="android:checkable">true</item>
        <item name="android:elevation">@dimen/m3_comp_input_chip_container_elevation</item>
        <item name="chipEndPadding">4dp</item>
        <item name="chipIconSize">@dimen/m3_comp_input_chip_with_avatar_avatar_size</item>
        <item name="chipMinHeight">@dimen/m3_comp_input_chip_container_height</item>
        <item name="chipStartPadding">4dp</item>
        <item name="chipStrokeWidth">@dimen/m3_comp_input_chip_unselected_outline_width</item>
        <item name="closeIconEndPadding">4dp</item>
        <item name="closeIconStartPadding">0dp</item>
        <item name="iconEndPadding">0dp</item>
        <item name="iconStartPadding">0dp</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerSmall</item>
        <item name="textEndPadding">8dp</item>
        <item name="textStartPadding">8dp</item>
    </style>
    <style name="Widget.Material3.Chip.Input.Elevated" parent="@style/Widget.Material3.Chip.Input">
        <item name="android:elevation">@dimen/m3_chip_elevated_elevation</item>
        <item name="android:stateListAnimator">@animator/m3_elevated_chip_state_list_anim</item>
        <item name="chipBackgroundColor">@color/m3_elevated_chip_background_color</item>
        <item name="chipStrokeColor">@android:color/transparent</item>
    </style>
    <style name="Widget.Material3.Chip.Input.Icon" parent="@style/Widget.Material3.Chip.Input">
        <item name="chipIconSize">@dimen/m3_comp_input_chip_with_leading_icon_leading_icon_size</item>
        <item name="iconStartPadding">4dp</item>
    </style>
    <style name="Widget.Material3.Chip.Input.Icon.Elevated" parent="@style/Widget.Material3.Chip.Input.Icon">
        <item name="android:elevation">@dimen/m3_chip_elevated_elevation</item>
        <item name="android:stateListAnimator">@animator/m3_elevated_chip_state_list_anim</item>
        <item name="chipBackgroundColor">@color/m3_elevated_chip_background_color</item>
        <item name="chipStrokeColor">@android:color/transparent</item>
    </style>
    <style name="Widget.Material3.Chip.Suggestion" parent="@style/Base.Widget.Material3.Chip">
        <item name="android:textAppearance">?attr/textAppearanceLabelLarge</item>
        <item name="android:checkable">true</item>
        <item name="android:elevation">@dimen/m3_comp_suggestion_chip_flat_container_elevation</item>
        <item name="checkedIconVisible">false</item>
        <item name="chipEndPadding">10dp</item>
        <item name="chipIconSize">@dimen/m3_comp_suggestion_chip_with_leading_icon_leading_icon_size</item>
        <item name="chipIconVisible">false</item>
        <item name="chipMinHeight">@dimen/m3_comp_suggestion_chip_container_height</item>
        <item name="chipStartPadding">8dp</item>
        <item name="chipStrokeWidth">@dimen/m3_comp_suggestion_chip_flat_outline_width</item>
        <item name="closeIconEndPadding">0dp</item>
        <item name="closeIconStartPadding">-2dp</item>
        <item name="closeIconVisible">false</item>
        <item name="iconEndPadding">-2dp</item>
        <item name="iconStartPadding">0dp</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerSmall</item>
        <item name="textEndPadding">6dp</item>
        <item name="textStartPadding">8dp</item>
    </style>
    <style name="Widget.Material3.Chip.Suggestion.Elevated" parent="@style/Widget.Material3.Chip.Suggestion">
        <item name="android:elevation">@dimen/m3_comp_suggestion_chip_elevated_container_elevation</item>
        <item name="android:stateListAnimator">@animator/m3_elevated_chip_state_list_anim</item>
        <item name="chipBackgroundColor">@color/m3_elevated_chip_background_color</item>
        <item name="chipStrokeColor">@android:color/transparent</item>
    </style>
    <style name="Widget.Material3.ChipGroup" parent="@style/Widget.MaterialComponents.ChipGroup">
        <item name="chipSpacingVertical">8dp</item>
    </style>
    <style name="Widget.Material3.CircularProgressIndicator" parent="@style/Widget.MaterialComponents.CircularProgressIndicator">
        <item name="indicatorColor">?attr/colorPrimary</item>
        <item name="indicatorTrackGapSize">@dimen/m3_comp_progress_indicator_active_indicator_track_space</item>
        <item name="trackColor">?attr/colorSecondaryContainer</item>
        <item name="trackCornerRadius">2dp</item>
        <item name="trackThickness">@dimen/m3_comp_progress_indicator_track_thickness</item>
    </style>
    <style name="Widget.Material3.CircularProgressIndicator.ExtraSmall" parent="@style/Widget.Material3.CircularProgressIndicator">
        <item name="indicatorInset">@dimen/mtrl_progress_circular_inset_extra_small</item>
        <item name="indicatorSize">@dimen/mtrl_progress_circular_size_extra_small</item>
        <item name="indicatorTrackGapSize">2dp</item>
        <item name="trackCornerRadius">1.25dp</item>
        <item name="trackThickness">@dimen/mtrl_progress_circular_track_thickness_extra_small</item>
    </style>
    <style name="Widget.Material3.CircularProgressIndicator.Legacy" parent="@style/Widget.MaterialComponents.CircularProgressIndicator">
        <item name="indicatorColor">?attr/colorPrimary</item>
        <item name="indicatorTrackGapSize">0dp</item>
        <item name="trackColor">@android:color/transparent</item>
        <item name="trackCornerRadius">0dp</item>
        <item name="trackThickness">4dp</item>
    </style>
    <style name="Widget.Material3.CircularProgressIndicator.Legacy.ExtraSmall" parent="@style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall">
    </style>
    <style name="Widget.Material3.CircularProgressIndicator.Legacy.Medium" parent="@style/Widget.MaterialComponents.CircularProgressIndicator.Medium">
    </style>
    <style name="Widget.Material3.CircularProgressIndicator.Legacy.Small" parent="@style/Widget.MaterialComponents.CircularProgressIndicator.Small">
    </style>
    <style name="Widget.Material3.CircularProgressIndicator.Medium" parent="@style/Widget.Material3.CircularProgressIndicator">
    </style>
    <style name="Widget.Material3.CircularProgressIndicator.Small" parent="@style/Widget.Material3.CircularProgressIndicator">
        <item name="indicatorInset">@dimen/mtrl_progress_circular_inset_small</item>
        <item name="indicatorSize">@dimen/mtrl_progress_circular_size_small</item>
        <item name="indicatorTrackGapSize">3dp</item>
        <item name="trackCornerRadius">1.5dp</item>
        <item name="trackThickness">@dimen/mtrl_progress_circular_track_thickness_small</item>
    </style>
    <style name="Widget.Material3.CollapsingToolbar" parent="@style/Base.Widget.Material3.CollapsingToolbar">
        <item name="expandedTitleTextAppearance">?attr/textAppearanceHeadlineSmall</item>
        <item name="scrimVisibleHeightTrigger">@dimen/m3_appbar_scrim_height_trigger</item>
    </style>
    <style name="Widget.Material3.CollapsingToolbar.Large" parent="@style/Base.Widget.Material3.CollapsingToolbar">
        <item name="collapsedTitleTextColor">?attr/colorOnSurface</item>
        <item name="expandedTitleTextAppearance">?attr/textAppearanceHeadlineMedium</item>
        <item name="expandedTitleTextColor">?attr/colorOnSurface</item>
        <item name="scrimVisibleHeightTrigger">@dimen/m3_appbar_scrim_height_trigger_large</item>
    </style>
    <style name="Widget.Material3.CollapsingToolbar.Medium" parent="@style/Base.Widget.Material3.CollapsingToolbar">
        <item name="collapsedTitleTextColor">?attr/colorOnSurface</item>
        <item name="expandedTitleTextAppearance">?attr/textAppearanceHeadlineSmall</item>
        <item name="expandedTitleTextColor">?attr/colorOnSurface</item>
        <item name="scrimVisibleHeightTrigger">@dimen/m3_appbar_scrim_height_trigger_medium</item>
    </style>
    <style name="Widget.Material3.CompoundButton.CheckBox" parent="@style/Base.Widget.Material3.CompoundButton.CheckBox">
        <item name="android:background">@drawable/m3_selection_control_ripple</item>
    </style>
    <style name="Widget.Material3.CompoundButton.MaterialSwitch" parent="@style/Widget.AppCompat.CompoundButton.Switch">
        <item name="android:textAppearance">?attr/textAppearanceBodyMedium</item>
        <item name="android:minHeight">?attr/minTouchTargetSize</item>
        <item name="android:thumb">@drawable/mtrl_switch_thumb</item>
        <item name="switchPadding">@dimen/mtrl_switch_text_padding</item>
        <item name="thumbIconSize">@dimen/mtrl_switch_thumb_icon_size</item>
        <item name="thumbIconTint">@color/mtrl_switch_thumb_icon_tint</item>
        <item name="thumbTint">@color/mtrl_switch_thumb_tint</item>
        <item name="track">@drawable/mtrl_switch_track</item>
        <item name="trackDecoration">@drawable/mtrl_switch_track_decoration</item>
        <item name="trackDecorationTint">@color/mtrl_switch_track_decoration_tint</item>
        <item name="trackTint">@color/mtrl_switch_track_tint</item>
    </style>
    <style name="Widget.Material3.CompoundButton.RadioButton" parent="@style/Base.Widget.Material3.CompoundButton.RadioButton">
        <item name="android:background">@drawable/m3_radiobutton_ripple</item>
    </style>
    <style name="Widget.Material3.CompoundButton.Switch" parent="@style/Base.Widget.Material3.CompoundButton.Switch">
        <item name="android:background">@drawable/m3_selection_control_ripple</item>
    </style>
    <style name="Widget.Material3.DrawerLayout" parent="@android:style/Widget">
        <item name="elevation">@dimen/m3_comp_navigation_drawer_modal_container_elevation</item>
    </style>
    <style name="Widget.Material3.ExtendedFloatingActionButton.Icon.Primary" parent="@style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary</item>
    </style>
    <style name="Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary" parent="@style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary</item>
    </style>
    <style name="Widget.Material3.ExtendedFloatingActionButton.Icon.Surface" parent="@style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface</item>
    </style>
    <style name="Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary" parent="@style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary</item>
    </style>
    <style name="Widget.Material3.ExtendedFloatingActionButton.Primary" parent="@style/Base.Widget.Material3.ExtendedFloatingActionButton">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary</item>
    </style>
    <style name="Widget.Material3.ExtendedFloatingActionButton.Secondary" parent="@style/Base.Widget.Material3.ExtendedFloatingActionButton">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary</item>
    </style>
    <style name="Widget.Material3.ExtendedFloatingActionButton.Surface" parent="@style/Base.Widget.Material3.ExtendedFloatingActionButton">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface</item>
    </style>
    <style name="Widget.Material3.ExtendedFloatingActionButton.Tertiary" parent="@style/Base.Widget.Material3.ExtendedFloatingActionButton">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary</item>
    </style>
    <style name="Widget.Material3.FloatingActionButton.Large.Primary" parent="@style/Base.Widget.Material3.FloatingActionButton.Large">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary</item>
    </style>
    <style name="Widget.Material3.FloatingActionButton.Large.Secondary" parent="@style/Base.Widget.Material3.FloatingActionButton.Large">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary</item>
    </style>
    <style name="Widget.Material3.FloatingActionButton.Large.Surface" parent="@style/Base.Widget.Material3.FloatingActionButton.Large">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface</item>
    </style>
    <style name="Widget.Material3.FloatingActionButton.Large.Tertiary" parent="@style/Base.Widget.Material3.FloatingActionButton.Large">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary</item>
    </style>
    <style name="Widget.Material3.FloatingActionButton.Primary" parent="@style/Base.Widget.Material3.FloatingActionButton">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Primary</item>
    </style>
    <style name="Widget.Material3.FloatingActionButton.Secondary" parent="@style/Base.Widget.Material3.FloatingActionButton">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Secondary</item>
    </style>
    <style name="Widget.Material3.FloatingActionButton.Small.Primary" parent="@style/Base.Widget.Material3.FloatingActionButton.Small">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary</item>
    </style>
    <style name="Widget.Material3.FloatingActionButton.Small.Secondary" parent="@style/Base.Widget.Material3.FloatingActionButton.Small">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary</item>
    </style>
    <style name="Widget.Material3.FloatingActionButton.Small.Surface" parent="@style/Base.Widget.Material3.FloatingActionButton.Small">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface</item>
    </style>
    <style name="Widget.Material3.FloatingActionButton.Small.Tertiary" parent="@style/Base.Widget.Material3.FloatingActionButton.Small">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary</item>
    </style>
    <style name="Widget.Material3.FloatingActionButton.Surface" parent="@style/Base.Widget.Material3.FloatingActionButton">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Surface</item>
    </style>
    <style name="Widget.Material3.FloatingActionButton.Tertiary" parent="@style/Base.Widget.Material3.FloatingActionButton">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.FloatingActionButton.Tertiary</item>
    </style>
    <style name="Widget.Material3.Light.ActionBar.Solid" parent="@style/Base.Widget.Material3.Light.ActionBar.Solid">
    </style>
    <style name="Widget.Material3.LinearProgressIndicator" parent="@style/Widget.MaterialComponents.LinearProgressIndicator">
        <item name="indicatorColor">?attr/colorPrimary</item>
        <item name="indicatorTrackGapSize">@dimen/m3_comp_progress_indicator_active_indicator_track_space</item>
        <item name="trackColor">?attr/colorSecondaryContainer</item>
        <item name="trackCornerRadius">2dp</item>
        <item name="trackStopIndicatorSize">@dimen/m3_comp_progress_indicator_stop_indicator_size</item>
        <item name="trackThickness">@dimen/m3_comp_progress_indicator_track_thickness</item>
    </style>
    <style name="Widget.Material3.LinearProgressIndicator.Legacy" parent="@style/Widget.MaterialComponents.LinearProgressIndicator">
        <item name="indicatorColor">?attr/colorPrimary</item>
        <item name="indicatorTrackGapSize">0dp</item>
        <item name="trackColor">?attr/colorSurfaceContainerHighest</item>
        <item name="trackCornerRadius">0dp</item>
        <item name="trackStopIndicatorSize">0dp</item>
        <item name="trackThickness">4dp</item>
    </style>
    <style name="Widget.Material3.MaterialButtonToggleGroup" parent="@style/Widget.MaterialComponents.MaterialButtonToggleGroup">
    </style>
    <style name="Widget.Material3.MaterialCalendar" parent="@style/Widget.MaterialComponents.MaterialCalendar">
        <item name="android:windowFullscreen">false</item>
        <item name="backgroundTint">?attr/colorSurfaceContainerHigh</item>
        <item name="dayInvalidStyle">@style/Widget.Material3.MaterialCalendar.Day.Invalid</item>
        <item name="daySelectedStyle">@style/Widget.Material3.MaterialCalendar.Day.Selected</item>
        <item name="dayStyle">@style/Widget.Material3.MaterialCalendar.Day</item>
        <item name="dayTodayStyle">@style/Widget.Material3.MaterialCalendar.Day.Today</item>
        <item name="rangeFillColor">?attr/colorSecondaryContainer</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerExtraLarge</item>
        <item name="yearSelectedStyle">@style/Widget.Material3.MaterialCalendar.Year.Selected</item>
        <item name="yearStyle">@style/Widget.Material3.MaterialCalendar.Year</item>
        <item name="yearTodayStyle">@style/Widget.Material3.MaterialCalendar.Year.Today</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.Day" parent="@style/Widget.Material3.MaterialCalendar.Item">
        <item name="android:height">@dimen/mtrl_calendar_day_height</item>
        <item name="android:width">@dimen/mtrl_calendar_day_width</item>
        <item name="android:insetLeft">@dimen/mtrl_calendar_day_horizontal_padding</item>
        <item name="android:insetRight">@dimen/mtrl_calendar_day_horizontal_padding</item>
        <item name="android:insetTop">@dimen/mtrl_calendar_day_vertical_padding</item>
        <item name="android:insetBottom">@dimen/mtrl_calendar_day_vertical_padding</item>
        <item name="itemShapeAppearance">@style/ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape</item>
        <item name="itemTextColor">?attr/colorOnSurface</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.Day.Invalid" parent="@style/Widget.Material3.MaterialCalendar.Day">
        <item name="itemStrokeWidth">0dp</item>
        <item name="itemTextColor">@color/m3_calendar_item_disabled_text</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.Day.Selected" parent="@style/Widget.Material3.MaterialCalendar.Day">
        <item name="itemFillColor">?attr/colorPrimary</item>
        <item name="itemStrokeWidth">0dp</item>
        <item name="itemTextColor">?attr/colorOnPrimary</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.Day.Today" parent="@style/Widget.Material3.MaterialCalendar.Day">
        <item name="itemStrokeColor">?attr/colorPrimary</item>
        <item name="itemStrokeWidth">@dimen/m3_comp_date_picker_modal_date_today_container_outline_width</item>
        <item name="itemTextColor">?attr/colorPrimary</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.DayOfWeekLabel" parent="@style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel">
        <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.DayTextView" parent="@style/Widget.MaterialComponents.MaterialCalendar.DayTextView">
        <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.Fullscreen" parent="@style/Widget.Material3.MaterialCalendar">
        <item name="android:windowFullscreen">true</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Material3.Corner.None</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.HeaderCancelButton" parent="@style/Widget.Material3.Button.TextButton">
        <item name="iconTint">?attr/colorOnSurfaceVariant</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.HeaderDivider" parent="@style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider">
        <item name="android:visibility">gone</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.HeaderLayout" parent="@style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:layout_height">@dimen/m3_comp_date_picker_modal_header_container_height</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen" parent="@style/Widget.Material3.MaterialCalendar.HeaderLayout">
        <item name="android:layout_height">@dimen/m3_comp_date_picker_modal_range_selection_header_container_height</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.HeaderSelection" parent="@style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection">
        <item name="android:textAppearance">?attr/textAppearanceHeadlineLarge</item>
        <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen" parent="@style/Widget.Material3.MaterialCalendar.HeaderSelection">
        <item name="android:textAppearance">?attr/textAppearanceTitleLarge</item>
        <item name="android:maxLines">1</item>
        <item name="autoSizeMaxTextSize">20sp</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.HeaderTitle" parent="@style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle">
        <item name="android:textAppearance">?attr/textAppearanceLabelLarge</item>
        <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
        <item name="autoSizeMaxTextSize">14sp</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.HeaderToggleButton" parent="@style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton">
        <item name="android:tint">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.Item" parent="">
        <item name="itemFillColor">@android:color/transparent</item>
        <item name="itemShapeAppearance">@style/ShapeAppearance.Material3.Corner.Full</item>
        <item name="itemStrokeColor">@color/m3_calendar_item_stroke_color</item>
        <item name="itemStrokeWidth">1dp</item>
        <item name="itemTextColor">?attr/colorOnSurface</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.MonthNavigationButton" parent="@style/Base.Widget.Material3.MaterialCalendar.NavigationButton">
        <item name="iconGravity">textStart</item>
        <item name="iconPadding">0dp</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.MonthTextView" parent="@style/Widget.MaterialComponents.MaterialCalendar.MonthTextView">
        <item name="android:textAppearance">?attr/textAppearanceTitleSmall</item>
        <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.Year" parent="@style/Widget.Material3.MaterialCalendar.Item">
        <item name="android:height">@dimen/mtrl_calendar_year_height</item>
        <item name="android:width">@dimen/mtrl_calendar_year_width</item>
        <item name="android:insetLeft">@dimen/mtrl_calendar_year_horizontal_padding</item>
        <item name="android:insetRight">@dimen/mtrl_calendar_year_horizontal_padding</item>
        <item name="android:insetTop">@dimen/mtrl_calendar_year_vertical_padding</item>
        <item name="android:insetBottom">@dimen/mtrl_calendar_year_vertical_padding</item>
        <item name="itemTextColor">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.Year.Selected" parent="@style/Widget.Material3.MaterialCalendar.Year">
        <item name="itemFillColor">?attr/colorPrimary</item>
        <item name="itemStrokeWidth">0dp</item>
        <item name="itemTextColor">?attr/colorOnPrimary</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.Year.Today" parent="@style/Widget.Material3.MaterialCalendar.Year">
        <item name="itemStrokeColor">?attr/colorPrimary</item>
        <item name="itemStrokeWidth">@dimen/mtrl_calendar_day_today_stroke</item>
        <item name="itemTextColor">?attr/colorPrimary</item>
    </style>
    <style name="Widget.Material3.MaterialCalendar.YearNavigationButton" parent="@style/Base.Widget.Material3.MaterialCalendar.NavigationButton">
    </style>
    <style name="Widget.Material3.MaterialDivider" parent="@style/Widget.MaterialComponents.MaterialDivider">
        <item name="dividerColor">?attr/colorOutlineVariant</item>
        <item name="dividerThickness">@dimen/m3_comp_divider_thickness</item>
    </style>
    <style name="Widget.Material3.MaterialDivider.Heavy" parent="@style/Widget.Material3.MaterialDivider">
        <item name="dividerThickness">@dimen/m3_divider_heavy_thickness</item>
    </style>
    <style name="Widget.Material3.MaterialTimePicker" parent="@style/Widget.MaterialComponents.TimePicker">
        <item name="backgroundTint">?attr/colorSurfaceContainerHigh</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerExtraLarge</item>
    </style>
    <style name="Widget.Material3.MaterialTimePicker.Button" parent="@style/Widget.MaterialComponents.TimePicker.Button">
        <item name="android:textAppearance">?attr/textAppearanceTitleMedium</item>
        <item name="android:textColor">@color/m3_timepicker_button_text_color</item>
        <item name="backgroundTint">@color/m3_timepicker_button_background_color</item>
        <item name="iconTint">@color/m3_timepicker_button_text_color</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TimePicker.Display</item>
        <item name="rippleColor">@color/m3_timepicker_button_ripple_color</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerSmall</item>
        <item name="shapeAppearanceOverlay">@null</item>
        <item name="strokeColor">?attr/colorOutline</item>
        <item name="strokeWidth">@dimen/m3_comp_time_picker_period_selector_outline_width</item>
    </style>
    <style name="Widget.Material3.MaterialTimePicker.Clock" parent="@style/Widget.MaterialComponents.TimePicker.Clock">
        <item name="clockFaceBackgroundColor">?attr/colorSurfaceContainerHighest</item>
        <item name="clockHandColor">?attr/colorPrimary</item>
        <item name="clockNumberTextColor">@color/m3_timepicker_clock_text_color</item>
    </style>
    <style name="Widget.Material3.MaterialTimePicker.Display" parent="@style/Widget.MaterialComponents.TimePicker.Display">
        <item name="android:textAppearance">?attr/textAppearanceDisplayLarge</item>
        <item name="android:textColor">@color/m3_timepicker_display_text_color</item>
        <item name="chipBackgroundColor">@color/m3_timepicker_display_background_color</item>
        <item name="rippleColor">@color/m3_timepicker_display_ripple_color</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerSmall</item>
        <item name="shapeAppearanceOverlay">@null</item>
    </style>
    <style name="Widget.Material3.MaterialTimePicker.Display.Divider" parent="@style/Widget.MaterialComponents.TimePicker.Display.Divider">
        <item name="android:textAppearance">?attr/textAppearanceDisplayLarge</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
    </style>
    <style name="Widget.Material3.MaterialTimePicker.Display.HelperText" parent="@style/Widget.MaterialComponents.TimePicker.Display.HelperText">
        <item name="android:textAppearance">?attr/textAppearanceBodySmall</item>
        <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="Widget.Material3.MaterialTimePicker.Display.TextInputEditText" parent="@style/Widget.Material3.TextInputEditText.OutlinedBox">
        <item name="android:textAppearance">?attr/textAppearanceDisplayLarge</item>
        <item name="android:textSize">56dp</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingTop">0dp</item>
        <item name="android:paddingBottom">0dp</item>
        <item name="android:minEms">2</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:maxLength">2</item>
        <item name="android:inputType">number</item>
        <item name="android:textAlignment">center</item>
        <item name="android:paddingStart">0dp</item>
        <item name="android:paddingEnd">0dp</item>
        <item name="boxStrokeColor">@color/m3_timepicker_time_input_stroke_color</item>
        <item name="boxStrokeWidthFocused">@dimen/m3_comp_time_input_time_input_field_focus_outline_width</item>
    </style>
    <style name="Widget.Material3.MaterialTimePicker.Display.TextInputLayout" parent="@style/Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerSmall</item>
    </style>
    <style name="Widget.Material3.MaterialTimePicker.ImageButton" parent="@style/Widget.MaterialComponents.TimePicker.ImageButton">
        <item name="iconTint">@color/m3_timepicker_secondary_text_button_text_color</item>
        <item name="rippleColor">@color/m3_timepicker_secondary_text_button_ripple_color</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Material3.Corner.Full</item>
    </style>
    <style name="Widget.Material3.NavigationRailView" parent="@style/Widget.MaterialComponents.NavigationRailView">
        <item name="android:background">?attr/colorSurface</item>
        <item name="android:minWidth">@dimen/m3_navigation_rail_default_width</item>
        <item name="backgroundTint">@null</item>
        <item name="elevation">@dimen/m3_navigation_rail_elevation</item>
        <item name="itemActiveIndicatorStyle">@style/Widget.Material3.NavigationRailView.ActiveIndicator</item>
        <item name="itemIconSize">@dimen/m3_comp_navigation_rail_icon_size</item>
        <item name="itemIconTint">@color/m3_navigation_rail_item_with_indicator_icon_tint</item>
        <item name="itemMinHeight">@dimen/m3_navigation_rail_item_min_height</item>
        <item name="itemPaddingBottom">@dimen/m3_navigation_rail_item_padding_bottom</item>
        <item name="itemPaddingTop">@dimen/m3_navigation_rail_item_padding_top</item>
        <item name="itemRippleColor">@color/m3_navigation_rail_ripple_color_selector</item>
        <item name="itemTextAppearanceActive">?attr/textAppearanceLabelMedium</item>
        <item name="itemTextAppearanceInactive">?attr/textAppearanceLabelMedium</item>
        <item name="itemTextColor">@color/m3_navigation_rail_item_with_indicator_label_tint</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.NavigationRailView</item>
        <item name="shapeAppearance">@style/ShapeAppearance.M3.Comp.NavigationRail.Container.Shape</item>
    </style>
    <style name="Widget.Material3.NavigationRailView.ActiveIndicator" parent="">
        <item name="android:height">@dimen/m3_navigation_rail_item_active_indicator_height</item>
        <item name="android:width">@dimen/m3_navigation_rail_item_active_indicator_width</item>
        <item name="android:color">?attr/colorSecondaryContainer</item>
        <item name="marginHorizontal">@dimen/m3_navigation_rail_item_active_indicator_margin_horizontal</item>
        <item name="shapeAppearance">@style/ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape</item>
    </style>
    <style name="Widget.Material3.NavigationRailView.Badge" parent="@style/Widget.Material3.Badge.AdjustToBounds">
        <item name="largeFontVerticalOffsetAdjustment">@dimen/m3_large_text_vertical_offset_adjustment</item>
        <item name="verticalOffsetWithText">@dimen/m3_nav_badge_with_text_vertical_offset</item>
    </style>
    <style name="Widget.Material3.NavigationView" parent="@style/Widget.MaterialComponents.NavigationView">
        <item name="android:background">?attr/colorSurfaceContainerLow</item>
        <item name="android:maxWidth">@dimen/m3_comp_navigation_drawer_container_width</item>
        <item name="activeIndicatorLabelPadding">@dimen/m3_navigation_item_active_indicator_label_padding</item>
        <item name="bottomInsetScrimEnabled">false</item>
        <item name="dividerInsetEnd">@dimen/m3_navigation_menu_divider_horizontal_padding</item>
        <item name="dividerInsetStart">@dimen/m3_navigation_menu_divider_horizontal_padding</item>
        <item name="drawerLayoutCornerSize">@dimen/m3_navigation_drawer_layout_corner_size</item>
        <item name="elevation">@dimen/m3_comp_navigation_drawer_standard_container_elevation</item>
        <item name="itemHorizontalPadding">@dimen/m3_navigation_item_horizontal_padding</item>
        <item name="itemIconPadding">@dimen/m3_navigation_item_icon_padding</item>
        <item name="itemIconSize">@dimen/m3_comp_navigation_drawer_icon_size</item>
        <item name="itemIconTint">@color/m3_navigation_item_icon_tint</item>
        <item name="itemRippleColor">@color/m3_navigation_item_ripple_color</item>
        <item name="itemShapeAppearance">@style/ShapeAppearance.Material3.Corner.Full</item>
        <item name="itemShapeFillColor">@color/m3_navigation_item_background_color</item>
        <item name="itemShapeInsetBottom">@dimen/m3_navigation_item_shape_inset_bottom</item>
        <item name="itemShapeInsetEnd">@dimen/m3_navigation_item_shape_inset_end</item>
        <item name="itemShapeInsetStart">@dimen/m3_navigation_item_shape_inset_start</item>
        <item name="itemShapeInsetTop">@dimen/m3_navigation_item_shape_inset_top</item>
        <item name="itemTextAppearance">?attr/textAppearanceLabelLarge</item>
        <item name="itemTextColor">@color/m3_navigation_item_text_color</item>
        <item name="itemVerticalPadding">@dimen/m3_navigation_item_vertical_padding</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.NavigationView</item>
        <item name="subheaderColor">?attr/colorOnSurfaceVariant</item>
        <item name="subheaderInsetEnd">@dimen/m3_navigation_menu_headline_horizontal_padding</item>
        <item name="subheaderInsetStart">@dimen/m3_navigation_menu_headline_horizontal_padding</item>
        <item name="subheaderTextAppearance">?attr/textAppearanceTitleSmall</item>
        <item name="topInsetScrimEnabled">false</item>
    </style>
    <style name="Widget.Material3.PopupMenu" parent="@style/Widget.MaterialComponents.PopupMenu">
        <item name="android:popupElevation">@dimen/m3_menu_elevation</item>
    </style>
    <style name="Widget.Material3.PopupMenu.ContextMenu" parent="@style/Widget.MaterialComponents.PopupMenu.ContextMenu">
        <item name="android:popupElevation">@dimen/m3_menu_elevation</item>
    </style>
    <style name="Widget.Material3.PopupMenu.ListPopupWindow" parent="@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow">
        <item name="android:popupElevation">@dimen/m3_menu_elevation</item>
    </style>
    <style name="Widget.Material3.PopupMenu.Overflow" parent="@style/Widget.MaterialComponents.PopupMenu.Overflow">
        <item name="android:popupElevation">@dimen/m3_menu_elevation</item>
    </style>
    <style name="Widget.Material3.Search.ActionButton.Overflow" parent="@style/Widget.AppCompat.ActionButton.Overflow">
        <item name="android:minWidth">@dimen/mtrl_min_touch_target_size</item>
    </style>
    <style name="Widget.Material3.Search.Toolbar.Button.Navigation" parent="@style/Widget.AppCompat.Toolbar.Button.Navigation">
        <item name="android:minWidth">@dimen/mtrl_min_touch_target_size</item>
    </style>
    <style name="Widget.Material3.SearchBar" parent="@android:style/Widget">
        <item name="android:textAppearance">@style/TextAppearance.Material3.SearchBar</item>
        <item name="android:minHeight">@dimen/m3_searchbar_height</item>
        <item name="android:paddingStart">@dimen/m3_searchbar_padding_start</item>
        <item name="backgroundTint">?attr/colorSurfaceContainerHigh</item>
        <item name="defaultMarginsEnabled">true</item>
        <item name="defaultScrollFlagsEnabled">true</item>
        <item name="elevation">@dimen/m3_searchbar_elevation</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="hideNavigationIcon">false</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Search</item>
        <item name="maxButtonHeight">@dimen/m3_searchbar_height</item>
        <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.Material3.SearchBar</item>
    </style>
    <style name="Widget.Material3.SearchBar.Outlined" parent="@style/Widget.Material3.SearchBar">
        <item name="backgroundTint">?attr/colorSurface</item>
        <item name="elevation">0dp</item>
        <item name="strokeColor">?attr/colorOutline</item>
        <item name="strokeWidth">@dimen/m3_searchbar_outlined_stroke_width</item>
    </style>
    <style name="Widget.Material3.SearchView" parent="@android:style/Widget">
        <item name="android:textAppearance">@style/TextAppearance.Material3.SearchView</item>
        <item name="android:elevation">@dimen/m3_searchview_elevation</item>
        <item name="backgroundTint">?attr/colorSurfaceContainerHigh</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Search</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.Material3.SearchView</item>
    </style>
    <style name="Widget.Material3.SearchView.Prefix" parent="@android:style/Widget.TextView">
        <item name="android:textAppearance">@style/TextAppearance.Material3.SearchView.Prefix</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:paddingEnd">16dp</item>
    </style>
    <style name="Widget.Material3.SearchView.Toolbar" parent="@style/Widget.AppCompat.Toolbar">
        <item name="android:paddingLeft">0dp</item>
        <item name="android:paddingRight">0dp</item>
        <item name="android:minHeight">@dimen/m3_searchview_height</item>
        <item name="android:paddingStart">0dp</item>
        <item name="android:paddingEnd">0dp</item>
        <item name="contentInsetStartWithNavigation">0dp</item>
        <item name="maxButtonHeight">@dimen/m3_searchview_height</item>
        <item name="navigationIcon">@drawable/ic_arrow_back_black_24</item>
        <item name="navigationIconTint">?attr/colorOnSurface</item>
    </style>
    <style name="Widget.Material3.SideSheet" parent="">
        <item name="android:layout_gravity">end</item>
        <item name="android:elevation">@dimen/m3_side_sheet_standard_elevation</item>
        <item name="backgroundTint">?attr/colorSurface</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="shapeAppearance">@style/ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape</item>
    </style>
    <style name="Widget.Material3.SideSheet.Detached" parent="@style/Widget.Material3.SideSheet">
        <item name="android:layout_margin">@dimen/m3_side_sheet_margin_detached</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerLarge</item>
    </style>
    <style name="Widget.Material3.SideSheet.Modal" parent="@style/Widget.Material3.SideSheet">
        <item name="android:layout_width">@dimen/m3_side_sheet_width</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:elevation">@dimen/m3_side_sheet_modal_elevation</item>
        <item name="backgroundTint">?attr/colorSurfaceContainerLow</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerLarge</item>
    </style>
    <style name="Widget.Material3.SideSheet.Modal.Detached" parent="@style/Widget.Material3.SideSheet.Modal">
        <item name="android:layout_margin">@dimen/m3_side_sheet_margin_detached</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerLarge</item>
    </style>
    <style name="Widget.Material3.Slider" parent="@style/Widget.MaterialComponents.Slider">
        <item name="haloColor">#00ffffff</item>
        <item name="labelStyle">@style/Widget.Material3.Slider.Label</item>
        <item name="thumbColor">@color/m3_slider_thumb_color</item>
        <item name="thumbElevation">0dp</item>
        <item name="thumbHeight">@dimen/m3_comp_slider_active_handle_height</item>
        <item name="thumbTrackGapSize">@dimen/m3_comp_slider_active_handle_leading_space</item>
        <item name="thumbWidth">@dimen/m3_comp_slider_active_handle_width</item>
        <item name="tickColorActive">@color/m3_slider_inactive_track_color</item>
        <item name="tickColorInactive">@color/m3_slider_active_track_color</item>
        <item name="tickRadiusActive">@null</item>
        <item name="tickRadiusInactive">@null</item>
        <item name="trackColorActive">@color/m3_slider_active_track_color</item>
        <item name="trackColorInactive">@color/m3_slider_inactive_track_color</item>
        <item name="trackHeight">@dimen/m3_comp_slider_inactive_track_height</item>
        <item name="trackInsideCornerSize">2dp</item>
        <item name="trackStopIndicatorSize">@dimen/m3_comp_slider_stop_indicator_size</item>
    </style>
    <style name="Widget.Material3.Slider.Label" parent="@style/Widget.Material3.Tooltip">
        <item name="android:textColor">?attr/colorOnSurfaceInverse</item>
        <item name="android:padding">10dp</item>
        <item name="android:minHeight">32dp</item>
        <item name="backgroundTint">?attr/colorSurfaceInverse</item>
        <item name="showMarker">false</item>
    </style>
    <style name="Widget.Material3.Slider.Legacy" parent="@style/Widget.MaterialComponents.Slider">
        <item name="haloColor">@color/m3_slider_halo_color_legacy</item>
        <item name="labelStyle">@style/Widget.Material3.Slider.Legacy.Label</item>
        <item name="thumbColor">@color/m3_slider_thumb_color_legacy</item>
        <item name="thumbElevation">@dimen/m3_slider_thumb_elevation</item>
        <item name="thumbHeight">@null</item>
        <item name="thumbTrackGapSize">0dp</item>
        <item name="thumbWidth">@null</item>
        <item name="tickColorActive">@color/m3_slider_inactive_track_color_legacy</item>
        <item name="tickColorInactive">@color/m3_slider_active_track_color_legacy</item>
        <item name="trackColorActive">@color/m3_slider_active_track_color_legacy</item>
        <item name="trackColorInactive">@color/m3_slider_inactive_track_color_legacy</item>
        <item name="trackHeight">4dp</item>
        <item name="trackInsideCornerSize">0dp</item>
        <item name="trackStopIndicatorSize">0dp</item>
    </style>
    <style name="Widget.Material3.Slider.Legacy.Label" parent="@style/Widget.Material3.Tooltip">
        <item name="android:textColor">?attr/colorOnPrimary</item>
        <item name="backgroundTint">?attr/colorPrimary</item>
    </style>
    <style name="Widget.Material3.Snackbar" parent="@style/Base.Widget.Material3.Snackbar">
        <item name="android:background">@null</item>
        <item name="android:layout_margin">@dimen/m3_snackbar_margin</item>
        <item name="animationMode">fade</item>
        <item name="elevation">@dimen/m3_comp_snackbar_container_elevation</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerExtraSmall</item>
    </style>
    <style name="Widget.Material3.Snackbar.FullWidth" parent="@style/Base.Widget.Material3.Snackbar">
        <item name="shapeAppearance">@style/ShapeAppearance.Material3.Corner.None</item>
    </style>
    <style name="Widget.Material3.Snackbar.TextView" parent="@style/Widget.MaterialComponents.Snackbar.TextView">
        <item name="android:textAppearance">?attr/textAppearanceBodyMedium</item>
        <item name="android:textColor">?attr/colorOnSurfaceInverse</item>
        <item name="android:alpha">@dimen/m3_snackbar_action_text_color_alpha</item>
    </style>
    <style name="Widget.Material3.TabLayout" parent="@style/Base.Widget.Material3.TabLayout">
        <item name="android:background">@drawable/m3_tabs_background</item>
    </style>
    <style name="Widget.Material3.TabLayout.OnSurface" parent="@style/Base.Widget.Material3.TabLayout.OnSurface">
        <item name="android:background">@drawable/m3_tabs_transparent_background</item>
    </style>
    <style name="Widget.Material3.TabLayout.Secondary" parent="@style/Base.Widget.Material3.TabLayout.Secondary">
        <item name="android:background">@drawable/m3_tabs_background</item>
    </style>
    <style name="Widget.Material3.TextInputEditText.FilledBox" parent="@style/Widget.MaterialComponents.TextInputEditText.FilledBox">
        <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
        <item name="android:textColor">@color/m3_textfield_input_text_color</item>
    </style>
    <style name="Widget.Material3.TextInputEditText.FilledBox.Dense" parent="@style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense">
        <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
        <item name="android:textColor">@color/m3_textfield_input_text_color</item>
    </style>
    <style name="Widget.Material3.TextInputEditText.OutlinedBox" parent="@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox">
        <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
        <item name="android:textColor">@color/m3_textfield_input_text_color</item>
    </style>
    <style name="Widget.Material3.TextInputEditText.OutlinedBox.Dense" parent="@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense">
        <item name="android:textAppearance">?attr/textAppearanceBodyLarge</item>
        <item name="android:textColor">@color/m3_textfield_input_text_color</item>
    </style>
    <style name="Widget.Material3.TextInputLayout.FilledBox" parent="@style/Widget.MaterialComponents.TextInputLayout.FilledBox">
        <item name="android:textColorHint">@color/m3_textfield_label_color</item>
        <item name="boxBackgroundColor">@color/m3_textfield_filled_background_color</item>
        <item name="boxStrokeColor">@color/m3_textfield_stroke_color</item>
        <item name="boxStrokeErrorColor">?attr/colorError</item>
        <item name="counterOverflowTextAppearance">?attr/textAppearanceBodySmall</item>
        <item name="counterOverflowTextColor">?attr/colorError</item>
        <item name="counterTextAppearance">?attr/textAppearanceBodySmall</item>
        <item name="counterTextColor">@color/m3_textfield_indicator_text_color</item>
        <item name="cursorColor">@null</item>
        <item name="cursorErrorColor">?attr/colorError</item>
        <item name="endIconTint">@color/m3_textfield_indicator_text_color</item>
        <item name="errorIconTint">?attr/colorError</item>
        <item name="errorTextAppearance">?attr/textAppearanceBodySmall</item>
        <item name="errorTextColor">?attr/colorError</item>
        <item name="helperTextTextAppearance">?attr/textAppearanceBodySmall</item>
        <item name="helperTextTextColor">@color/m3_textfield_indicator_text_color</item>
        <item name="hintTextAppearance">?attr/textAppearanceBodySmall</item>
        <item name="hintTextColor">@color/m3_textfield_label_color</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.TextInputEditText.FilledBox</item>
        <item name="passwordToggleDrawable">@drawable/m3_password_eye</item>
        <item name="placeholderTextAppearance">?attr/textAppearanceBodyLarge</item>
        <item name="placeholderTextColor">@color/m3_textfield_indicator_text_color</item>
        <item name="prefixTextAppearance">?attr/textAppearanceTitleMedium</item>
        <item name="prefixTextColor">@color/m3_textfield_indicator_text_color</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerExtraSmall</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.Material3.Corner.Top</item>
        <item name="startIconTint">@color/m3_textfield_indicator_text_color</item>
        <item name="suffixTextAppearance">?attr/textAppearanceTitleMedium</item>
        <item name="suffixTextColor">@color/m3_textfield_indicator_text_color</item>
    </style>
    <style name="Widget.Material3.TextInputLayout.FilledBox.Dense" parent="@style/Widget.Material3.TextInputLayout.FilledBox">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense</item>
    </style>
    <style name="Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu" parent="@style/Widget.Material3.TextInputLayout.FilledBox.Dense">
        <item name="endIconMode">dropdown_menu</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense</item>
    </style>
    <style name="Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu" parent="@style/Widget.Material3.TextInputLayout.FilledBox">
        <item name="endIconMode">dropdown_menu</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox</item>
    </style>
    <style name="Widget.Material3.TextInputLayout.OutlinedBox" parent="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="android:textColorHint">@color/m3_textfield_label_color</item>
        <item name="boxStrokeColor">@color/m3_textfield_stroke_color</item>
        <item name="boxStrokeErrorColor">?attr/colorError</item>
        <item name="counterOverflowTextAppearance">?attr/textAppearanceBodySmall</item>
        <item name="counterOverflowTextColor">?attr/colorError</item>
        <item name="counterTextAppearance">?attr/textAppearanceBodySmall</item>
        <item name="counterTextColor">@color/m3_textfield_indicator_text_color</item>
        <item name="cursorColor">@null</item>
        <item name="cursorErrorColor">?attr/colorError</item>
        <item name="endIconTint">@color/m3_textfield_indicator_text_color</item>
        <item name="errorIconTint">?attr/colorError</item>
        <item name="errorTextAppearance">?attr/textAppearanceBodySmall</item>
        <item name="errorTextColor">?attr/colorError</item>
        <item name="helperTextTextAppearance">?attr/textAppearanceBodySmall</item>
        <item name="helperTextTextColor">@color/m3_textfield_indicator_text_color</item>
        <item name="hintTextAppearance">?attr/textAppearanceBodySmall</item>
        <item name="hintTextColor">@color/m3_textfield_label_color</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox</item>
        <item name="passwordToggleDrawable">@drawable/m3_password_eye</item>
        <item name="placeholderTextAppearance">?attr/textAppearanceBodyLarge</item>
        <item name="placeholderTextColor">@color/m3_textfield_indicator_text_color</item>
        <item name="prefixTextAppearance">?attr/textAppearanceTitleMedium</item>
        <item name="prefixTextColor">@color/m3_textfield_indicator_text_color</item>
        <item name="shapeAppearance">?attr/shapeAppearanceCornerExtraSmall</item>
        <item name="shapeAppearanceOverlay">@null</item>
        <item name="startIconTint">@color/m3_textfield_indicator_text_color</item>
        <item name="suffixTextAppearance">?attr/textAppearanceTitleMedium</item>
        <item name="suffixTextColor">@color/m3_textfield_indicator_text_color</item>
    </style>
    <style name="Widget.Material3.TextInputLayout.OutlinedBox.Dense" parent="@style/Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense</item>
    </style>
    <style name="Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu" parent="@style/Widget.Material3.TextInputLayout.OutlinedBox.Dense">
        <item name="endIconMode">dropdown_menu</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense</item>
    </style>
    <style name="Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu" parent="@style/Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="endIconMode">dropdown_menu</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox</item>
    </style>
    <style name="Widget.Material3.Toolbar" parent="@style/Widget.AppCompat.Toolbar">
        <item name="contentInsetStartWithNavigation">0dp</item>
        <item name="subtitleTextAppearance">?attr/textAppearanceTitleMedium</item>
        <item name="titleTextAppearance">?attr/textAppearanceTitleLarge</item>
    </style>
    <style name="Widget.Material3.Toolbar.OnSurface" parent="@style/Widget.Material3.Toolbar">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Toolbar.Surface</item>
        <item name="navigationIconTint">?attr/colorOnSurface</item>
        <item name="subtitleTextColor">?attr/colorOnSurfaceVariant</item>
        <item name="titleTextColor">?attr/colorOnSurface</item>
    </style>
    <style name="Widget.Material3.Toolbar.Surface" parent="@style/Widget.Material3.Toolbar.OnSurface">
        <item name="android:background">?attr/colorSurface</item>
    </style>
    <style name="Widget.Material3.Tooltip" parent="@style/Widget.MaterialComponents.Tooltip">
        <item name="android:textAppearance">?attr/textAppearanceBodySmall</item>
        <item name="android:textColor">?attr/colorOnPrimary</item>
        <item name="android:padding">4dp</item>
        <item name="android:layout_margin">2dp</item>
        <item name="android:minWidth">28dp</item>
        <item name="android:minHeight">28dp</item>
        <item name="backgroundTint">?attr/colorPrimary</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Material3.Tooltip</item>
    </style>
    <style name="Widget.MaterialComponents.ActionBar.Primary" parent="@style/Widget.AppCompat.ActionBar.Solid">
        <item name="background">?attr/colorPrimary</item>
        <item name="elevation">@dimen/design_appbar_elevation</item>
    </style>
    <style name="Widget.MaterialComponents.ActionBar.PrimarySurface" parent="@style/Widget.MaterialComponents.ActionBar.Primary">
    </style>
    <style name="Widget.MaterialComponents.ActionBar.Solid" parent="@style/Widget.AppCompat.ActionBar.Solid">
        <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="maxButtonHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="subtitleTextStyle">?attr/textAppearanceSubtitle1</item>
        <item name="titleTextStyle">?attr/textAppearanceHeadline6</item>
    </style>
    <style name="Widget.MaterialComponents.ActionBar.Surface" parent="@style/Widget.AppCompat.Light.ActionBar.Solid">
        <item name="background">?attr/colorSurface</item>
        <item name="elevation">0dp</item>
    </style>
    <style name="Widget.MaterialComponents.ActionMode" parent="@style/Widget.AppCompat.ActionMode">
        <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="maxButtonHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="subtitleTextStyle">?attr/textAppearanceSubtitle1</item>
        <item name="titleTextStyle">?attr/textAppearanceHeadline6</item>
    </style>
    <style name="Widget.MaterialComponents.AppBarLayout.Primary" parent="@style/Widget.Design.AppBarLayout">
    </style>
    <style name="Widget.MaterialComponents.AppBarLayout.PrimarySurface" parent="@style/Widget.MaterialComponents.AppBarLayout.Primary">
    </style>
    <style name="Widget.MaterialComponents.AppBarLayout.Surface" parent="@style/Widget.Design.AppBarLayout">
        <item name="android:background">?attr/colorSurface</item>
    </style>
    <style name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox" parent="@style/Base.Widget.MaterialComponents.AutoCompleteTextView">
        <item name="android:paddingTop">24dp</item>
        <item name="android:paddingBottom">10dp</item>
    </style>
    <style name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense" parent="@style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox">
        <item name="android:paddingTop">24dp</item>
        <item name="android:paddingBottom">8dp</item>
    </style>
    <style name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox" parent="@style/Base.Widget.MaterialComponents.AutoCompleteTextView">
    </style>
    <style name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense" parent="@style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox">
        <item name="android:paddingTop">13dp</item>
        <item name="android:paddingBottom">13dp</item>
    </style>
    <style name="Widget.MaterialComponents.Badge" parent="@android:style/Widget">
        <item name="autoAdjustToWithinGrandparentBounds">false</item>
        <item name="backgroundColor">?attr/colorError</item>
        <item name="badgeGravity">TOP_END</item>
        <item name="badgeHeight">@dimen/mtrl_badge_size</item>
        <item name="badgeShapeAppearance">@style/ShapeAppearance.MaterialComponents.Badge</item>
        <item name="badgeTextAppearance">@style/TextAppearance.MaterialComponents.Badge</item>
        <item name="badgeWidePadding">@dimen/mtrl_badge_long_text_horizontal_padding</item>
        <item name="badgeWidth">@dimen/mtrl_badge_size</item>
        <item name="badgeWithTextHeight">@dimen/mtrl_badge_with_text_size</item>
        <item name="badgeWithTextShapeAppearance">@style/ShapeAppearance.MaterialComponents.Badge</item>
        <item name="badgeWithTextWidth">@dimen/mtrl_badge_with_text_size</item>
        <item name="maxNumber">@integer/m3_badge_max_number</item>
        <item name="offsetAlignmentMode">legacy</item>
    </style>
    <style name="Widget.MaterialComponents.BottomAppBar" parent="@style/Widget.AppCompat.Toolbar">
        <item name="android:minHeight">@dimen/mtrl_bottomappbar_height</item>
        <item name="addElevationShadow">true</item>
        <item name="backgroundTint">?attr/colorSurface</item>
        <item name="elevation">8dp</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="fabAlignmentMode">center</item>
        <item name="fabAnchorMode">cradle</item>
        <item name="fabCradleMargin">@dimen/mtrl_bottomappbar_fab_cradle_margin</item>
        <item name="fabCradleRoundedCornerRadius">@dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius</item>
        <item name="fabCradleVerticalOffset">@dimen/mtrl_bottomappbar_fab_cradle_vertical_offset</item>
        <item name="maxButtonHeight">@dimen/mtrl_bottomappbar_height</item>
        <item name="menuAlignmentMode">auto</item>
        <item name="paddingBottomSystemWindowInsets">true</item>
        <item name="paddingLeftSystemWindowInsets">true</item>
        <item name="paddingRightSystemWindowInsets">true</item>
    </style>
    <style name="Widget.MaterialComponents.BottomAppBar.Colored" parent="@style/Widget.MaterialComponents.BottomAppBar">
        <item name="backgroundTint">?attr/colorPrimary</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary</item>
    </style>
    <style name="Widget.MaterialComponents.BottomAppBar.PrimarySurface" parent="@style/Widget.MaterialComponents.BottomAppBar.Colored">
    </style>
    <style name="Widget.MaterialComponents.BottomNavigationView" parent="@style/Widget.Design.BottomNavigationView">
        <item name="android:background">?attr/colorSurface</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="itemBackground">@null</item>
        <item name="itemHorizontalTranslationEnabled">false</item>
        <item name="itemIconTint">@color/mtrl_navigation_bar_item_tint</item>
        <item name="itemRippleColor">@color/mtrl_navigation_bar_ripple_color</item>
        <item name="itemTextAppearanceActive">?attr/textAppearanceCaption</item>
        <item name="itemTextAppearanceInactive">?attr/textAppearanceCaption</item>
        <item name="itemTextColor">@color/mtrl_navigation_bar_item_tint</item>
    </style>
    <style name="Widget.MaterialComponents.BottomNavigationView.Colored" parent="@style/Widget.MaterialComponents.BottomNavigationView">
        <item name="android:background">?attr/colorPrimary</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="itemIconTint">@color/mtrl_navigation_bar_colored_item_tint</item>
        <item name="itemRippleColor">@color/mtrl_navigation_bar_colored_ripple_color</item>
        <item name="itemTextAppearanceActive">?attr/textAppearanceCaption</item>
        <item name="itemTextAppearanceInactive">?attr/textAppearanceCaption</item>
        <item name="itemTextColor">@color/mtrl_navigation_bar_colored_item_tint</item>
    </style>
    <style name="Widget.MaterialComponents.BottomNavigationView.PrimarySurface" parent="@style/Widget.MaterialComponents.BottomNavigationView.Colored">
    </style>
    <style name="Widget.MaterialComponents.BottomSheet" parent="@style/Widget.Design.BottomSheet.Modal">
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:background">@null</item>
        <item name="android:maxWidth">@dimen/material_bottom_sheet_max_width</item>
        <item name="android:elevation">@dimen/design_bottom_sheet_elevation</item>
        <item name="backgroundTint">?attr/colorSurface</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="shapeAppearance">?attr/shapeAppearanceLargeComponent</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet</item>
    </style>
    <style name="Widget.MaterialComponents.BottomSheet.Modal" parent="@style/Widget.MaterialComponents.BottomSheet">
        <item name="android:elevation">@dimen/design_bottom_sheet_modal_elevation</item>
    </style>
    <style name="Widget.MaterialComponents.Button" parent="@style/Widget.AppCompat.Button">
        <item name="android:textAppearance">?attr/textAppearanceButton</item>
        <item name="android:textColor">@color/mtrl_btn_text_color_selector</item>
        <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
        <item name="android:paddingTop">@dimen/mtrl_btn_padding_top</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
        <item name="android:paddingBottom">@dimen/mtrl_btn_padding_bottom</item>
        <item name="android:maxWidth">@dimen/mtrl_btn_max_width</item>
        <item name="android:insetLeft">0dp</item>
        <item name="android:insetRight">0dp</item>
        <item name="android:insetTop">@dimen/mtrl_btn_inset</item>
        <item name="android:insetBottom">@dimen/mtrl_btn_inset</item>
        <item name="android:stateListAnimator">@animator/mtrl_btn_state_list_anim</item>
        <item name="backgroundTint">@color/mtrl_btn_bg_color_selector</item>
        <item name="cornerRadius">@null</item>
        <item name="elevation">@dimen/mtrl_btn_elevation</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="iconPadding">@dimen/mtrl_btn_icon_padding</item>
        <item name="iconTint">@color/mtrl_btn_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
        <item name="shapeAppearance">?attr/shapeAppearanceSmallComponent</item>
    </style>
    <style name="Widget.MaterialComponents.Button.Icon" parent="@style/Widget.MaterialComponents.Button">
        <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
    </style>
    <style name="Widget.MaterialComponents.Button.OutlinedButton" parent="@style/Widget.MaterialComponents.Button.TextButton">
        <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
        <item name="strokeColor">@color/mtrl_btn_stroke_color_selector</item>
        <item name="strokeWidth">@dimen/mtrl_btn_stroke_size</item>
    </style>
    <style name="Widget.MaterialComponents.Button.OutlinedButton.Icon" parent="@style/Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton" parent="@style/Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:textColor">@color/mtrl_text_btn_text_color_selector</item>
        <item name="android:paddingLeft">@dimen/mtrl_btn_text_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_text_btn_padding_right</item>
        <item name="backgroundTint">@color/mtrl_btn_text_btn_bg_color_selector</item>
        <item name="iconPadding">@dimen/mtrl_btn_text_btn_icon_padding</item>
        <item name="iconTint">@color/mtrl_text_btn_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_btn_text_btn_ripple_color</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog" parent="@style/Widget.MaterialComponents.Button.TextButton">
        <item name="android:ellipsize">end</item>
        <item name="android:layout_marginLeft">@dimen/mtrl_btn_text_btn_padding_left</item>
        <item name="android:minWidth">@dimen/mtrl_btn_dialog_btn_min_width</item>
        <item name="android:lines">1</item>
        <item name="android:singleLine">true</item>
        <item name="android:layout_marginStart">@dimen/mtrl_btn_text_btn_padding_left</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog.Flush" parent="@style/Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:layout_marginLeft">0dp</item>
        <item name="android:layout_marginStart">0dp</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog.Icon" parent="@style/Widget.MaterialComponents.Button.TextButton.Dialog">
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Icon" parent="@style/Widget.MaterialComponents.Button.TextButton">
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Snackbar" parent="@style/Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">?attr/colorPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.Button.UnelevatedButton" parent="@style/Widget.MaterialComponents.Button">
        <item name="android:stateListAnimator">@animator/mtrl_btn_unelevated_state_list_anim</item>
        <item name="elevation">0dp</item>
    </style>
    <style name="Widget.MaterialComponents.Button.UnelevatedButton.Icon" parent="@style/Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
    </style>
    <style name="Widget.MaterialComponents.CardView" parent="@style/CardView">
        <item name="android:stateListAnimator">@animator/mtrl_card_state_list_anim</item>
        <item name="cardBackgroundColor">?attr/colorSurface</item>
        <item name="cardCornerRadius">@null</item>
        <item name="cardElevation">@dimen/mtrl_card_elevation</item>
        <item name="cardForegroundColor">@color/mtrl_card_view_foreground</item>
        <item name="checkedIcon">@drawable/ic_mtrl_checked_circle</item>
        <item name="checkedIconMargin">@dimen/mtrl_card_checked_icon_margin</item>
        <item name="checkedIconSize">@dimen/mtrl_card_checked_icon_size</item>
        <item name="checkedIconTint">?attr/colorPrimary</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="rippleColor">@color/mtrl_card_view_ripple</item>
        <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
    </style>
    <style name="Widget.MaterialComponents.CheckedTextView" parent="@style/Base.Widget.MaterialComponents.CheckedTextView">
        <item name="android:textAppearance">?attr/textAppearanceBody1</item>
        <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Action" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="closeIconVisible">false</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Choice" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="android:textColor">@color/mtrl_choice_chip_text_color</item>
        <item name="android:checkable">true</item>
        <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_black</item>
        <item name="checkedIconVisible">false</item>
        <item name="chipBackgroundColor">@color/mtrl_choice_chip_background_color</item>
        <item name="chipIconVisible">false</item>
        <item name="closeIconVisible">false</item>
        <item name="rippleColor">@color/mtrl_choice_chip_ripple_color</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Entry" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="android:checkable">true</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Filter" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="android:checkable">true</item>
        <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_black</item>
        <item name="chipIconVisible">false</item>
        <item name="closeIconVisible">false</item>
    </style>
    <style name="Widget.MaterialComponents.ChipGroup" parent="@android:style/Widget">
        <item name="chipSpacingHorizontal">8dp</item>
        <item name="singleLine">false</item>
        <item name="singleSelection">false</item>
    </style>
    <style name="Widget.MaterialComponents.CircularProgressIndicator" parent="@style/Widget.MaterialComponents.ProgressIndicator">
        <item name="indicatorDirectionCircular">clockwise</item>
        <item name="indicatorInset">@dimen/mtrl_progress_circular_inset_medium</item>
        <item name="indicatorSize">@dimen/mtrl_progress_circular_size_medium</item>
        <item name="trackColor">@android:color/transparent</item>
        <item name="trackThickness">@dimen/mtrl_progress_circular_track_thickness_medium</item>
    </style>
    <style name="Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall" parent="@style/Widget.MaterialComponents.CircularProgressIndicator">
        <item name="indicatorInset">@dimen/mtrl_progress_circular_inset_extra_small</item>
        <item name="indicatorSize">@dimen/mtrl_progress_circular_size_extra_small</item>
        <item name="trackThickness">@dimen/mtrl_progress_circular_track_thickness_extra_small</item>
    </style>
    <style name="Widget.MaterialComponents.CircularProgressIndicator.Medium" parent="@style/Widget.MaterialComponents.CircularProgressIndicator">
    </style>
    <style name="Widget.MaterialComponents.CircularProgressIndicator.Small" parent="@style/Widget.MaterialComponents.CircularProgressIndicator">
        <item name="indicatorInset">@dimen/mtrl_progress_circular_inset_small</item>
        <item name="indicatorSize">@dimen/mtrl_progress_circular_size_small</item>
        <item name="trackThickness">@dimen/mtrl_progress_circular_track_thickness_small</item>
    </style>
    <style name="Widget.MaterialComponents.CollapsingToolbar" parent="@style/Widget.Design.CollapsingToolbar">
    </style>
    <style name="Widget.MaterialComponents.CompoundButton.CheckBox" parent="@style/Widget.AppCompat.CompoundButton.CheckBox">
        <item name="android:minWidth">?attr/minTouchTargetSize</item>
        <item name="android:minHeight">?attr/minTouchTargetSize</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="useMaterialThemeColors">true</item>
    </style>
    <style name="Widget.MaterialComponents.CompoundButton.RadioButton" parent="@style/Widget.AppCompat.CompoundButton.RadioButton">
        <item name="android:minWidth">?attr/minTouchTargetSize</item>
        <item name="android:minHeight">?attr/minTouchTargetSize</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="useMaterialThemeColors">true</item>
    </style>
    <style name="Widget.MaterialComponents.CompoundButton.Switch" parent="@style/Widget.AppCompat.CompoundButton.Switch">
        <item name="android:minWidth">?attr/minTouchTargetSize</item>
        <item name="android:minHeight">?attr/minTouchTargetSize</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="useMaterialThemeColors">true</item>
    </style>
    <style name="Widget.MaterialComponents.ExtendedFloatingActionButton" parent="@style/Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/mtrl_fab_icon_text_color_selector</item>
        <item name="android:paddingLeft">@dimen/mtrl_extended_fab_start_padding</item>
        <item name="android:paddingTop">@dimen/mtrl_extended_fab_top_padding</item>
        <item name="android:paddingRight">@dimen/mtrl_extended_fab_end_padding</item>
        <item name="android:paddingBottom">@dimen/mtrl_extended_fab_bottom_padding</item>
        <item name="android:minWidth">@dimen/mtrl_extended_fab_min_width</item>
        <item name="android:minHeight">@dimen/mtrl_extended_fab_min_height</item>
        <item name="android:maxLines">1</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:paddingStart">@dimen/mtrl_extended_fab_start_padding</item>
        <item name="android:paddingEnd">@dimen/mtrl_extended_fab_end_padding</item>
        <item name="android:stateListAnimator">@animator/mtrl_extended_fab_state_list_animator</item>
        <item name="backgroundTint">@color/mtrl_fab_bg_color_selector</item>
        <item name="collapsedSize">@dimen/design_fab_size_normal</item>
        <item name="elevation">@dimen/mtrl_extended_fab_elevation</item>
        <item name="extendStrategy">wrap_content</item>
        <item name="iconPadding">@dimen/mtrl_extended_fab_icon_text_spacing</item>
        <item name="iconSize">@dimen/mtrl_extended_fab_icon_size</item>
        <item name="iconTint">@color/mtrl_fab_icon_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_fab_ripple_color</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton</item>
    </style>
    <style name="Widget.MaterialComponents.ExtendedFloatingActionButton.Icon" parent="@style/Widget.MaterialComponents.ExtendedFloatingActionButton">
        <item name="android:gravity">start|center_vertical</item>
        <item name="android:paddingLeft">@dimen/mtrl_extended_fab_start_padding_icon</item>
        <item name="android:paddingRight">@dimen/mtrl_extended_fab_end_padding_icon</item>
        <item name="android:paddingStart">@dimen/mtrl_extended_fab_start_padding_icon</item>
        <item name="android:paddingEnd">@dimen/mtrl_extended_fab_end_padding_icon</item>
    </style>
    <style name="Widget.MaterialComponents.FloatingActionButton" parent="@style/Widget.Design.FloatingActionButton">
        <item name="android:background">@null</item>
        <item name="backgroundTint">@color/mtrl_fab_bg_color_selector</item>
        <item name="elevation">@dimen/mtrl_fab_elevation</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="ensureMinTouchTargetSize">true</item>
        <item name="hideMotionSpec">@animator/mtrl_fab_hide_motion_spec</item>
        <item name="hoveredFocusedTranslationZ">@dimen/mtrl_fab_translation_z_hovered_focused</item>
        <item name="pressedTranslationZ">@dimen/mtrl_fab_translation_z_pressed</item>
        <item name="rippleColor">@color/mtrl_fab_ripple_color</item>
        <item name="shapeAppearance">?attr/shapeAppearanceSmallComponent</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton</item>
        <item name="showMotionSpec">@animator/mtrl_fab_show_motion_spec</item>
        <item name="tint">@color/mtrl_fab_icon_text_color_selector</item>
    </style>
    <style name="Widget.MaterialComponents.Light.ActionBar.Solid" parent="@style/Widget.AppCompat.Light.ActionBar.Solid">
        <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="maxButtonHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="subtitleTextStyle">?attr/textAppearanceSubtitle1</item>
        <item name="titleTextStyle">?attr/textAppearanceHeadline6</item>
    </style>
    <style name="Widget.MaterialComponents.LinearProgressIndicator" parent="@style/Widget.MaterialComponents.ProgressIndicator">
        <item name="indeterminateAnimationType">disjoint</item>
        <item name="indicatorDirectionLinear">startToEnd</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialButtonToggleGroup" parent="@android:style/Widget">
        <item name="singleSelection">false</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar" parent="@android:style/Widget">
        <item name="android:windowFullscreen">false</item>
        <item name="backgroundTint">?attr/colorSurface</item>
        <item name="dayInvalidStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid</item>
        <item name="daySelectedStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day.Selected</item>
        <item name="dayStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day</item>
        <item name="dayTodayStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day.Today</item>
        <item name="rangeFillColor">@color/mtrl_calendar_selected_range</item>
        <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
        <item name="yearSelectedStyle">@style/Widget.MaterialComponents.MaterialCalendar.Year.Selected</item>
        <item name="yearStyle">@style/Widget.MaterialComponents.MaterialCalendar.Year</item>
        <item name="yearTodayStyle">@style/Widget.MaterialComponents.MaterialCalendar.Year.Today</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day" parent="@style/Widget.MaterialComponents.MaterialCalendar.Item">
        <item name="android:height">@dimen/mtrl_calendar_day_height</item>
        <item name="android:width">@dimen/mtrl_calendar_day_width</item>
        <item name="android:insetLeft">@dimen/mtrl_calendar_day_horizontal_padding</item>
        <item name="android:insetRight">@dimen/mtrl_calendar_day_horizontal_padding</item>
        <item name="android:insetTop">@dimen/mtrl_calendar_day_vertical_padding</item>
        <item name="android:insetBottom">@dimen/mtrl_calendar_day_vertical_padding</item>
        <item name="itemShapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day.Invalid" parent="@style/Widget.MaterialComponents.MaterialCalendar.Day">
        <item name="itemStrokeWidth">0dp</item>
        <item name="itemTextColor">@color/material_on_surface_disabled</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day.Selected" parent="@style/Widget.MaterialComponents.MaterialCalendar.Day">
        <item name="itemFillColor">?attr/colorPrimary</item>
        <item name="itemStrokeWidth">0dp</item>
        <item name="itemTextColor">?attr/colorOnPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day.Today" parent="@style/Widget.MaterialComponents.MaterialCalendar.Day">
        <item name="itemStrokeColor">@color/material_on_surface_emphasis_high_type</item>
        <item name="itemStrokeWidth">@dimen/mtrl_calendar_day_today_stroke</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel" parent="">
        <item name="android:textAppearance">?attr/textAppearanceBody2</item>
        <item name="android:gravity">center</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.DayTextView" parent="@style/Widget.AppCompat.TextView">
        <item name="android:textAppearance">?attr/textAppearanceCaption</item>
        <item name="android:gravity">center</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Fullscreen" parent="@style/Widget.MaterialComponents.MaterialCalendar">
        <item name="android:windowFullscreen">true</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton" parent="@style/Widget.MaterialComponents.Button.TextButton">
        <item name="iconTint">?attr/colorOnPrimary</item>
        <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton" parent="@style/Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">@color/mtrl_on_primary_text_btn_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderDivider" parent="@android:style/Widget">
        <item name="android:background">?attr/colorOnPrimary</item>
        <item name="android:visibility">gone</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderLayout" parent="@android:style/Widget">
        <item name="android:background">?attr/colorPrimary</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/mtrl_calendar_header_height</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen" parent="@style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout">
        <item name="android:layout_height">@dimen/mtrl_calendar_header_height_fullscreen</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection" parent="@style/Widget.AppCompat.TextView">
        <item name="android:textAppearance">?attr/textAppearanceHeadline4</item>
        <item name="android:textColor">?attr/colorOnPrimary</item>
        <item name="android:ellipsize">end</item>
        <item name="android:layout_gravity">start|top</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:maxLines">@integer/mtrl_calendar_selection_text_lines</item>
        <item name="autoSizeMaxTextSize">34sp</item>
        <item name="autoSizeMinTextSize">2sp</item>
        <item name="autoSizeTextType">uniform</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen" parent="@style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection">
        <item name="android:textAppearance">?attr/textAppearanceHeadline6</item>
        <item name="android:maxLines">1</item>
        <item name="autoSizeMaxTextSize">20sp</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderTitle" parent="@style/Widget.AppCompat.TextView">
        <item name="android:textAppearance">?attr/textAppearanceOverline</item>
        <item name="android:textColor">?attr/colorOnPrimary</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">1</item>
        <item name="autoSizeMaxTextSize">10sp</item>
        <item name="autoSizeMinTextSize">2sp</item>
        <item name="autoSizeTextType">uniform</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton" parent="@style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton">
        <item name="android:layout_gravity">end|bottom</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Item" parent="">
        <item name="itemFillColor">@android:color/transparent</item>
        <item name="itemShapeAppearance">?attr/shapeAppearanceSmallComponent</item>
        <item name="itemStrokeColor">@color/mtrl_calendar_item_stroke_color</item>
        <item name="itemStrokeWidth">1dp</item>
        <item name="itemTextColor">@color/material_on_surface_emphasis_high_type</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton" parent="@style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton">
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.MonthTextView" parent="@style/Widget.AppCompat.TextView">
        <item name="android:textAppearance">?attr/textAppearanceSubtitle2</item>
        <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Year" parent="@style/Widget.MaterialComponents.MaterialCalendar.Item">
        <item name="android:height">@dimen/mtrl_calendar_year_height</item>
        <item name="android:width">@dimen/mtrl_calendar_year_width</item>
        <item name="android:insetLeft">@dimen/mtrl_calendar_year_horizontal_padding</item>
        <item name="android:insetRight">@dimen/mtrl_calendar_year_horizontal_padding</item>
        <item name="android:insetTop">@dimen/mtrl_calendar_year_vertical_padding</item>
        <item name="android:insetBottom">@dimen/mtrl_calendar_year_vertical_padding</item>
        <item name="itemShapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Year.Selected" parent="@style/Widget.MaterialComponents.MaterialCalendar.Year">
        <item name="itemFillColor">?attr/colorPrimary</item>
        <item name="itemStrokeColor">?attr/colorOnPrimary</item>
        <item name="itemStrokeWidth">0dp</item>
        <item name="itemTextColor">?attr/colorOnPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Year.Today" parent="@style/Widget.MaterialComponents.MaterialCalendar.Year">
        <item name="itemStrokeColor">@color/material_on_surface_emphasis_high_type</item>
        <item name="itemStrokeWidth">@dimen/mtrl_calendar_day_today_stroke</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.YearNavigationButton" parent="@style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton">
    </style>
    <style name="Widget.MaterialComponents.MaterialDivider" parent="@android:style/Widget">
        <item name="dividerColor">@color/material_divider_color</item>
        <item name="dividerThickness">@dimen/material_divider_thickness</item>
    </style>
    <style name="Widget.MaterialComponents.NavigationRailView" parent="">
        <item name="android:background">?attr/colorSurface</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:minWidth">@dimen/mtrl_navigation_rail_default_width</item>
        <item name="elevation">@dimen/mtrl_navigation_rail_elevation</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="itemActiveIndicatorStyle">@null</item>
        <item name="itemBackground">@null</item>
        <item name="itemIconSize">@dimen/mtrl_navigation_rail_icon_size</item>
        <item name="itemIconTint">@color/mtrl_navigation_bar_item_tint</item>
        <item name="itemMinHeight">@null</item>
        <item name="itemPaddingBottom">@dimen/mtrl_navigation_rail_text_bottom_margin</item>
        <item name="itemPaddingTop">@dimen/mtrl_navigation_rail_icon_margin</item>
        <item name="itemRippleColor">@color/mtrl_navigation_bar_ripple_color</item>
        <item name="itemTextAppearanceActive">?attr/textAppearanceCaption</item>
        <item name="itemTextAppearanceInactive">?attr/textAppearanceCaption</item>
        <item name="itemTextColor">@color/mtrl_navigation_bar_item_tint</item>
        <item name="labelVisibilityMode">auto</item>
        <item name="menuGravity">top</item>
    </style>
    <style name="Widget.MaterialComponents.NavigationRailView.Colored" parent="@style/Widget.MaterialComponents.NavigationRailView">
        <item name="android:background">?attr/colorPrimary</item>
        <item name="itemIconTint">@color/mtrl_navigation_bar_colored_item_tint</item>
        <item name="itemRippleColor">@color/mtrl_navigation_bar_colored_ripple_color</item>
        <item name="itemTextColor">@color/mtrl_navigation_bar_colored_item_tint</item>
    </style>
    <style name="Widget.MaterialComponents.NavigationRailView.Colored.Compact" parent="@style/Widget.MaterialComponents.NavigationRailView.Colored">
        <item name="android:minWidth">@dimen/mtrl_navigation_rail_compact_width</item>
        <item name="labelVisibilityMode">unlabeled</item>
    </style>
    <style name="Widget.MaterialComponents.NavigationRailView.Compact" parent="@style/Widget.MaterialComponents.NavigationRailView">
        <item name="android:minWidth">@dimen/mtrl_navigation_rail_compact_width</item>
        <item name="labelVisibilityMode">unlabeled</item>
    </style>
    <style name="Widget.MaterialComponents.NavigationRailView.PrimarySurface" parent="@style/Widget.MaterialComponents.NavigationRailView.Colored">
    </style>
    <style name="Widget.MaterialComponents.NavigationView" parent="@style/Widget.Design.NavigationView">
        <item name="android:background">?attr/colorSurface</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="itemBackground">@null</item>
        <item name="itemHorizontalPadding">@dimen/mtrl_navigation_item_horizontal_padding</item>
        <item name="itemIconPadding">@dimen/mtrl_navigation_item_icon_padding</item>
        <item name="itemIconSize">@dimen/mtrl_navigation_item_icon_size</item>
        <item name="itemIconTint">@color/mtrl_navigation_item_icon_tint</item>
        <item name="itemShapeAppearance">?attr/shapeAppearanceSmallComponent</item>
        <item name="itemShapeFillColor">@color/mtrl_navigation_item_background_color</item>
        <item name="itemShapeInsetBottom">@dimen/mtrl_navigation_item_shape_vertical_margin</item>
        <item name="itemShapeInsetEnd">@dimen/mtrl_navigation_item_shape_horizontal_margin</item>
        <item name="itemShapeInsetStart">@dimen/mtrl_navigation_item_shape_horizontal_margin</item>
        <item name="itemShapeInsetTop">@dimen/mtrl_navigation_item_shape_vertical_margin</item>
        <item name="itemTextAppearance">?attr/textAppearanceSubtitle2</item>
        <item name="itemTextColor">@color/mtrl_navigation_item_text_color</item>
    </style>
    <style name="Widget.MaterialComponents.PopupMenu" parent="@style/Base.Widget.MaterialComponents.PopupMenu">
        <item name="android:popupBackground">?attr/popupMenuBackground</item>
        <item name="android:popupElevation">8dp</item>
    </style>
    <style name="Widget.MaterialComponents.PopupMenu.ContextMenu" parent="@style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu">
        <item name="android:popupBackground">?attr/popupMenuBackground</item>
        <item name="android:popupElevation">8dp</item>
    </style>
    <style name="Widget.MaterialComponents.PopupMenu.ListPopupWindow" parent="@style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow">
        <item name="android:popupBackground">?attr/popupMenuBackground</item>
        <item name="android:popupElevation">8dp</item>
    </style>
    <style name="Widget.MaterialComponents.PopupMenu.Overflow" parent="@style/Base.Widget.MaterialComponents.PopupMenu.Overflow">
        <item name="android:popupBackground">?attr/popupMenuBackground</item>
        <item name="android:popupElevation">8dp</item>
    </style>
    <style name="Widget.MaterialComponents.ProgressIndicator" parent="@android:style/Widget">
        <item name="android:indeterminateOnly">false</item>
        <item name="indicatorColor">?attr/colorPrimary</item>
        <item name="trackThickness">@dimen/mtrl_progress_track_thickness</item>
    </style>
    <style name="Widget.MaterialComponents.ShapeableImageView" parent="@android:style/Widget">
        <item name="strokeColor">@color/material_on_surface_stroke</item>
    </style>
    <style name="Widget.MaterialComponents.Slider" parent="@style/Base.Widget.MaterialComponents.Slider">
        <item name="android:background">?attr/controlBackground</item>
    </style>
    <style name="Widget.MaterialComponents.Snackbar" parent="@style/Base.Widget.MaterialComponents.Snackbar">
        <item name="android:background">@null</item>
        <item name="android:layout_margin">@dimen/mtrl_snackbar_margin</item>
        <item name="animationMode">fade</item>
    </style>
    <style name="Widget.MaterialComponents.Snackbar.FullWidth" parent="@style/Base.Widget.MaterialComponents.Snackbar">
    </style>
    <style name="Widget.MaterialComponents.Snackbar.TextView" parent="@style/Widget.AppCompat.TextView">
        <item name="android:textAppearance">?attr/textAppearanceBody2</item>
        <item name="android:textColor">?attr/colorSurface</item>
        <item name="android:ellipsize">end</item>
        <item name="android:paddingTop">@dimen/design_snackbar_padding_vertical</item>
        <item name="android:paddingBottom">@dimen/design_snackbar_padding_vertical</item>
        <item name="android:layout_marginLeft">@dimen/mtrl_snackbar_message_margin_horizontal</item>
        <item name="android:layout_marginRight">@dimen/mtrl_snackbar_message_margin_horizontal</item>
        <item name="android:maxLines">@integer/design_snackbar_text_max_lines</item>
        <item name="android:alpha">@dimen/material_emphasis_high_type</item>
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="Widget.MaterialComponents.TabLayout" parent="@style/Widget.Design.TabLayout">
        <item name="android:background">?attr/colorSurface</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="tabIconTint">@color/mtrl_tabs_icon_color_selector</item>
        <item name="tabIndicatorAnimationDuration">@integer/mtrl_tab_indicator_anim_duration_ms</item>
        <item name="tabIndicatorColor">?attr/colorPrimary</item>
        <item name="tabRippleColor">@color/mtrl_tabs_ripple_color</item>
        <item name="tabTextAppearance">?attr/textAppearanceButton</item>
        <item name="tabTextColor">@color/mtrl_tabs_icon_color_selector</item>
        <item name="tabUnboundedRipple">true</item>
    </style>
    <style name="Widget.MaterialComponents.TabLayout.Colored" parent="@style/Widget.MaterialComponents.TabLayout">
        <item name="android:background">?attr/colorPrimary</item>
        <item name="tabIconTint">@color/mtrl_tabs_icon_color_selector_colored</item>
        <item name="tabIndicatorColor">?attr/colorOnPrimary</item>
        <item name="tabRippleColor">@color/mtrl_tabs_colored_ripple_color</item>
        <item name="tabTextColor">@color/mtrl_tabs_icon_color_selector_colored</item>
    </style>
    <style name="Widget.MaterialComponents.TabLayout.PrimarySurface" parent="@style/Widget.MaterialComponents.TabLayout.Colored">
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.FilledBox" parent="@style/Base.Widget.MaterialComponents.TextInputEditText">
        <item name="android:paddingTop">24dp</item>
        <item name="android:paddingBottom">10dp</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.FilledBox.Dense" parent="@style/Widget.MaterialComponents.TextInputEditText.FilledBox">
        <item name="android:paddingTop">24dp</item>
        <item name="android:paddingBottom">8dp</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.OutlinedBox" parent="@style/Base.Widget.MaterialComponents.TextInputEditText">
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense" parent="@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox">
        <item name="android:paddingTop">13dp</item>
        <item name="android:paddingBottom">13dp</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox" parent="@style/Base.Widget.MaterialComponents.TextInputLayout">
        <item name="boxBackgroundColor">@color/mtrl_filled_background_color</item>
        <item name="boxBackgroundMode">filled</item>
        <item name="boxCollapsedPaddingTop">10dp</item>
        <item name="boxStrokeColor">@color/mtrl_filled_stroke_color</item>
        <item name="endIconTint">@color/mtrl_filled_icon_tint</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="startIconTint">@color/mtrl_filled_icon_tint</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense" parent="@style/Widget.MaterialComponents.TextInputLayout.FilledBox">
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu" parent="@style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense">
        <item name="endIconMode">dropdown_menu</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu" parent="@style/Widget.MaterialComponents.TextInputLayout.FilledBox">
        <item name="endIconMode">dropdown_menu</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox" parent="@style/Base.Widget.MaterialComponents.TextInputLayout">
        <item name="boxCollapsedPaddingTop">0dp</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense" parent="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu" parent="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense">
        <item name="endIconMode">dropdown_menu</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu" parent="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="endIconMode">dropdown_menu</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox</item>
    </style>
    <style name="Widget.MaterialComponents.TextView" parent="@style/Base.Widget.MaterialComponents.TextView">
    </style>
    <style name="Widget.MaterialComponents.TimePicker" parent="">
        <item name="backgroundTint">?attr/colorSurface</item>
        <item name="clockIcon">@drawable/ic_clock_black_24dp</item>
        <item name="keyboardIcon">@drawable/ic_keyboard_black_24dp</item>
        <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
    </style>
    <style name="Widget.MaterialComponents.TimePicker.Button" parent="@style/Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textAppearance">?attr/textAppearanceSubtitle2</item>
        <item name="android:textSize">16sp</item>
        <item name="android:padding">0dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:textAlignment">center</item>
        <item name="backgroundTint">@color/material_timepicker_button_background</item>
        <item name="shapeAppearanceOverlay">?attr/shapeAppearanceMediumComponent</item>
        <item name="strokeColor">@color/material_timepicker_button_stroke</item>
        <item name="strokeWidth">@dimen/mtrl_btn_stroke_size</item>
    </style>
    <style name="Widget.MaterialComponents.TimePicker.Clock" parent="">
        <item name="clockFaceBackgroundColor">@color/material_timepicker_clockface</item>
        <item name="clockHandColor">?attr/colorPrimary</item>
        <item name="clockNumberTextColor">@color/material_timepicker_clock_text_color</item>
    </style>
    <style name="Widget.MaterialComponents.TimePicker.Display" parent="@style/Widget.MaterialComponents.Chip.Choice">
        <item name="android:textAppearance">?attr/textAppearanceHeadline3</item>
        <item name="android:textSize">56dp</item>
        <item name="android:textAlignment">center</item>
        <item name="ensureMinTouchTargetSize">false</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TimePicker.Display</item>
        <item name="shapeAppearanceOverlay">?attr/shapeAppearanceMediumComponent</item>
    </style>
    <style name="Widget.MaterialComponents.TimePicker.Display.Divider" parent="">
        <item name="android:textAppearance">?attr/textAppearanceHeadline3</item>
        <item name="android:textSize">56dp</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="android:text">@string/material_clock_display_divider</item>
        <item name="android:maxEms">1</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:lineSpacingExtra">0dp</item>
    </style>
    <style name="Widget.MaterialComponents.TimePicker.Display.HelperText" parent="@style/Widget.MaterialComponents.TextView">
        <item name="android:textAppearance">?attr/textAppearanceCaption</item>
    </style>
    <style name="Widget.MaterialComponents.TimePicker.Display.TextInputEditText" parent="@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox">
        <item name="android:textAppearance">?attr/textAppearanceHeadline3</item>
        <item name="android:textSize">56dp</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingTop">0dp</item>
        <item name="android:paddingBottom">0dp</item>
        <item name="android:minEms">2</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:maxLength">2</item>
        <item name="android:inputType">number</item>
        <item name="android:textAlignment">center</item>
        <item name="android:paddingStart">0dp</item>
        <item name="android:paddingEnd">0dp</item>
    </style>
    <style name="Widget.MaterialComponents.TimePicker.Display.TextInputLayout" parent="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText</item>
        <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
        <item name="shapeAppearanceOverlay">@null</item>
    </style>
    <style name="Widget.MaterialComponents.TimePicker.ImageButton" parent="">
        <item name="android:minWidth">?attr/minTouchTargetSize</item>
        <item name="android:minHeight">?attr/minTouchTargetSize</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="iconGravity">textStart</item>
        <item name="iconPadding">0dp</item>
        <item name="iconTint">@color/material_timepicker_modebutton_tint</item>
        <item name="rippleColor">@color/mtrl_on_surface_ripple_color</item>
        <item name="shapeAppearance">@style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance</item>
    </style>
    <style name="Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>
    <style name="Widget.MaterialComponents.Toolbar" parent="@style/Widget.AppCompat.Toolbar">
        <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="maxButtonHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="subtitleTextAppearance">?attr/textAppearanceSubtitle1</item>
        <item name="subtitleTextColor">?android:attr/textColorSecondary</item>
        <item name="titleTextAppearance">?attr/textAppearanceHeadline6</item>
        <item name="titleTextColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.Toolbar.Primary" parent="@style/Widget.MaterialComponents.Toolbar">
        <item name="android:theme">@style/ThemeOverlay.MaterialComponents.Toolbar.Primary</item>
        <item name="android:background">?attr/colorPrimary</item>
        <item name="android:elevation">@dimen/design_appbar_elevation</item>
        <item name="popupTheme">@style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary</item>
        <item name="subtitleTextColor">@color/material_on_primary_emphasis_medium</item>
        <item name="titleTextColor">?attr/colorOnPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.Toolbar.PrimarySurface" parent="@style/Widget.MaterialComponents.Toolbar.Primary">
    </style>
    <style name="Widget.MaterialComponents.Toolbar.Surface" parent="@style/Widget.MaterialComponents.Toolbar">
        <item name="android:theme">@style/ThemeOverlay.MaterialComponents.Toolbar.Surface</item>
        <item name="android:background">?attr/colorSurface</item>
        <item name="subtitleTextColor">@color/material_on_surface_emphasis_medium</item>
        <item name="titleTextColor">@color/material_on_surface_emphasis_high_type</item>
    </style>
    <style name="Widget.MaterialComponents.Tooltip" parent="@android:style/Widget">
        <item name="android:textAppearance">@style/TextAppearance.MaterialComponents.Tooltip</item>
        <item name="android:padding">@dimen/mtrl_tooltip_padding</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:minWidth">@dimen/mtrl_tooltip_minWidth</item>
        <item name="android:minHeight">@dimen/mtrl_tooltip_minHeight</item>
        <item name="shapeAppearance">@style/ShapeAppearance.MaterialComponents.Tooltip</item>
    </style>
    <style name="Widget.Support.CoordinatorLayout" parent="@android:style/Widget">
        <item name="statusBarBackground">#000000</item>
    </style>
    <style name="ucrop_ImageViewWidgetIcon" parent="">
        <item name="android:layout_gravity">center</item>
        <item name="android:duplicateParentState">true</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:scaleType">fitCenter</item>
    </style>
    <style name="ucrop_TextViewCropAspectRatio" parent="">
        <item name="android:textColor">@color/ucrop_scale_text_view_selector</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:duplicateParentState">true</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/ucrop_height_crop_aspect_ratio_text</item>
    </style>
    <style name="ucrop_TextViewWidget" parent="">
        <item name="android:textSize">@dimen/ucrop_text_size_controls_text</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/ucrop_color_widget_active</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">@dimen/ucrop_margin_top_controls_text</item>
        <item name="android:layout_centerHorizontal">true</item>
    </style>
    <style name="ucrop_TextViewWidgetText" parent="">
        <item name="android:textSize">@dimen/ucrop_text_size_widget_text</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/ucrop_color_widget_text</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">@dimen/ucrop_margin_top_widget_text</item>
        <item name="android:layout_centerHorizontal">true</item>
    </style>
    <style name="ucrop_WrapperIconState" parent="">
        <item name="android:gravity">center</item>
        <item name="android:orientation">vertical</item>
        <item name="android:clickable">true</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_weight">1</item>
        <item name="background">?attr/selectableItemBackground</item>
    </style>
    <style name="ucrop_WrapperRotateButton" parent="">
        <item name="android:clickable">true</item>
        <item name="android:layout_width">@dimen/ucrop_size_wrapper_rotate_button</item>
        <item name="android:layout_height">@dimen/ucrop_size_wrapper_rotate_button</item>
        <item name="background">?attr/selectableItemBackgroundBorderless</item>
    </style>
</resources>
