package com.example.ardrawingsketch.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ardrawingsketch.R;

/* loaded from: classes3.dex */
public final class ActivityGuideBinding implements ViewBinding {
    public final AppCompatImageView btnBack;
    public final TextView btnContinue;
    public final CardView helpVideoCard;
    public final AppCompatImageView img;
    public final TextView labelHelp;
    public final ConstraintLayout main;
    private final ConstraintLayout rootView;
    public final TextView textMessageHelpe;
    public final TextView textTitleHelpe;
    public final ConstraintLayout toolbar;

    private ActivityGuideBinding(ConstraintLayout rootView, AppCompatImageView btnBack, TextView btnContinue, CardView helpVideoCard, AppCompatImageView img, TextView labelHelp, ConstraintLayout main, TextView textMessageHelpe, TextView textTitleHelpe, ConstraintLayout toolbar) {
        this.rootView = rootView;
        this.btnBack = btnBack;
        this.btnContinue = btnContinue;
        this.helpVideoCard = helpVideoCard;
        this.img = img;
        this.labelHelp = labelHelp;
        this.main = main;
        this.textMessageHelpe = textMessageHelpe;
        this.textTitleHelpe = textTitleHelpe;
        this.toolbar = toolbar;
    }

    @Override // androidx.viewbinding.ViewBinding
    public ConstraintLayout getRoot() {
        return this.rootView;
    }

    public static ActivityGuideBinding inflate(LayoutInflater inflater) {
        return inflate(inflater, null, false);
    }

    public static ActivityGuideBinding inflate(LayoutInflater inflater, ViewGroup parent, boolean attachToParent) {
        View root = inflater.inflate(R.layout.activity_guide, parent, false);
        if (attachToParent) {
            parent.addView(root);
        }
        return bind(root);
    }

    public static ActivityGuideBinding bind(View rootView) {
        int id = R.id.btn_back;
        AppCompatImageView btnBack = (AppCompatImageView) ViewBindings.findChildViewById(rootView, id);
        if (btnBack != null) {
            id = R.id.btn_continue;
            TextView btnContinue = (TextView) ViewBindings.findChildViewById(rootView, id);
            if (btnContinue != null) {
                id = R.id.help_video_card;
                CardView helpVideoCard = (CardView) ViewBindings.findChildViewById(rootView, id);
                if (helpVideoCard != null) {
                    id = R.id.img;
                    AppCompatImageView img = (AppCompatImageView) ViewBindings.findChildViewById(rootView, id);
                    if (img != null) {
                        id = R.id.label_help;
                        TextView labelHelp = (TextView) ViewBindings.findChildViewById(rootView, id);
                        if (labelHelp != null) {
                            ConstraintLayout main = (ConstraintLayout) rootView;
                            id = R.id.text_message_helpe;
                            TextView textMessageHelpe = (TextView) ViewBindings.findChildViewById(rootView, id);
                            if (textMessageHelpe != null) {
                                id = R.id.text_title_helpe;
                                TextView textTitleHelpe = (TextView) ViewBindings.findChildViewById(rootView, id);
                                if (textTitleHelpe != null) {
                                    id = R.id.toolbar;
                                    ConstraintLayout toolbar = (ConstraintLayout) ViewBindings.findChildViewById(rootView, id);
                                    if (toolbar != null) {
                                        return new ActivityGuideBinding((ConstraintLayout) rootView, btnBack, btnContinue, helpVideoCard, img, labelHelp, main, textMessageHelpe, textTitleHelpe, toolbar);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        String missingId = rootView.getResources().getResourceName(id);
        throw new NullPointerException("Missing required view with ID: ".concat(missingId));
    }
}
