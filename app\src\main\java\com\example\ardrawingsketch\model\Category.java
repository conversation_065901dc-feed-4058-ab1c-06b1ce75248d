package com.example.ardrawingsketch.model;

import java.io.Serializable;

/* loaded from: classes5.dex */
public class Category implements Serializable {
    int id;
    int imageResource;
    int nameCategory;

    public Category(int nameCategory, int imageResource, int idCategory) {
        this.imageResource = imageResource;
        this.nameCategory = nameCategory;
        this.id = idCategory;
    }

    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getImageResource() {
        return this.imageResource;
    }

    public void setImageResource(int imageResource) {
        this.imageResource = imageResource;
    }

    public int getNameCategory() {
        return this.nameCategory;
    }

    public void setNameCategory(int nameCategory) {
        this.nameCategory = nameCategory;
    }
}
