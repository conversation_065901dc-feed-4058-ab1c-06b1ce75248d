<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:id="@+id/main" android:background="@color/white" android:layout_width="match_parent" android:layout_height="match_parent">
    <androidx.appcompat.widget.Toolbar android:id="@+id/toolbar" android:layout_width="match_parent" android:layout_height="?attr/actionBarSize" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0" app:title="@string/app_name" app:titleTextAppearance="@style/TitleCollapseAppBar"/>
    <androidx.cardview.widget.CardView android:id="@+id/ads_container" android:layout_width="match_parent" android:layout_height="0dp" android:layout_margin="16dp" app:cardBackgroundColor="#f2f2f2" app:cardCornerRadius="8dp" app:cardElevation="2dp" app:layout_constraintBottom_toTopOf="@+id/btn_draw" app:layout_constraintTop_toBottomOf="@+id/toolbar">
        <androidx.appcompat.widget.AppCompatImageView android:layout_width="match_parent" android:layout_height="match_parent" android:src="@drawable/main_back" android:scaleType="centerCrop"/>
    </androidx.cardview.widget.CardView>
    <androidx.cardview.widget.CardView android:id="@+id/btn_draw" android:layout_width="0dp" android:layout_height="120dp" android:layout_marginBottom="16dp" app:cardCornerRadius="8dp" app:cardElevation="4dp" app:layout_constraintBottom_toTopOf="@+id/btn_gallery" app:layout_constraintEnd_toEndOf="@+id/btn_gallery" app:layout_constraintStart_toStartOf="@+id/btn_camera">
        <LinearLayout android:gravity="center" android:orientation="horizontal" android:background="@drawable/bg_btn_positive" android:layout_width="match_parent" android:layout_height="match_parent">
            <androidx.appcompat.widget.AppCompatImageView android:padding="7dp" android:layout_width="48dp" android:layout_height="48dp" android:src="@drawable/ic_draw" android:tint="@color/white"/>
            <TextView android:textSize="14sp" android:textColor="@color/white" android:gravity="center" android:paddingLeft="8dp" android:paddingRight="8dp" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4dp" android:text="@string/str_art_draw" android:fontFamily="@font/poppins_bold"/>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
    <androidx.cardview.widget.CardView android:orientation="vertical" android:id="@+id/btn_camera" android:layout_width="0dp" android:layout_height="120dp" android:layout_marginLeft="16dp" android:layout_marginRight="16dp" android:layout_marginBottom="16dp" android:foreground="?attr/selectableItemBackground" app:cardCornerRadius="8dp" app:cardElevation="4dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintHorizontal_weight="5" app:layout_constraintLeft_toLeftOf="0" app:layout_constraintRight_toLeftOf="@+id/btn_gallery">
        <LinearLayout android:gravity="center" android:orientation="vertical" android:background="@drawable/bg_button_slideshow" android:layout_width="match_parent" android:layout_height="match_parent">
            <androidx.appcompat.widget.AppCompatImageView android:padding="7dp" android:layout_width="48dp" android:layout_height="48dp" android:src="@drawable/ic_camera" android:tint="@color/white"/>
            <TextView android:textSize="14sp" android:textColor="@color/white" android:gravity="center" android:paddingLeft="8dp" android:paddingRight="8dp" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4dp" android:text="@string/str_camera" android:fontFamily="@font/poppins_bold"/>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
    <androidx.cardview.widget.CardView android:orientation="vertical" android:id="@+id/btn_gallery" android:layout_width="0dp" android:layout_height="120dp" android:layout_marginBottom="16dp" android:foreground="?attr/selectableItemBackground" android:layout_marginEnd="16dp" app:cardCornerRadius="8dp" app:cardElevation="4dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintHorizontal_weight="3" app:layout_constraintLeft_toRightOf="@+id/btn_camera" app:layout_constraintRight_toRightOf="0">
        <LinearLayout android:gravity="center" android:orientation="vertical" android:background="@drawable/bg_button_mystudio" android:layout_width="match_parent" android:layout_height="match_parent">
            <androidx.appcompat.widget.AppCompatImageView android:padding="7dp" android:layout_width="48dp" android:layout_height="48dp" android:src="@drawable/home_ic_studio" android:tint="@color/white"/>
            <TextView android:textSize="14sp" android:textColor="@color/white" android:gravity="center" android:paddingLeft="8dp" android:paddingRight="8dp" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4dp" android:text="@string/str_gallery" android:fontFamily="@font/poppins_bold"/>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>
