<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:id="@+id/main" android:background="@color/white" android:layout_width="match_parent" android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/camera_layout" android:layout_width="match_parent" android:layout_height="match_parent" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0">
        <com.otaliastudios.cameraview.CameraView android:id="@+id/camera_view" android:layout_width="0dp" android:layout_height="0dp" android:adjustViewBounds="true" android:keepScreenOn="true" app:cameraPictureSizeMaxWidth="2000" app:cameraSnapshotMaxWidth="1280" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
        <com.example.ardrawingsketch.views.TouchImageView android:id="@+id/objImage" android:layout_width="0dp" android:layout_height="0dp" android:src="@mipmap/ic_launcher" android:scaleType="matrix" android:alpha="50" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
        <com.airbnb.lottie.LottieAnimationView android:id="@+id/animation_view" android:visibility="invisible" android:layout_width="250dp" android:layout_height="250dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0" app:lottie_autoPlay="true" app:lottie_loop="true" app:lottie_rawRes="@raw/lottiehandstrech"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.appcompat.widget.AppCompatImageView android:id="@+id/rel_flash" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="5dp" android:layout_marginTop="10dp" android:layout_marginRight="5dp" android:src="@drawable/flash_on" android:layout_marginHorizontal="5dp" app:layout_constraintEnd_toStartOf="@+id/rel_lock" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toBottomOf="@+id/action_bar_camera"/>
    <androidx.appcompat.widget.AppCompatImageView android:id="@+id/rel_lock" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="5dp" android:layout_marginTop="10dp" android:layout_marginRight="5dp" android:src="@drawable/lock" android:layout_marginHorizontal="5dp" app:layout_constraintEnd_toStartOf="@+id/rel_edit_round" app:layout_constraintStart_toEndOf="@+id/rel_flash" app:layout_constraintTop_toBottomOf="@+id/action_bar_camera"/>
    <androidx.appcompat.widget.AppCompatImageView android:id="@+id/rel_edit_round" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="5dp" android:layout_marginTop="10dp" android:layout_marginRight="5dp" android:src="@drawable/sketch_off" android:layout_marginHorizontal="5dp" app:layout_constraintEnd_toStartOf="@+id/rel_flip" app:layout_constraintStart_toEndOf="@+id/rel_lock" app:layout_constraintTop_toBottomOf="@+id/action_bar_camera"/>
    <androidx.appcompat.widget.AppCompatImageView android:id="@+id/rel_flip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="5dp" android:layout_marginTop="10dp" android:layout_marginRight="5dp" android:src="@drawable/effect_flip_camera" android:layout_marginHorizontal="5dp" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toEndOf="@+id/rel_edit_round" app:layout_constraintTop_toBottomOf="@+id/action_bar_camera"/>
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/bottom_layer" android:background="@drawable/opacity_bg" android:paddingTop="10dp" android:paddingBottom="10dp" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginLeft="16dp" android:layout_marginRight="16dp" android:layout_marginBottom="24dp" android:layout_marginHorizontal="16dp" android:paddingVertical="10dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0">
        <androidx.appcompat.widget.AppCompatImageView android:id="@+id/opacity_zero" android:layout_width="24dp" android:layout_height="wrap_content" android:src="@drawable/opacity_0" android:layout_marginStart="10dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
        <TextView android:textSize="14sp" android:textStyle="bold" android:textColor="@color/app_color" android:id="@+id/opacity_text" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="50%" android:fontFamily="sans-serif-condensed-medium" app:layout_constraintBottom_toTopOf="@+id/alpha_seek" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
        <SeekBar android:id="@+id/alpha_seek" android:layout_width="0dp" android:layout_height="wrap_content" android:maxHeight="4dp" android:max="10" android:progress="5" android:progressDrawable="@drawable/progressdrawable" android:thumb="@drawable/effect_opacity_thumb" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toStartOf="@+id/opacity_hundred" app:layout_constraintStart_toEndOf="@+id/opacity_zero" app:layout_constraintTop_toBottomOf="@+id/opacity_text"/>
        <androidx.appcompat.widget.AppCompatImageView android:id="@+id/opacity_hundred" android:layout_width="24dp" android:layout_height="wrap_content" android:src="@drawable/opacity_100" android:layout_marginEnd="10dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintTop_toTopOf="0"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/action_bar_camera" android:background="@drawable/camera_header_bg" android:layout_width="match_parent" android:layout_height="?attr/actionBarSize" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0">
        <androidx.appcompat.widget.AppCompatImageView android:gravity="center" android:id="@+id/back_camera" android:padding="8dp" android:layout_width="50dp" android:layout_height="50dp" android:src="@drawable/ic_back" android:layout_marginStart="5dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
        <TextView android:textSize="14sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:id="@+id/label_camera" android:layout_width="0dp" android:layout_height="match_parent" android:text="@string/ar_draw_sketch" android:maxLines="1" android:maxEms="12" android:fontFamily="sans-serif-condensed-medium" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
        <androidx.appcompat.widget.AppCompatImageView android:id="@+id/rel_gallery" android:visibility="gone" android:layout_width="50dp" android:layout_height="50dp" android:src="@drawable/effect_gallery_cg" android:layout_marginEnd="8dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toStartOf="@+id/rel_camera" app:layout_constraintTop_toTopOf="0"/>
        <androidx.appcompat.widget.AppCompatImageView android:id="@+id/rel_camera" android:layout_width="50dp" android:layout_height="50dp" android:src="@drawable/effect_camera_cg" android:layout_marginEnd="16dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintTop_toTopOf="0"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
