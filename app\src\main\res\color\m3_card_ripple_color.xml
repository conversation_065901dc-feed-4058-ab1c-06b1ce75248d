<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_checked="true" android:state_pressed="true" android:color="?attr/colorOnSurfaceVariant" android:alpha="@dimen/m3_comp_filled_card_pressed_state_layer_opacity"/>
    <item android:state_focused="true" android:state_checked="true" android:color="?attr/colorSecondary" android:alpha="@dimen/m3_comp_filled_card_focus_state_layer_opacity"/>
    <item android:state_checked="true" android:color="?attr/colorSecondary" android:alpha="@dimen/m3_comp_filled_card_hover_state_layer_opacity" android:state_hovered="true"/>
    <item android:state_checked="true" android:color="?attr/colorSecondary" android:alpha="@dimen/m3_ripple_default_alpha"/>
    <item android:state_checkable="true" android:state_pressed="true" android:color="?attr/colorSecondary" android:alpha="@dimen/m3_comp_filled_card_pressed_state_layer_opacity"/>
    <item android:state_checkable="false" android:state_pressed="true" android:color="?attr/colorOnSurfaceVariant" android:alpha="@dimen/m3_comp_filled_card_pressed_state_layer_opacity"/>
    <item android:state_focused="true" android:color="?attr/colorOnSurfaceVariant" android:alpha="@dimen/m3_comp_filled_card_focus_state_layer_opacity"/>
    <item android:color="?attr/colorOnSurfaceVariant" android:alpha="@dimen/m3_comp_filled_card_hover_state_layer_opacity" android:state_hovered="true"/>
    <item android:color="?attr/colorOnSurfaceVariant" android:alpha="@dimen/m3_comp_filled_card_dragged_state_layer_opacity"/>
</selector>
