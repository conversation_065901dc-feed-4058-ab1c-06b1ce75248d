<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:id="@+id/main" android:background="@color/appmaincolorm" android:layout_width="match_parent" android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/action_bar_main" android:background="@color/appmaincolorm" android:layout_width="match_parent" android:layout_height="?attr/actionBarSize" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0">
        <androidx.appcompat.widget.AppCompatImageView android:id="@+id/btn_back" android:padding="12dp" android:layout_width="50dp" android:layout_height="50dp" android:src="@drawable/ic_back" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
        <TextView android:textSize="16sp" android:textStyle="bold" android:textColor="@color/white" android:id="@+id/label_main" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/str_ar_draw_to_easy" android:fontFamily="@font/poppins_bold" android:layout_marginStart="10dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/bottom_bar_main" android:background="#e1f0fb" android:layout_width="match_parent" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toBottomOf="@+id/action_bar_main">
        <TextView android:textSize="12sp" android:textStyle="bold" android:textColor="@color/app_color" android:ellipsize="end" android:gravity="center" android:id="@+id/select_from_camera" android:paddingTop="20dp" android:paddingBottom="20dp" android:layout_width="0dp" android:layout_height="match_parent" android:text="@string/str_camera" android:maxLines="1" android:maxEms="5" android:drawableTint="@color/black" app:drawableTopCompat="@drawable/ic_camera" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toEndOf="@+id/divider" app:layout_constraintTop_toTopOf="0"/>
        <androidx.appcompat.widget.AppCompatImageView android:id="@+id/divider" android:layout_width="2dp" android:layout_height="match_parent" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toStartOf="@+id/select_from_gallery" app:layout_constraintStart_toEndOf="@+id/select_from_camera" app:layout_constraintTop_toTopOf="0"/>
        <TextView android:textSize="12sp" android:textStyle="bold" android:textColor="@color/app_color" android:ellipsize="end" android:gravity="center" android:id="@+id/select_from_gallery" android:paddingTop="20dp" android:paddingBottom="20dp" android:layout_width="0dp" android:layout_height="match_parent" android:text="@string/str_gallery" android:maxLines="1" android:maxEms="5" app:drawableTint="@color/black" app:drawableTopCompat="@drawable/ic_add_photo" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toStartOf="@+id/divider" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.recyclerview.widget.RecyclerView android:id="@+id/sketch_list_rv" android:background="@color/white" android:layout_width="match_parent" android:layout_height="0dp" android:layout_marginBottom="3dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toBottomOf="@+id/bottom_bar_main" app:spanCount="2"/>
</androidx.constraintlayout.widget.ConstraintLayout>
