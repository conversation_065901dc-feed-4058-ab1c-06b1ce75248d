package com.example.ardrawingsketch.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ardrawingsketch.R;

/* loaded from: classes3.dex */
public final class ActivityMainBinding implements ViewBinding {
    public final CardView adsContainer;
    public final CardView btnCamera;
    public final CardView btnDraw;
    public final CardView btnGallery;
    public final ConstraintLayout main;
    private final ConstraintLayout rootView;
    public final Toolbar toolbar;

    private ActivityMainBinding(ConstraintLayout rootView, CardView adsContainer, CardView btnCamera, CardView btnDraw, CardView btnGallery, ConstraintLayout main, Toolbar toolbar) {
        this.rootView = rootView;
        this.adsContainer = adsContainer;
        this.btnCamera = btnCamera;
        this.btnDraw = btnDraw;
        this.btnGallery = btnGallery;
        this.main = main;
        this.toolbar = toolbar;
    }

    @Override // androidx.viewbinding.ViewBinding
    public ConstraintLayout getRoot() {
        return this.rootView;
    }

    public static ActivityMainBinding inflate(LayoutInflater inflater) {
        return inflate(inflater, null, false);
    }

    public static ActivityMainBinding inflate(LayoutInflater inflater, ViewGroup parent, boolean attachToParent) {
        View root = inflater.inflate(R.layout.activity_main, parent, false);
        if (attachToParent) {
            parent.addView(root);
        }
        return bind(root);
    }

    public static ActivityMainBinding bind(View rootView) {
        int id = R.id.ads_container;
        CardView adsContainer = (CardView) ViewBindings.findChildViewById(rootView, id);
        if (adsContainer != null) {
            id = R.id.btn_camera;
            CardView btnCamera = (CardView) ViewBindings.findChildViewById(rootView, id);
            if (btnCamera != null) {
                id = R.id.btn_draw;
                CardView btnDraw = (CardView) ViewBindings.findChildViewById(rootView, id);
                if (btnDraw != null) {
                    id = R.id.btn_gallery;
                    CardView btnGallery = (CardView) ViewBindings.findChildViewById(rootView, id);
                    if (btnGallery != null) {
                        ConstraintLayout main = (ConstraintLayout) rootView;
                        id = R.id.toolbar;
                        Toolbar toolbar = (Toolbar) ViewBindings.findChildViewById(rootView, id);
                        if (toolbar != null) {
                            return new ActivityMainBinding((ConstraintLayout) rootView, adsContainer, btnCamera, btnDraw, btnGallery, main, toolbar);
                        }
                    }
                }
            }
        }
        String missingId = rootView.getResources().getResourceName(id);
        throw new NullPointerException("Missing required view with ID: ".concat(missingId));
    }
}
