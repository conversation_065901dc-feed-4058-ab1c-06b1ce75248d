package com.example.ardrawingsketch.imagepicker.provider;

import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.imagepicker.ImagePicker;
import com.example.ardrawingsketch.imagepicker.ImagePickerActivity;
import com.example.ardrawingsketch.imagepicker.util.FileUtil;
import com.yalantis.ucrop.UCrop;
import java.io.File;
import java.io.IOException;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;


public final class CropProvider extends BaseProvider {
    private static final String STATE_CROP_FILE = "state.crop_file";
    private final boolean mCrop;
    private final float mCropAspectX;
    private final float mCropAspectY;
    private File mCropImageFile;
    private final File mFileDir;
    private final int mMaxHeight;
    private final int mMaxWidth;
    public static final Companion Companion = new Companion(null);
    private static final String TAG = CropProvider.class.getSimpleName();

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public CropProvider(ImagePickerActivity activity) {
        super(activity);
        Intrinsics.checkNotNullParameter(activity, "activity");
        Bundle bundle = activity.getIntent().getExtras();
        bundle = bundle == null ? new Bundle() : bundle;
        this.mMaxWidth = bundle.getInt(ImagePicker.EXTRA_MAX_WIDTH, 0);
        this.mMaxHeight = bundle.getInt(ImagePicker.EXTRA_MAX_HEIGHT, 0);
        this.mCrop = bundle.getBoolean(ImagePicker.EXTRA_CROP, false);
        this.mCropAspectX = bundle.getFloat(ImagePicker.EXTRA_CROP_X, 0.0f);
        this.mCropAspectY = bundle.getFloat(ImagePicker.EXTRA_CROP_Y, 0.0f);
        String fileDir = bundle.getString(ImagePicker.EXTRA_SAVE_DIRECTORY);
        this.mFileDir = getFileDir(fileDir);
    }


    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        private Companion() {
        }
    }

    @Override // com.example.ardrawingsketch.imagepicker.provider.BaseProvider
    public void onSaveInstanceState(Bundle outState) {
        Intrinsics.checkNotNullParameter(outState, "outState");
        outState.putSerializable(STATE_CROP_FILE, this.mCropImageFile);
    }

    @Override // com.example.ardrawingsketch.imagepicker.provider.BaseProvider
    public void onRestoreInstanceState(Bundle savedInstanceState) {
        this.mCropImageFile = (File) (savedInstanceState != null ? savedInstanceState.getSerializable(STATE_CROP_FILE) : null);
    }

    public final boolean isCropEnabled() {
        return this.mCrop;
    }

    public final void startIntent(Uri uri) {
        Intrinsics.checkNotNullParameter(uri, "uri");
        try {
            cropImage(uri);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private final void cropImage(Uri uri) throws IOException {
        String extension = FileUtil.INSTANCE.getImageExtension(uri);
        this.mCropImageFile = FileUtil.INSTANCE.getImageFile(this.mFileDir, extension);
        if (this.mCropImageFile != null) {
            File file = this.mCropImageFile;
            Intrinsics.checkNotNull(file);
            if (file.exists()) {
                UCrop.Options options = new UCrop.Options();
                options.setCompressionFormat(FileUtil.INSTANCE.getCompressFormat(extension));
                UCrop uCrop = UCrop.of(uri, Uri.fromFile(this.mCropImageFile)).withOptions(options);
                if (this.mCropAspectX > 0.0f && this.mCropAspectY > 0.0f) {
                    uCrop.withAspectRatio(this.mCropAspectX, this.mCropAspectY);
                }
                if (this.mMaxWidth > 0 && this.mMaxHeight > 0) {
                    uCrop.withMaxResultSize(this.mMaxWidth, this.mMaxHeight);
                }
                try {
                    uCrop.start(getActivity(), 69);
                    return;
                } catch (ActivityNotFoundException ex) {
                    setError("uCrop not specified in manifest file.Add UCropActivity in Manifest<activity\n    android:name=\"com.yalantis.ucrop.UCropActivity\"\n    android:screenOrientation=\"portrait\"\n    android:theme=\"@style/Theme.AppCompat.Light.NoActionBar\"/>");
                    ex.printStackTrace();
                    return;
                }
            }
        }
        Log.e(TAG, "Failed to create crop image file");
        setError(R.string.error_failed_to_crop_image);
    }

    public final void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == 69) {
            if (resultCode == -1) {
                handleResult(this.mCropImageFile);
            } else {
                setResultCancel();
            }
        }
    }

    private final void handleResult(File file) {
        if (file != null) {
            ImagePickerActivity activity = getActivity();
            Uri fromFile = Uri.fromFile(file);
            Intrinsics.checkNotNullExpressionValue(fromFile, "fromFile(...)");
            activity.setCropImage(fromFile);
            return;
        }
        setError(R.string.error_failed_to_crop_image);
    }

    @Override // com.example.ardrawingsketch.imagepicker.provider.BaseProvider
    protected void onFailure() {
        delete();
    }

    public final void delete() {
        File file = this.mCropImageFile;
        if (file != null) {
            file.delete();
        }
        this.mCropImageFile = null;
    }
}
