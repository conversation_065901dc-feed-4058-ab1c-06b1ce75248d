package com.example.ardrawingsketch.imagepicker.provider;

import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.imagepicker.ImagePicker;
import com.example.ardrawingsketch.imagepicker.ImagePickerActivity;
import com.example.ardrawingsketch.imagepicker.util.FileUtil;
import com.yalantis.ucrop.UCrop;
import java.io.File;
import java.io.IOException;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: CropProvider.kt */
@Metadata(d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u0000 %2\u00020\u0001:\u0001%B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003¢\u0006\u0004\b\u0004\u0010\u0005J\u0010\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0014H\u0016J\u0012\u0010\u0015\u001a\u00020\u00122\b\u0010\u0016\u001a\u0004\u0018\u00010\u0014H\u0016J\u0006\u0010\u0017\u001a\u00020\nJ\u000e\u0010\u0018\u001a\u00020\u00122\u0006\u0010\u0019\u001a\u00020\u001aJ\u0010\u0010\u001b\u001a\u00020\u00122\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J \u0010\u001c\u001a\u00020\u00122\u0006\u0010\u001d\u001a\u00020\u00072\u0006\u0010\u001e\u001a\u00020\u00072\b\u0010\u001f\u001a\u0004\u0018\u00010 J\u0012\u0010!\u001a\u00020\u00122\b\u0010\"\u001a\u0004\u0018\u00010\u000fH\u0002J\b\u0010#\u001a\u00020\u0012H\u0014J\u0006\u0010$\u001a\u00020\u0012R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\fX\u0082\u0004¢\u0006\u0002\n\u0000R\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u000fX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u000fX\u0082\u0004¢\u0006\u0002\n\u0000¨\u0006&"}, d2 = {"Lcom/example/ardrawsketch/sketch/imagepicker/provider/CropProvider;", "Lcom/example/ardrawsketch/sketch/imagepicker/provider/BaseProvider;", "activity", "Lcom/example/ardrawsketch/sketch/imagepicker/ImagePickerActivity;", "<init>", "(Lcom/example/ardrawsketch/sketch/imagepicker/ImagePickerActivity;)V", "mMaxWidth", "", "mMaxHeight", "mCrop", "", "mCropAspectX", "", "mCropAspectY", "mCropImageFile", "Ljava/io/File;", "mFileDir", "onSaveInstanceState", "", "outState", "Landroid/os/Bundle;", "onRestoreInstanceState", "savedInstanceState", "isCropEnabled", "startIntent", "uri", "Landroid/net/Uri;", "cropImage", "onActivityResult", "requestCode", "resultCode", "data", "Landroid/content/Intent;", "handleResult", "file", "onFailure", "delete", "Companion", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes7.dex */
public final class CropProvider extends BaseProvider {
    private static final String STATE_CROP_FILE = "state.crop_file";
    private final boolean mCrop;
    private final float mCropAspectX;
    private final float mCropAspectY;
    private File mCropImageFile;
    private final File mFileDir;
    private final int mMaxHeight;
    private final int mMaxWidth;
    public static final Companion Companion = new Companion(null);
    private static final String TAG = CropProvider.class.getSimpleName();

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public CropProvider(ImagePickerActivity activity) {
        super(activity);
        Intrinsics.checkNotNullParameter(activity, "activity");
        Bundle bundle = activity.getIntent().getExtras();
        bundle = bundle == null ? new Bundle() : bundle;
        this.mMaxWidth = bundle.getInt(ImagePicker.EXTRA_MAX_WIDTH, 0);
        this.mMaxHeight = bundle.getInt(ImagePicker.EXTRA_MAX_HEIGHT, 0);
        this.mCrop = bundle.getBoolean(ImagePicker.EXTRA_CROP, false);
        this.mCropAspectX = bundle.getFloat(ImagePicker.EXTRA_CROP_X, 0.0f);
        this.mCropAspectY = bundle.getFloat(ImagePicker.EXTRA_CROP_Y, 0.0f);
        String fileDir = bundle.getString(ImagePicker.EXTRA_SAVE_DIRECTORY);
        this.mFileDir = getFileDir(fileDir);
    }

    /* compiled from: CropProvider.kt */
    @Metadata(d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003R\u0016\u0010\u0004\u001a\n \u0006*\u0004\u0018\u00010\u00050\u0005X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0005X\u0082T¢\u0006\u0002\n\u0000¨\u0006\b"}, d2 = {"Lcom/example/ardrawsketch/sketch/imagepicker/provider/CropProvider$Companion;", "", "<init>", "()V", "TAG", "", "kotlin.jvm.PlatformType", "STATE_CROP_FILE", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* loaded from: classes7.dex */
    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        private Companion() {
        }
    }

    @Override // com.example.ardrawingsketch.imagepicker.provider.BaseProvider
    public void onSaveInstanceState(Bundle outState) {
        Intrinsics.checkNotNullParameter(outState, "outState");
        outState.putSerializable(STATE_CROP_FILE, this.mCropImageFile);
    }

    @Override // com.example.ardrawingsketch.imagepicker.provider.BaseProvider
    public void onRestoreInstanceState(Bundle savedInstanceState) {
        this.mCropImageFile = (File) (savedInstanceState != null ? savedInstanceState.getSerializable(STATE_CROP_FILE) : null);
    }

    public final boolean isCropEnabled() {
        return this.mCrop;
    }

    public final void startIntent(Uri uri) {
        Intrinsics.checkNotNullParameter(uri, "uri");
        cropImage(uri);
    }

    private final void cropImage(Uri uri) throws IOException {
        String extension = FileUtil.INSTANCE.getImageExtension(uri);
        this.mCropImageFile = FileUtil.INSTANCE.getImageFile(this.mFileDir, extension);
        if (this.mCropImageFile != null) {
            File file = this.mCropImageFile;
            Intrinsics.checkNotNull(file);
            if (file.exists()) {
                UCrop.Options options = new UCrop.Options();
                options.setCompressionFormat(FileUtil.INSTANCE.getCompressFormat(extension));
                UCrop uCrop = UCrop.of(uri, Uri.fromFile(this.mCropImageFile)).withOptions(options);
                if (this.mCropAspectX > 0.0f && this.mCropAspectY > 0.0f) {
                    uCrop.withAspectRatio(this.mCropAspectX, this.mCropAspectY);
                }
                if (this.mMaxWidth > 0 && this.mMaxHeight > 0) {
                    uCrop.withMaxResultSize(this.mMaxWidth, this.mMaxHeight);
                }
                try {
                    uCrop.start(getActivity(), 69);
                    return;
                } catch (ActivityNotFoundException ex) {
                    setError("uCrop not specified in manifest file.Add UCropActivity in Manifest<activity\n    android:name=\"com.yalantis.ucrop.UCropActivity\"\n    android:screenOrientation=\"portrait\"\n    android:theme=\"@style/Theme.AppCompat.Light.NoActionBar\"/>");
                    ex.printStackTrace();
                    return;
                }
            }
        }
        Log.e(TAG, "Failed to create crop image file");
        setError(R.string.error_failed_to_crop_image);
    }

    public final void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == 69) {
            if (resultCode == -1) {
                handleResult(this.mCropImageFile);
            } else {
                setResultCancel();
            }
        }
    }

    private final void handleResult(File file) {
        if (file != null) {
            ImagePickerActivity activity = getActivity();
            Uri fromFile = Uri.fromFile(file);
            Intrinsics.checkNotNullExpressionValue(fromFile, "fromFile(...)");
            activity.setCropImage(fromFile);
            return;
        }
        setError(R.string.error_failed_to_crop_image);
    }

    @Override // com.example.ardrawingsketch.imagepicker.provider.BaseProvider
    protected void onFailure() {
        delete();
    }

    public final void delete() {
        File file = this.mCropImageFile;
        if (file != null) {
            file.delete();
        }
        this.mCropImageFile = null;
    }
}
