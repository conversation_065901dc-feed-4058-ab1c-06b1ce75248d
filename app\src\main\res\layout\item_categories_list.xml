<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:id="@+id/categories_parent" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_margin="5dp" app:cardCornerRadius="9dp">
    <androidx.appcompat.widget.AppCompatImageView android:id="@+id/img_category" android:layout_width="wrap_content" android:layout_height="200dp" android:layout_marginTop="5dp" android:layout_marginBottom="5dp" android:src="@drawable/category_cars" android:layout_centerInParent="true" android:layout_marginStart="5dp" android:layout_marginEnd="5dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
</androidx.cardview.widget.CardView>
