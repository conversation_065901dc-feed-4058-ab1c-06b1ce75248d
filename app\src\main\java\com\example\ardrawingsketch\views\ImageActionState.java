package com.example.ardrawingsketch.views;

/**
 * تعداد حالات عمل الصورة التفاعلية
 * يحدد الحالة الحالية للتفاعل مع عنصر عرض الصورة
 *
 * يُستخدم لتتبع نوع العملية الجارية على الصورة وتحسين الأداء
 * وضمان عدم تضارب العمليات المختلفة
 */
public enum ImageActionState {

    /**
     * لا يوجد عمل
     * الحالة الافتراضية عندما لا يقوم المستخدم بأي تفاعل
     * الصورة في وضع السكون وجاهزة لاستقبال أوامر جديدة
     */
    NONE,

    /**
     * سحب وتحريك
     * المستخدم يقوم بسحب الصورة لتحريكها في الشاشة
     * يحدث عند لمس وسحب الصورة بإصبع واحد
     */
    DRAG,

    /**
     * تكبير وتصغير
     * المستخدم يقوم بتكبير أو تصغير الصورة
     * يحدث عند استخدام إيماءة القرص (إصبعين) أو النقر المزدوج
     */
    ZOOM,

    /**
     * حركة سريعة بالقصور الذاتي
     * الصورة تتحرك بسرعة بعد سحبها وتركها فجأة
     * تحاكي حركة القذف الطبيعية مع تباطؤ تدريجي
     */
    FLING,

    /**
     * رسوم متحركة للتكبير
     * الصورة في عملية تكبير أو تصغير متحركة ناعمة
     * يحدث عند النقر المزدوج أو الانتقال التلقائي لمستوى تكبير محدد
     */
    ANIMATE_ZOOM
}
