package com.example.ardrawingsketch.views;

import androidx.constraintlayout.widget.ConstraintLayout;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.enums.EnumEntriesKt;


public enum ImageActionState {
    NONE,
    DRAG,
    ZOOM,
    FLING,
    ANIMATE_ZOOM;
    
    private static final /* synthetic */ EnumEntries $ENTRIES = EnumEntriesKt.enumEntries($VALUES);

    public static EnumEntries<ImageActionState> getEntries() {
        return $ENTRIES;
    }
}
