package com.example.ardrawingsketch;

import android.app.Application;
import androidx.appcompat.app.AppCompatDelegate;

/**
 * فئة التطبيق الرئيسية - تُستدعى عند بدء تشغيل التطبيق
 * تحتوي على الإعدادات العامة التي تؤثر على التطبيق بأكمله
 */
public class MyApplication extends Application {

    /**
     * دالة تُستدعى عند إنشاء التطبيق لأول مرة
     * تُستخدم لتهيئة الإعدادات العامة
     */
    @Override
    public void onCreate() {
        super.onCreate();

        // تعيين الوضع النهاري كوضع افتراضي للتطبيق
        // القيمة 1 تعني MODE_NIGHT_NO (الوضع النهاري)
        // يمكن تغييرها إلى MODE_NIGHT_YES للوضع الليلي أو MODE_NIGHT_FOLLOW_SYSTEM
        // لاتباع إعدادات النظام
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
    }
}
