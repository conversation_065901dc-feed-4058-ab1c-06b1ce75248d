<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true" android:state_pressed="true" android:color="?attr/colorOnSurface" android:alpha="@dimen/m3_comp_navigation_bar_pressed_state_layer_opacity"/>
    <item android:state_focused="true" android:state_selected="true" android:color="?attr/colorOnSurface" android:alpha="@dimen/m3_comp_navigation_bar_focus_state_layer_opacity"/>
    <item android:state_selected="true" android:color="?attr/colorOnSurface" android:alpha="@dimen/m3_comp_navigation_bar_hover_state_layer_opacity" android:state_hovered="true"/>
    <item android:state_pressed="true" android:color="?attr/colorOnSurface" android:alpha="@dimen/m3_comp_navigation_bar_pressed_state_layer_opacity"/>
    <item android:state_focused="true" android:color="?attr/colorOnSurface" android:alpha="@dimen/m3_comp_navigation_bar_focus_state_layer_opacity"/>
    <item android:color="?attr/colorOnSurface" android:alpha="@dimen/m3_comp_navigation_bar_hover_state_layer_opacity" android:state_hovered="true"/>
    <item android:color="@android:color/transparent"/>
</selector>
