package com.example.ardrawingsketch.imagepicker.util;

import android.content.Context;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: PermissionUtil.kt */
@Metadata(d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\bÆ\u0002\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J\u0016\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tJ\u0016\u0010\n\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t¨\u0006\u000b"}, d2 = {"Lcom/example/ardrawsketch/sketch/imagepicker/util/PermissionUtil;", "", "<init>", "()V", "isPermissionGranted", "", "context", "Landroid/content/Context;", "permission", "", "isPermissionInManifest", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes4.dex */
public final class PermissionUtil {
    public static final PermissionUtil INSTANCE = new PermissionUtil();

    private PermissionUtil() {
    }

    public final boolean isPermissionGranted(Context context, String permission) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(permission, "permission");
        int selfPermission = ContextCompat.checkSelfPermission(context, permission);
        return selfPermission == 0;
    }

    /* JADX WARN: Removed duplicated region for block: B:13:0x002d A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:14:0x002e  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final boolean isPermissionInManifest(Context r8, String r9) {
        /*
            r7 = this;
            java.lang.String r0 = "context"
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r8, r0)
            java.lang.String r0 = "permission"
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r9, r0)
            android.content.pm.PackageManager r0 = r8.getPackageManager()
            java.lang.String r1 = r8.getPackageName()
            r2 = 4096(0x1000, float:5.74E-42)
            android.content.pm.PackageInfo r0 = r0.getPackageInfo(r1, r2)
            java.lang.String[] r1 = r0.requestedPermissions
            r2 = 1
            r3 = 0
            if (r1 == 0) goto L2a
            int r4 = r1.length
            if (r4 != 0) goto L24
            r4 = r2
            goto L25
        L24:
            r4 = r3
        L25:
            if (r4 == 0) goto L28
            goto L2a
        L28:
            r4 = r3
            goto L2b
        L2a:
            r4 = r2
        L2b:
            if (r4 == 0) goto L2e
            return r3
        L2e:
            java.util.Iterator r4 = kotlin.jvm.internal.ArrayIteratorKt.iterator(r1)
        L32:
            boolean r5 = r4.hasNext()
            if (r5 == 0) goto L45
            java.lang.Object r5 = r4.next()
            java.lang.String r5 = (java.lang.String) r5
            boolean r6 = kotlin.jvm.internal.Intrinsics.areEqual(r5, r9)
            if (r6 == 0) goto L32
            return r2
        L45:
            return r3
        */
        throw new UnsupportedOperationException("Method not decompiled: com.example.ardrawingsketch.imagepicker.util.PermissionUtil.isPermissionInManifest(android.content.Context, java.lang.String):boolean");
    }
}
