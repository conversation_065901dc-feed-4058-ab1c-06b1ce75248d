<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:orientation="vertical" android:layout_width="match_parent" android:layout_height="wrap_content">
    <FrameLayout android:id="@+id/wrapper_controls" android:layout_width="match_parent" android:layout_height="@dimen/ucrop_height_wrapper_controls" android:layout_marginBottom="0dp" android:layout_alignParentTop="true">
        <ImageView android:background="@drawable/ucrop_wrapper_controls_shape" android:layout_width="match_parent" android:layout_height="match_parent"/>
        <LinearLayout android:gravity="center" android:orientation="horizontal" android:id="@+id/layout_aspect_ratio" android:layout_width="match_parent" android:layout_height="match_parent"/>
        <include android:id="@+id/layout_rotate_wheel" layout="@layout/ucrop_layout_rotate_wheel"/>
        <include android:id="@+id/layout_scale_wheel" layout="@layout/ucrop_layout_scale_wheel"/>
    </FrameLayout>
    <ImageView android:id="@+id/controls_shadow" android:layout_width="match_parent" android:layout_height="8dp" android:src="@drawable/ucrop_shadow_upside" android:layout_alignBottom="@+id/wrapper_controls"/>
    <LinearLayout android:gravity="center" android:orientation="horizontal" android:id="@+id/wrapper_states" android:background="@color/ucrop_color_widget_background" android:layout_width="match_parent" android:layout_height="@dimen/ucrop_height_wrapper_states" android:baselineAligned="false" android:layout_below="@+id/controls_shadow">
        <LinearLayout android:id="@+id/state_aspect_ratio" style="@style/ucrop_WrapperIconState">
            <ImageView android:gravity="center" android:orientation="vertical" android:id="@+id/image_view_state_aspect_ratio" app:srcCompat="@drawable/ucrop_crop" style="@style/ucrop_ImageViewWidgetIcon"/>
            <TextView android:id="@+id/text_view_crop" android:text="@string/ucrop_crop" style="@style/ucrop_TextViewWidget"/>
        </LinearLayout>
        <LinearLayout android:id="@+id/state_rotate" style="@style/ucrop_WrapperIconState">
            <ImageView android:id="@+id/image_view_state_rotate" app:srcCompat="@drawable/ucrop_rotate" style="@style/ucrop_ImageViewWidgetIcon"/>
            <TextView android:id="@+id/text_view_rotate" android:text="@string/ucrop_rotate" style="@style/ucrop_TextViewWidget"/>
        </LinearLayout>
        <LinearLayout android:id="@+id/state_scale" style="@style/ucrop_WrapperIconState">
            <ImageView android:id="@+id/image_view_state_scale" app:srcCompat="@drawable/ucrop_scale" style="@style/ucrop_ImageViewWidgetIcon"/>
            <TextView android:id="@+id/text_view_scale" android:text="@string/ucrop_scale" style="@style/ucrop_TextViewWidget"/>
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>
