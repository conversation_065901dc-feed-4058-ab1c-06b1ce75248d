<?xml version="1.0" encoding="utf-8"?>
<animated-selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_checked="true" android:id="@+id/on" android:drawable="@drawable/btn_radio_on_mtrl"/>
    <item android:id="@+id/off" android:drawable="@drawable/btn_radio_off_mtrl"/>
    <transition android:drawable="@drawable/btn_radio_on_to_off_mtrl_animation" android:toId="@+id/off" android:fromId="@+id/on"/>
    <transition android:drawable="@drawable/btn_radio_off_to_on_mtrl_animation" android:toId="@+id/on" android:fromId="@+id/off"/>
</animated-selector>
