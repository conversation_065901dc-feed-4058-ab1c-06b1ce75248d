package com.example.ardrawingsketch.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.example.ardrawingsketch.GlobalConstant;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.listener.CategoryListener;
import com.example.ardrawingsketch.model.Category;
import java.util.ArrayList;

/* loaded from: classes10.dex */
public class CategoriesAdapter extends RecyclerView.Adapter<ViewHolder> {
    private final ArrayList<Category> arrayList = GlobalConstant.createCategoryList();
    private final CategoryListener listener;
    private final Context mContext;

    public CategoriesAdapter(Context context, CategoryListener listener) {
        this.listener = listener;
        this.mContext = context;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_categories_list, parent, false);
        return new ViewHolder(view);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    public void onBindViewHolder(ViewHolder holder, int position) {
        final Category category = this.arrayList.get(position);
        Glide.with(this.mContext).load(Integer.valueOf(category.getImageResource())).into(holder.imgView);
        holder.itemView.setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.adapter.CategoriesAdapter$$ExternalSyntheticLambda0
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                CategoriesAdapter.this.m143xb1ac9f43(category, view);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$onBindViewHolder$0$com-example-ardrawsketch-sketch-adapter-CategoriesAdapter  reason: not valid java name */
    public /* synthetic */ void m143xb1ac9f43(Category category, View v) {
        if (this.listener != null) {
            this.listener.onCategoryClick(category);
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    public int getItemCount() {
        if (this.arrayList == null) {
            return 0;
        }
        return this.arrayList.size();
    }

    /* loaded from: classes10.dex */
    public static class ViewHolder extends RecyclerView.ViewHolder {
        private final AppCompatImageView imgView;

        public ViewHolder(View itemView) {
            super(itemView);
            this.imgView = (AppCompatImageView) itemView.findViewById(R.id.img_category);
        }
    }
}
