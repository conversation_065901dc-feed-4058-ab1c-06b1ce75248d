package com.example.ardrawingsketch.imagepicker;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import androidx.appcompat.app.AppCompatActivity;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.imagepicker.constant.ImageProvider;
import com.example.ardrawingsketch.imagepicker.provider.CameraProvider;
import com.example.ardrawingsketch.imagepicker.provider.CompressionProvider;
import com.example.ardrawingsketch.imagepicker.provider.CropProvider;
import com.example.ardrawingsketch.imagepicker.provider.GalleryProvider;
import com.example.ardrawingsketch.imagepicker.util.FileUriUtils;

/**
 * نشاط منتقي الصور الرئيسي
 * يوفر واجهة موحدة لاختيار الصور من مصادر مختلفة
 *
 * الميزات الرئيسية:
 * - اختيار الصور من المعرض
 * - التقاط صور جديدة بالكاميرا
 * - قص وتعديل الصور
 * - ضغط وتحسين الصور
 * - إدارة الصلاحيات والأخطاء
 * - دعم صيغ مختلفة للصور
 * - تخصيص إعدادات الجودة والحجم
 *
 * يعمل كواجهة موحدة لجميع عمليات اختيار ومعالجة الصور
 */
public final class ImagePickerActivity extends AppCompatActivity {

    /** علامة التسجيل لهذا النشاط */
    private static final String TAG = "ImagePickerActivity";

    // ===== مزودو الخدمات =====
    /** مزود خدمات الكاميرا */
    private CameraProvider mCameraProvider;
    /** مزود خدمات ضغط الصور */
    private CompressionProvider mCompressionProvider;
    /** مزود خدمات قص الصور */
    private CropProvider mCropProvider;
    /** مزود خدمات المعرض */
    private GalleryProvider mGalleryProvider;

    public /* synthetic */ class WhenMappings {
        public static final /* synthetic */ int[] $EnumSwitchMapping$0;

        static {
            int[] iArr = new int[ImageProvider.values().length];
            try {
                iArr[ImageProvider.GALLERY.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                iArr[ImageProvider.CAMERA.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            $EnumSwitchMapping$0 = iArr;
        }
    }

    /* compiled from: ImagePickerActivity.kt */
    @Metadata(d1 = {
            "\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J\u0015\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0000¢\u0006\u0002\b\nR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082T¢\u0006\u0002\n\u0000¨\u0006\u000b" }, d2 = {
                    "Lcom/example/ardrawsketch/sketch/imagepicker/ImagePickerActivity$Companion;", "", "<init>", "()V",
                    "TAG", "", "getCancelledIntent", "Landroid/content/Intent;", "context", "Landroid/content/Context;",
                    "getCancelledIntent$app_debug", "app_debug" }, k = 1, mv = { 2, 1,
                            0 }, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* loaded from: classes9.dex */
    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        private Companion() {
        }

        public final Intent getCancelledIntent$app_debug(Context context) {
            Intrinsics.checkNotNullParameter(context, "context");
            Intent intent = new Intent();
            String message = context.getString(R.string.error_task_cancelled);
            Intrinsics.checkNotNullExpressionValue(message, "getString(...)");
            intent.putExtra(ImagePicker.EXTRA_ERROR, message);
            return intent;
        }
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity,
              // androidx.core.app.ComponentActivity, android.app.Activity
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        loadBundle(savedInstanceState);
    }

    @Override // androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity,
              // android.app.Activity
    public void onSaveInstanceState(Bundle outState) {
        Intrinsics.checkNotNullParameter(outState, "outState");
        CameraProvider cameraProvider = this.mCameraProvider;
        if (cameraProvider != null) {
            cameraProvider.onSaveInstanceState(outState);
        }
        CropProvider cropProvider = this.mCropProvider;
        if (cropProvider == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mCropProvider");
            cropProvider = null;
        }
        cropProvider.onSaveInstanceState(outState);
        super.onSaveInstanceState(outState);
    }

    private final void loadBundle(Bundle savedInstanceState) {
        GalleryProvider galleryProvider;
        CameraProvider cameraProvider;
        this.mCropProvider = new CropProvider(this);
        CropProvider cropProvider = this.mCropProvider;
        if (cropProvider == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mCropProvider");
            cropProvider = null;
        }
        cropProvider.onRestoreInstanceState(savedInstanceState);
        this.mCompressionProvider = new CompressionProvider(this);
        Intent intent = getIntent();
        ImageProvider provider = (ImageProvider) (intent != null
                ? intent.getSerializableExtra(ImagePicker.EXTRA_IMAGE_PROVIDER)
                : null);
        switch (provider == null ? -1 : WhenMappings.$EnumSwitchMapping$0[provider.ordinal()]) {
            case 1:
                this.mGalleryProvider = new GalleryProvider(this);
                if (savedInstanceState != null || (galleryProvider = this.mGalleryProvider) == null) {
                    return;
                }
                galleryProvider.startIntent();
                Unit unit = Unit.INSTANCE;
                return;
            case 2:
                this.mCameraProvider = new CameraProvider(this);
                CameraProvider cameraProvider2 = this.mCameraProvider;
                if (cameraProvider2 != null) {
                    cameraProvider2.onRestoreInstanceState(savedInstanceState);
                }
                if (savedInstanceState != null || (cameraProvider = this.mCameraProvider) == null) {
                    return;
                }
                cameraProvider.startIntent();
                Unit unit2 = Unit.INSTANCE;
                return;
            default:
                Log.e(TAG, "Image provider can not be null");
                String string = getString(R.string.error_task_cancelled);
                Intrinsics.checkNotNullExpressionValue(string, "getString(...)");
                setError(string);
                Unit unit3 = Unit.INSTANCE;
                return;
        }
    }

    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity,
              // android.app.Activity
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        Intrinsics.checkNotNullParameter(permissions, "permissions");
        Intrinsics.checkNotNullParameter(grantResults, "grantResults");
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        CameraProvider cameraProvider = this.mCameraProvider;
        if (cameraProvider != null) {
            cameraProvider.onRequestPermissionsResult(requestCode);
        }
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity,
              // android.app.Activity
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        CameraProvider cameraProvider = this.mCameraProvider;
        if (cameraProvider != null) {
            cameraProvider.onActivityResult(requestCode, resultCode, data);
        }
        GalleryProvider galleryProvider = this.mGalleryProvider;
        if (galleryProvider != null) {
            galleryProvider.onActivityResult(requestCode, resultCode, data);
        }
        CropProvider cropProvider = this.mCropProvider;
        if (cropProvider == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mCropProvider");
            cropProvider = null;
        }
        cropProvider.onActivityResult(requestCode, resultCode, data);
    }

    @Override // androidx.activity.ComponentActivity, android.app.Activity
    public void onBackPressed() {
        setResultCancel();
    }

    public final void setImage(Uri uri) {
        Intrinsics.checkNotNullParameter(uri, "uri");
        CropProvider cropProvider = this.mCropProvider;
        CompressionProvider compressionProvider = null;
        CropProvider cropProvider2 = null;
        if (cropProvider == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mCropProvider");
            cropProvider = null;
        }
        if (!cropProvider.isCropEnabled()) {
            CompressionProvider compressionProvider2 = this.mCompressionProvider;
            if (compressionProvider2 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("mCompressionProvider");
                compressionProvider2 = null;
            }
            if (!compressionProvider2.isCompressionRequired(uri)) {
                setResult(uri);
                return;
            }
            CompressionProvider compressionProvider3 = this.mCompressionProvider;
            if (compressionProvider3 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("mCompressionProvider");
            } else {
                compressionProvider = compressionProvider3;
            }
            compressionProvider.compress(uri);
            return;
        }
        CropProvider cropProvider3 = this.mCropProvider;
        if (cropProvider3 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mCropProvider");
        } else {
            cropProvider2 = cropProvider3;
        }
        cropProvider2.startIntent(uri);
    }

    public final void setCropImage(Uri uri) {
        Intrinsics.checkNotNullParameter(uri, "uri");
        CameraProvider cameraProvider = this.mCameraProvider;
        if (cameraProvider != null) {
            cameraProvider.delete();
        }
        CompressionProvider compressionProvider = this.mCompressionProvider;
        CompressionProvider compressionProvider2 = null;
        if (compressionProvider == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mCompressionProvider");
            compressionProvider = null;
        }
        if (compressionProvider.isCompressionRequired(uri)) {
            CompressionProvider compressionProvider3 = this.mCompressionProvider;
            if (compressionProvider3 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("mCompressionProvider");
            } else {
                compressionProvider2 = compressionProvider3;
            }
            compressionProvider2.compress(uri);
            return;
        }
        setResult(uri);
    }

    public final void setCompressedImage(Uri uri) {
        Intrinsics.checkNotNullParameter(uri, "uri");
        CameraProvider cameraProvider = this.mCameraProvider;
        if (cameraProvider != null) {
            cameraProvider.delete();
        }
        CropProvider cropProvider = this.mCropProvider;
        if (cropProvider == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mCropProvider");
            cropProvider = null;
        }
        cropProvider.delete();
        setResult(uri);
    }

    private final void setResult(Uri uri) {
        Intent intent = new Intent();
        intent.setData(uri);
        intent.putExtra(ImagePicker.EXTRA_FILE_PATH, FileUriUtils.INSTANCE.getRealPath(this, uri));
        setResult(-1, intent);
        finish();
    }

    public final void setResultCancel() {
        setResult(0, Companion.getCancelledIntent$app_debug(this));
        finish();
    }

    public final void setError(String message) {
        Intrinsics.checkNotNullParameter(message, "message");
        Intent intent = new Intent();
        intent.putExtra(ImagePicker.EXTRA_ERROR, message);
        setResult(64, intent);
        finish();
    }
}
