package com.example.ardrawingsketch.imagepicker;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.imagepicker.constant.ImageProvider;
import com.example.ardrawingsketch.imagepicker.provider.CameraProvider;
import com.example.ardrawingsketch.imagepicker.provider.CompressionProvider;
import com.example.ardrawingsketch.imagepicker.provider.CropProvider;
import com.example.ardrawingsketch.imagepicker.provider.GalleryProvider;
import com.example.ardrawingsketch.imagepicker.util.FileUriUtils;
import kotlin.Metadata;
import kotlin.Unit;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: ImagePickerActivity.kt */
@Metadata(d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0015\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\b\u0018\u0000 *2\u00020\u0001:\u0001*B\u0007¢\u0006\u0004\b\u0002\u0010\u0003J\u0012\u0010\f\u001a\u00020\r2\b\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u0014J\u0010\u0010\u0010\u001a\u00020\r2\u0006\u0010\u0011\u001a\u00020\u000fH\u0016J\u0012\u0010\u0012\u001a\u00020\r2\b\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u0002J+\u0010\u0013\u001a\u00020\r2\u0006\u0010\u0014\u001a\u00020\u00152\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00180\u00172\u0006\u0010\u0019\u001a\u00020\u001aH\u0016¢\u0006\u0002\u0010\u001bJ\"\u0010\u001c\u001a\u00020\r2\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u001d\u001a\u00020\u00152\b\u0010\u001e\u001a\u0004\u0018\u00010\u001fH\u0014J\b\u0010 \u001a\u00020\rH\u0016J\u000e\u0010!\u001a\u00020\r2\u0006\u0010\"\u001a\u00020#J\u000e\u0010$\u001a\u00020\r2\u0006\u0010\"\u001a\u00020#J\u000e\u0010%\u001a\u00020\r2\u0006\u0010\"\u001a\u00020#J\u0010\u0010&\u001a\u00020\r2\u0006\u0010\"\u001a\u00020#H\u0002J\u0006\u0010'\u001a\u00020\rJ\u000e\u0010(\u001a\u00020\r2\u0006\u0010)\u001a\u00020\u0018R\u0010\u0010\u0004\u001a\u0004\u0018\u00010\u0005X\u0082\u000e¢\u0006\u0002\n\u0000R\u0010\u0010\u0006\u001a\u0004\u0018\u00010\u0007X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082.¢\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082.¢\u0006\u0002\n\u0000¨\u0006+"}, d2 = {"Lcom/example/ardrawsketch/sketch/imagepicker/ImagePickerActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "<init>", "()V", "mGalleryProvider", "Lcom/example/ardrawsketch/sketch/imagepicker/provider/GalleryProvider;", "mCameraProvider", "Lcom/example/ardrawsketch/sketch/imagepicker/provider/CameraProvider;", "mCropProvider", "Lcom/example/ardrawsketch/sketch/imagepicker/provider/CropProvider;", "mCompressionProvider", "Lcom/example/ardrawsketch/sketch/imagepicker/provider/CompressionProvider;", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "onSaveInstanceState", "outState", "loadBundle", "onRequestPermissionsResult", "requestCode", "", "permissions", "", "", "grantResults", "", "(I[Ljava/lang/String;[I)V", "onActivityResult", "resultCode", "data", "Landroid/content/Intent;", "onBackPressed", "setImage", "uri", "Landroid/net/Uri;", "setCropImage", "setCompressedImage", "setResult", "setResultCancel", "setError", "message", "Companion", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes9.dex */
public final class ImagePickerActivity extends AppCompatActivity {
    public static final Companion Companion = new Companion(null);
    private static final String TAG = "image_picker";
    private CameraProvider mCameraProvider;
    private CompressionProvider mCompressionProvider;
    private CropProvider mCropProvider;
    private GalleryProvider mGalleryProvider;

    /* compiled from: ImagePickerActivity.kt */
    @Metadata(k = 3, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* loaded from: classes9.dex */
    public /* synthetic */ class WhenMappings {
        public static final /* synthetic */ int[] $EnumSwitchMapping$0;

        static {
            int[] iArr = new int[ImageProvider.values().length];
            try {
                iArr[ImageProvider.GALLERY.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                iArr[ImageProvider.CAMERA.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            $EnumSwitchMapping$0 = iArr;
        }
    }

    /* compiled from: ImagePickerActivity.kt */
    @Metadata(d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J\u0015\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0000¢\u0006\u0002\b\nR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082T¢\u0006\u0002\n\u0000¨\u0006\u000b"}, d2 = {"Lcom/example/ardrawsketch/sketch/imagepicker/ImagePickerActivity$Companion;", "", "<init>", "()V", "TAG", "", "getCancelledIntent", "Landroid/content/Intent;", "context", "Landroid/content/Context;", "getCancelledIntent$app_debug", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* loaded from: classes9.dex */
    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        private Companion() {
        }

        public final Intent getCancelledIntent$app_debug(Context context) {
            Intrinsics.checkNotNullParameter(context, "context");
            Intent intent = new Intent();
            String message = context.getString(R.string.error_task_cancelled);
            Intrinsics.checkNotNullExpressionValue(message, "getString(...)");
            intent.putExtra(ImagePicker.EXTRA_ERROR, message);
            return intent;
        }
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        loadBundle(savedInstanceState);
    }

    @Override // androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    public void onSaveInstanceState(Bundle outState) {
        Intrinsics.checkNotNullParameter(outState, "outState");
        CameraProvider cameraProvider = this.mCameraProvider;
        if (cameraProvider != null) {
            cameraProvider.onSaveInstanceState(outState);
        }
        CropProvider cropProvider = this.mCropProvider;
        if (cropProvider == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mCropProvider");
            cropProvider = null;
        }
        cropProvider.onSaveInstanceState(outState);
        super.onSaveInstanceState(outState);
    }

    private final void loadBundle(Bundle savedInstanceState) {
        GalleryProvider galleryProvider;
        CameraProvider cameraProvider;
        this.mCropProvider = new CropProvider(this);
        CropProvider cropProvider = this.mCropProvider;
        if (cropProvider == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mCropProvider");
            cropProvider = null;
        }
        cropProvider.onRestoreInstanceState(savedInstanceState);
        this.mCompressionProvider = new CompressionProvider(this);
        Intent intent = getIntent();
        ImageProvider provider = (ImageProvider) (intent != null ? intent.getSerializableExtra(ImagePicker.EXTRA_IMAGE_PROVIDER) : null);
        switch (provider == null ? -1 : WhenMappings.$EnumSwitchMapping$0[provider.ordinal()]) {
            case 1:
                this.mGalleryProvider = new GalleryProvider(this);
                if (savedInstanceState != null || (galleryProvider = this.mGalleryProvider) == null) {
                    return;
                }
                galleryProvider.startIntent();
                Unit unit = Unit.INSTANCE;
                return;
            case 2:
                this.mCameraProvider = new CameraProvider(this);
                CameraProvider cameraProvider2 = this.mCameraProvider;
                if (cameraProvider2 != null) {
                    cameraProvider2.onRestoreInstanceState(savedInstanceState);
                }
                if (savedInstanceState != null || (cameraProvider = this.mCameraProvider) == null) {
                    return;
                }
                cameraProvider.startIntent();
                Unit unit2 = Unit.INSTANCE;
                return;
            default:
                Log.e(TAG, "Image provider can not be null");
                String string = getString(R.string.error_task_cancelled);
                Intrinsics.checkNotNullExpressionValue(string, "getString(...)");
                setError(string);
                Unit unit3 = Unit.INSTANCE;
                return;
        }
    }

    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, android.app.Activity
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        Intrinsics.checkNotNullParameter(permissions, "permissions");
        Intrinsics.checkNotNullParameter(grantResults, "grantResults");
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        CameraProvider cameraProvider = this.mCameraProvider;
        if (cameraProvider != null) {
            cameraProvider.onRequestPermissionsResult(requestCode);
        }
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, android.app.Activity
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        CameraProvider cameraProvider = this.mCameraProvider;
        if (cameraProvider != null) {
            cameraProvider.onActivityResult(requestCode, resultCode, data);
        }
        GalleryProvider galleryProvider = this.mGalleryProvider;
        if (galleryProvider != null) {
            galleryProvider.onActivityResult(requestCode, resultCode, data);
        }
        CropProvider cropProvider = this.mCropProvider;
        if (cropProvider == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mCropProvider");
            cropProvider = null;
        }
        cropProvider.onActivityResult(requestCode, resultCode, data);
    }

    @Override // androidx.activity.ComponentActivity, android.app.Activity
    public void onBackPressed() {
        setResultCancel();
    }

    public final void setImage(Uri uri) {
        Intrinsics.checkNotNullParameter(uri, "uri");
        CropProvider cropProvider = this.mCropProvider;
        CompressionProvider compressionProvider = null;
        CropProvider cropProvider2 = null;
        if (cropProvider == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mCropProvider");
            cropProvider = null;
        }
        if (!cropProvider.isCropEnabled()) {
            CompressionProvider compressionProvider2 = this.mCompressionProvider;
            if (compressionProvider2 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("mCompressionProvider");
                compressionProvider2 = null;
            }
            if (!compressionProvider2.isCompressionRequired(uri)) {
                setResult(uri);
                return;
            }
            CompressionProvider compressionProvider3 = this.mCompressionProvider;
            if (compressionProvider3 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("mCompressionProvider");
            } else {
                compressionProvider = compressionProvider3;
            }
            compressionProvider.compress(uri);
            return;
        }
        CropProvider cropProvider3 = this.mCropProvider;
        if (cropProvider3 == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mCropProvider");
        } else {
            cropProvider2 = cropProvider3;
        }
        cropProvider2.startIntent(uri);
    }

    public final void setCropImage(Uri uri) {
        Intrinsics.checkNotNullParameter(uri, "uri");
        CameraProvider cameraProvider = this.mCameraProvider;
        if (cameraProvider != null) {
            cameraProvider.delete();
        }
        CompressionProvider compressionProvider = this.mCompressionProvider;
        CompressionProvider compressionProvider2 = null;
        if (compressionProvider == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mCompressionProvider");
            compressionProvider = null;
        }
        if (compressionProvider.isCompressionRequired(uri)) {
            CompressionProvider compressionProvider3 = this.mCompressionProvider;
            if (compressionProvider3 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("mCompressionProvider");
            } else {
                compressionProvider2 = compressionProvider3;
            }
            compressionProvider2.compress(uri);
            return;
        }
        setResult(uri);
    }

    public final void setCompressedImage(Uri uri) {
        Intrinsics.checkNotNullParameter(uri, "uri");
        CameraProvider cameraProvider = this.mCameraProvider;
        if (cameraProvider != null) {
            cameraProvider.delete();
        }
        CropProvider cropProvider = this.mCropProvider;
        if (cropProvider == null) {
            Intrinsics.throwUninitializedPropertyAccessException("mCropProvider");
            cropProvider = null;
        }
        cropProvider.delete();
        setResult(uri);
    }

    private final void setResult(Uri uri) {
        Intent intent = new Intent();
        intent.setData(uri);
        intent.putExtra(ImagePicker.EXTRA_FILE_PATH, FileUriUtils.INSTANCE.getRealPath(this, uri));
        setResult(-1, intent);
        finish();
    }

    public final void setResultCancel() {
        setResult(0, Companion.getCancelledIntent$app_debug(this));
        finish();
    }

    public final void setError(String message) {
        Intrinsics.checkNotNullParameter(message, "message");
        Intent intent = new Intent();
        intent.putExtra(ImagePicker.EXTRA_ERROR, message);
        setResult(64, intent);
        finish();
    }
}
