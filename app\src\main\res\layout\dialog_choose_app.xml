<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:orientation="horizontal" android:padding="16dp" android:layout_width="match_parent" android:layout_height="wrap_content" android:baselineAligned="false">
    <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@+id/lytCameraPick" android:layout_width="0dp" android:layout_height="wrap_content" android:layout_marginTop="10dp" android:layout_weight="1">
        <androidx.appcompat.widget.AppCompatImageView android:layout_width="48dp" android:layout_height="48dp" app:srcCompat="@drawable/ic_camera" app:tint="@color/dialog_choose_icon_color"/>
        <androidx.appcompat.widget.AppCompatTextView android:textAppearance="@style/TextAppearance.AppCompat.Medium" android:textColor="@color/dialog_choose_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="5dp" android:text="@string/title_camera"/>
    </LinearLayout>
    <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@+id/lytGalleryPick" android:layout_width="0dp" android:layout_height="wrap_content" android:layout_marginTop="10dp" android:layout_weight="1">
        <androidx.appcompat.widget.AppCompatImageView android:layout_width="48dp" android:layout_height="48dp" app:srcCompat="@drawable/ic_photo" app:tint="@color/dialog_choose_icon_color"/>
        <androidx.appcompat.widget.AppCompatTextView android:textAppearance="@style/TextAppearance.AppCompat.Medium" android:textColor="@color/dialog_choose_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="5dp" android:text="@string/title_gallery"/>
    </LinearLayout>
</LinearLayout>
