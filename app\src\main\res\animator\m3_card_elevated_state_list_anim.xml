<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <item android:state_enabled="true" android:state_hovered="true">
        <objectAnimator android:interpolator="?attr/motionEasingEmphasizedInterpolator" android:duration="?attr/motionDurationMedium4" android:valueTo="@dimen/m3_card_elevated_hovered_z" android:valueType="floatType" android:propertyName="translationZ" android:startDelay="?attr/motionDurationMedium1"/>
    </item>
    <item android:state_enabled="true" app:state_dragged="true">
        <objectAnimator android:interpolator="?attr/motionEasingEmphasizedInterpolator" android:duration="?attr/motionDurationMedium4" android:valueTo="@dimen/m3_card_elevated_dragged_z" android:valueType="floatType" android:propertyName="translationZ" android:startDelay="?attr/motionDurationMedium1"/>
    </item>
    <item>
        <set>
            <objectAnimator android:interpolator="?attr/motionEasingEmphasizedInterpolator" android:duration="?attr/motionDurationLong2" android:valueTo="0dp" android:valueType="floatType" android:propertyName="translationZ"/>
        </set>
    </item>
    <item>
        <objectAnimator android:duration="0" android:valueTo="0dp" android:valueType="floatType" android:propertyName="translationZ"/>
    </item>
</selector>
