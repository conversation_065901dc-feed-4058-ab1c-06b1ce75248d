<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:id="@+id/main" android:background="@color/white" android:layout_width="match_parent" android:layout_height="match_parent">
    <com.airbnb.lottie.LottieAnimationView android:id="@+id/splash_gif" android:layout_width="match_parent" android:layout_height="0dp" android:layout_marginTop="56dp" android:layout_marginBottom="56dp" app:layout_constraintBottom_toTopOf="@+id/splash_text" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0" app:lottie_autoPlay="true" app:lottie_loop="false" app:lottie_rawRes="@raw/splash" app:lottie_speed="0.5"/>
    <TextView android:textSize="25sp" android:textStyle="bold" android:textColor="@color/app_color" android:id="@+id/splash_text" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="56dp" android:text="@string/ar_draw_sketch" android:fontFamily="@font/poppins_bold" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toBottomOf="@+id/splash_gif"/>
</androidx.constraintlayout.widget.ConstraintLayout>
