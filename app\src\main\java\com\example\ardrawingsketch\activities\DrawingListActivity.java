package com.example.ardrawingsketch.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.OnApplyWindowInsetsListener;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import com.example.ardrawingsketch.GlobalConstant;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.adapter.DrawingListAdapter;
import com.example.ardrawingsketch.databinding.ActivityDrawingListBinding;
import com.example.ardrawingsketch.listener.DrawingListener;
import com.example.ardrawingsketch.model.Category;

/* loaded from: classes6.dex */
public class DrawingListActivity extends AppCompatActivity {
    DrawingListAdapter adapter;
    ActivityDrawingListBinding binding;
    String category_name;
    int[] drawingList;

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        this.binding = ActivityDrawingListBinding.inflate(getLayoutInflater());
        setContentView(this.binding.getRoot());
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), new OnApplyWindowInsetsListener() { // from class: com.example.ardrawingsketch.activities.DrawingListActivity$$ExternalSyntheticLambda1
            @Override // androidx.core.view.OnApplyWindowInsetsListener
            public final WindowInsetsCompat onApplyWindowInsets(View view, WindowInsetsCompat windowInsetsCompat) {
                return DrawingListActivity.lambda$onCreate$0(view, windowInsetsCompat);
            }
        });
        getData();
        initData();
        initListener();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    public static /* synthetic */ WindowInsetsCompat lambda$onCreate$0(View v, WindowInsetsCompat insets) {
        Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
        v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
        return insets;
    }

    private void initData() {
        this.adapter = new DrawingListAdapter(this, this.drawingList, new DrawingListener() { // from class: com.example.ardrawingsketch.activities.DrawingListActivity.1
            @Override // com.example.ardrawingsketch.listener.DrawingListener
            public void onDrawingChoice(int imageResource) {
                Intent intent = new Intent(DrawingListActivity.this, GuideActivity.class);
                intent.putExtra(GlobalConstant.FROM_DRAWABLE, true);
                intent.putExtra(GlobalConstant.IMAGE_PATH, imageResource);
                DrawingListActivity.this.startActivity(intent);
            }
        });
        this.binding.drawingListRv.setLayoutManager(new GridLayoutManager(this, 2));
        this.binding.drawingListRv.setHasFixedSize(true);
        this.binding.drawingListRv.setAdapter(this.adapter);
    }

    private void initListener() {
        this.binding.btnBack.setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.activities.DrawingListActivity$$ExternalSyntheticLambda0
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                DrawingListActivity.this.m138x214e21e5(view);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$initListener$1$com-example-ardrawsketch-sketch-activities-DrawingListActivity  reason: not valid java name */
    public /* synthetic */ void m138x214e21e5(View v) {
        finish();
    }

    private void getData() {
        Intent intent = getIntent();
        Category category = (Category) intent.getSerializableExtra(GlobalConstant.CATEGORY_CHOICE);
        this.binding.labelHelp.setText(category.getNameCategory());
        this.drawingList = getList(category.getId());
    }

    private int[] getList(int id) {
        switch (id) {
            case 0:
                return GlobalConstant.listTrending;
            case 1:
                return GlobalConstant.listDrawing;
            case 2:
                return GlobalConstant.listAnime;
            case 3:
                return GlobalConstant.listPeople;
            case 4:
                return GlobalConstant.listAnimal;
            case 5:
                return GlobalConstant.listAesthetics;
            case 6:
                return GlobalConstant.listCars;
            case 7:
                return GlobalConstant.listKid;
            case 8:
                return GlobalConstant.listNature;
            default:
                return GlobalConstant.listDrawing;
        }
    }
}
