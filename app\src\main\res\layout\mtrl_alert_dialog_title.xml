<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" android:orientation="vertical" android:id="@+id/topPanel" android:layout_width="match_parent" android:layout_height="wrap_content">
    <LinearLayout android:id="@+id/title_template" style="?attr/materialAlertDialogTitlePanelStyle">
        <ImageView android:id="@android:id/icon" style="?attr/materialAlertDialogTitleIconStyle"/>
        <androidx.appcompat.widget.DialogTitle android:id="@+id/alertTitle" style="?attr/materialAlertDialogTitleTextStyle"/>
    </LinearLayout>
    <android.widget.Space android:id="@+id/titleDividerNoCustom" android:visibility="gone" android:layout_width="match_parent" android:layout_height="@dimen/abc_dialog_title_divider_material"/>
</LinearLayout>
