<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:id="@+id/container" android:fitsSystemWindows="true" android:layout_width="match_parent" android:layout_height="match_parent">
    <androidx.coordinatorlayout.widget.CoordinatorLayout android:id="@+id/coordinator" android:fitsSystemWindows="true" android:layout_width="match_parent" android:layout_height="match_parent">
        <View android:id="@+id/touch_outside" android:focusable="false" android:layout_width="match_parent" android:layout_height="match_parent" android:soundEffectsEnabled="false" android:importantForAccessibility="no"/>
        <FrameLayout android:id="@+id/m3_side_sheet" app:layout_behavior="@string/side_sheet_behavior" style="?attr/sideSheetModalStyle"/>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</FrameLayout>
