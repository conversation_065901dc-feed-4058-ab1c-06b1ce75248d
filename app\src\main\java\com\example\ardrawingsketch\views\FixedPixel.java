package com.example.ardrawingsketch.views;

import androidx.constraintlayout.widget.ConstraintLayout;
import kotlin.Metadata;
import kotlin.enums.EnumEntries;
import kotlin.enums.EnumEntriesKt;


public enum FixedPixel {
    CENTER,
    TOP_LEFT,
    BOTTOM_RIGHT;
    
    private static final /* synthetic */ EnumEntries $ENTRIES = EnumEntriesKt.enumEntries($VALUES);

    public static EnumEntries<FixedPixel> getEntries() {
        return $ENTRIES;
    }
}
