package com.example.ardrawingsketch.views;

/**
 * تعداد نقاط التثبيت الثابتة للبكسل
 * يحدد النقاط المرجعية التي تبقى ثابتة عند تغيير حجم أو تحريك الصورة
 *
 * يُستخدم في عمليات التكبير والتصغير لتحديد النقطة التي ستبقى في موضعها
 * بينما يتم تغيير باقي أجزاء الصورة حولها
 */
public enum FixedPixel {

    /**
     * المركز
     * النقطة المركزية للصورة تبقى ثابتة
     * مفيد عند التكبير من المركز أو عرض الصورة بشكل متوازن
     */
    CENTER,

    /**
     * الزاوية العلوية اليسرى
     * النقطة في أعلى يسار الصورة تبقى ثابتة
     * مفيد عند التكبير من الزاوية أو عرض الصورة من البداية
     */
    TOP_LEFT,

    /**
     * الزاوية السفلية اليمنى
     * النقطة في أسفل يمين الصورة تبقى ثابتة
     * مفيد عند التكبير من الزاوية المقابلة أو عرض الصورة من النهاية
     */
    BOTTOM_RIGHT
}
