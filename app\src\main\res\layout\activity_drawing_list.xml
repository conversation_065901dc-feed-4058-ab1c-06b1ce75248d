<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:id="@+id/main" android:background="@color/appmaincolorm" android:layout_width="match_parent" android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/toolbar" android:background="@color/appmaincolorm" android:layout_width="match_parent" android:layout_height="?attr/actionBarSize" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0">
        <androidx.appcompat.widget.AppCompatImageView android:id="@+id/btn_back" android:padding="12dp" android:layout_width="50dp" android:layout_height="50dp" android:src="@drawable/ic_back" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
        <TextView android:textSize="18sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:id="@+id/label_help" android:layout_width="wrap_content" android:layout_height="match_parent" android:text="@string/text_ar_draw_sketch_sketch_amp_paint" android:maxLines="1" android:maxEms="12" android:fontFamily="@font/poppins_bold" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.recyclerview.widget.RecyclerView android:id="@+id/drawing_list_rv" android:background="#d3e7f8" android:paddingLeft="5dp" android:paddingRight="5dp" android:clipToPadding="true" android:layout_width="match_parent" android:layout_height="0dp" android:paddingHorizontal="5dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toBottomOf="@+id/toolbar"/>
</androidx.constraintlayout.widget.ConstraintLayout>
