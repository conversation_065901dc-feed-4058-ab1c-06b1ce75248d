package com.example.ardrawingsketch.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ardrawingsketch.R;

/* loaded from: classes3.dex */
public final class DialogChooseAppBinding implements ViewBinding {
    public final LinearLayout lytCameraPick;
    public final LinearLayout lytGalleryPick;
    private final LinearLayout rootView;

    private DialogChooseAppBinding(LinearLayout rootView, LinearLayout lytCameraPick, LinearLayout lytGalleryPick) {
        this.rootView = rootView;
        this.lytCameraPick = lytCameraPick;
        this.lytGalleryPick = lytGalleryPick;
    }

    @Override // androidx.viewbinding.ViewBinding
    public LinearLayout getRoot() {
        return this.rootView;
    }

    public static DialogChooseAppBinding inflate(LayoutInflater inflater) {
        return inflate(inflater, null, false);
    }

    public static DialogChooseAppBinding inflate(LayoutInflater inflater, ViewGroup parent, boolean attachToParent) {
        View root = inflater.inflate(R.layout.dialog_choose_app, parent, false);
        if (attachToParent) {
            parent.addView(root);
        }
        return bind(root);
    }

    public static DialogChooseAppBinding bind(View rootView) {
        int id = R.id.lytCameraPick;
        LinearLayout lytCameraPick = (LinearLayout) ViewBindings.findChildViewById(rootView, id);
        if (lytCameraPick != null) {
            id = R.id.lytGalleryPick;
            LinearLayout lytGalleryPick = (LinearLayout) ViewBindings.findChildViewById(rootView, id);
            if (lytGalleryPick != null) {
                return new DialogChooseAppBinding((LinearLayout) rootView, lytCameraPick, lytGalleryPick);
            }
        }
        String missingId = rootView.getResources().getResourceName(id);
        throw new NullPointerException("Missing required view with ID: ".concat(missingId));
    }
}
