package com.example.ardrawingsketch.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ardrawingsketch.R;

/* loaded from: classes3.dex */
public final class ItemCategoriesListBinding implements ViewBinding {
    public final CardView categoriesParent;
    public final AppCompatImageView imgCategory;
    private final CardView rootView;

    private ItemCategoriesListBinding(CardView rootView, CardView categoriesParent, AppCompatImageView imgCategory) {
        this.rootView = rootView;
        this.categoriesParent = categoriesParent;
        this.imgCategory = imgCategory;
    }

    @Override // androidx.viewbinding.ViewBinding
    public CardView getRoot() {
        return this.rootView;
    }

    public static ItemCategoriesListBinding inflate(LayoutInflater inflater) {
        return inflate(inflater, null, false);
    }

    public static ItemCategoriesListBinding inflate(LayoutInflater inflater, ViewGroup parent, boolean attachToParent) {
        View root = inflater.inflate(R.layout.item_categories_list, parent, false);
        if (attachToParent) {
            parent.addView(root);
        }
        return bind(root);
    }

    public static ItemCategoriesListBinding bind(View rootView) {
        CardView categoriesParent = (CardView) rootView;
        int id = R.id.img_category;
        AppCompatImageView imgCategory = (AppCompatImageView) ViewBindings.findChildViewById(rootView, id);
        if (imgCategory != null) {
            return new ItemCategoriesListBinding((CardView) rootView, categoriesParent, imgCategory);
        }
        String missingId = rootView.getResources().getResourceName(id);
        throw new NullPointerException("Missing required view with ID: ".concat(missingId));
    }
}
