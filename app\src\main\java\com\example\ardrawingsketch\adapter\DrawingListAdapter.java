package com.example.ardrawingsketch.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.listener.DrawingListener;

/* loaded from: classes10.dex */
public class DrawingListAdapter extends RecyclerView.Adapter<ViewHolder> {
    private int[] arraylist;
    private Context mContext;
    private DrawingListener mListener;

    public DrawingListAdapter(Context mContext, int[] arraylist, DrawingListener mListener) {
        this.mContext = mContext;
        this.arraylist = arraylist;
        this.mListener = mListener;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_categories_list, parent, false);
        return new ViewHolder(view);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    public void onBindViewHolder(ViewHolder holder, int position) {
        final int imageResource = this.arraylist[position];
        Glide.with(this.mContext).load(Integer.valueOf(imageResource)).into(holder.imgView);
        holder.itemView.setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.adapter.DrawingListAdapter$$ExternalSyntheticLambda0
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                DrawingListAdapter.this.m145x25da4623(imageResource, view);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$onBindViewHolder$0$com-example-ardrawsketch-sketch-adapter-DrawingListAdapter  reason: not valid java name */
    public /* synthetic */ void m145x25da4623(int imageResource, View v) {
        if (this.mListener != null) {
            this.mListener.onDrawingChoice(imageResource);
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    public int getItemCount() {
        if (this.arraylist == null) {
            return 0;
        }
        return this.arraylist.length;
    }

    /* loaded from: classes10.dex */
    public static class ViewHolder extends RecyclerView.ViewHolder {
        private final AppCompatImageView imgView;

        public ViewHolder(View itemView) {
            super(itemView);
            this.imgView = (AppCompatImageView) itemView.findViewById(R.id.img_category);
        }
    }
}
