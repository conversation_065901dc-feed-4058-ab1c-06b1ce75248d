package com.example.ardrawingsketch.activities;

import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.view.View;
import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.OnApplyWindowInsetsListener;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import com.example.ardrawingsketch.GlobalConstant;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.databinding.ActivityGuideBinding;

/* loaded from: classes6.dex */
public class GuideActivity extends AppCompatActivity {
    ActivityGuideBinding binding;
    boolean fromGallery;
    String imagePath;
    int imgResource;
    long mLastClickTime = 0;

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        this.binding = ActivityGuideBinding.inflate(getLayoutInflater());
        setContentView(this.binding.getRoot());
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), new OnApplyWindowInsetsListener() { // from class: com.example.ardrawingsketch.activities.GuideActivity$$ExternalSyntheticLambda1
            @Override // androidx.core.view.OnApplyWindowInsetsListener
            public final WindowInsetsCompat onApplyWindowInsets(View view, WindowInsetsCompat windowInsetsCompat) {
                return GuideActivity.lambda$onCreate$0(view, windowInsetsCompat);
            }
        });
        getData();
        initListener();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    public static /* synthetic */ WindowInsetsCompat lambda$onCreate$0(View v, WindowInsetsCompat insets) {
        Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
        v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
        return insets;
    }

    private void getData() {
        this.fromGallery = getIntent().getBooleanExtra(GlobalConstant.FROM_GALLERY, false);
        if (this.fromGallery) {
            this.imagePath = getIntent().getStringExtra(GlobalConstant.IMAGE_PATH);
        } else {
            this.imgResource = getIntent().getIntExtra(GlobalConstant.IMAGE_PATH, R.drawable.aesthetics_1);
        }
    }

    private void initListener() {
        this.binding.btnBack.setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.activities.GuideActivity$$ExternalSyntheticLambda0
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                GuideActivity.this.m139xc6ff4e85(view);
            }
        });
        this.binding.btnContinue.setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.activities.GuideActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                if (SystemClock.elapsedRealtime() - GuideActivity.this.mLastClickTime >= 1000) {
                    GuideActivity.this.mLastClickTime = SystemClock.elapsedRealtime();
                    Intent intent = new Intent(GuideActivity.this, CameraActivity.class);
                    intent.putExtra(GlobalConstant.FROM_GALLERY, GuideActivity.this.fromGallery);
                    if (GuideActivity.this.fromGallery) {
                        intent.putExtra(GlobalConstant.IMAGE_PATH, GuideActivity.this.imagePath);
                    } else {
                        intent.putExtra(GlobalConstant.IMAGE_PATH, GuideActivity.this.imgResource);
                    }
                    GuideActivity.this.startActivity(intent);
                    GuideActivity.this.finish();
                }
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$initListener$1$com-example-ardrawsketch-sketch-activities-GuideActivity  reason: not valid java name */
    public /* synthetic */ void m139xc6ff4e85(View v) {
        finish();
    }
}
