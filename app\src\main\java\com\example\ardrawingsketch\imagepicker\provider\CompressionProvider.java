package com.example.ardrawingsketch.imagepicker.provider;

import android.graphics.Bitmap;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.imagepicker.ImagePicker;
import com.example.ardrawingsketch.imagepicker.ImagePickerActivity;
import com.example.ardrawingsketch.imagepicker.util.ExifDataCopier;
import com.example.ardrawingsketch.imagepicker.util.FileUtil;
import com.example.ardrawingsketch.imagepicker.util.ImageUtil;
import com.google.android.material.internal.ViewUtils;
import java.io.File;
import java.util.List;
import kotlin.Metadata;
import kotlin.Pair;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.StringsKt;


public final class CompressionProvider extends BaseProvider {
    public static final Companion Companion = new Companion(null);
    private static final String TAG = CompressionProvider.class.getSimpleName();
    private final File mFileDir;
    private final long mMaxFileSize;
    private final int mMaxHeight;
    private final int mMaxWidth;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public CompressionProvider(ImagePickerActivity activity) {
        super(activity);
        Intrinsics.checkNotNullParameter(activity, "activity");
        Bundle bundle = activity.getIntent().getExtras();
        bundle = bundle == null ? new Bundle() : bundle;
        this.mMaxWidth = bundle.getInt(ImagePicker.EXTRA_MAX_WIDTH, 0);
        this.mMaxHeight = bundle.getInt(ImagePicker.EXTRA_MAX_HEIGHT, 0);
        this.mMaxFileSize = bundle.getLong(ImagePicker.EXTRA_IMAGE_MAX_SIZE, 0L);
        String fileDir = bundle.getString(ImagePicker.EXTRA_SAVE_DIRECTORY);
        this.mFileDir = getFileDir(fileDir);
    }


    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        private Companion() {
        }
    }

    private final boolean isCompressEnabled() {
        return this.mMaxFileSize > 0;
    }

    private final boolean isCompressionRequired(File file) {
        boolean status = isCompressEnabled() && getSizeDiff(file) > 0;
        if (!status && this.mMaxWidth > 0 && this.mMaxHeight > 0) {
            Pair resolution = FileUtil.INSTANCE.getImageResolution(file);
            return resolution.getFirst().intValue() > this.mMaxWidth || resolution.getSecond().intValue() > this.mMaxHeight;
        }
        return status;
    }

    public final boolean isCompressionRequired(Uri uri) {
        Intrinsics.checkNotNullParameter(uri, "uri");
        boolean status = isCompressEnabled() && getSizeDiff(uri) > 0;
        if (!status && this.mMaxWidth > 0 && this.mMaxHeight > 0) {
            Pair resolution = FileUtil.INSTANCE.getImageResolution(this, uri);
            return resolution.getFirst().intValue() > this.mMaxWidth || resolution.getSecond().intValue() > this.mMaxHeight;
        }
        return status;
    }

    private final long getSizeDiff(File file) {
        return file.length() - this.mMaxFileSize;
    }

    private final long getSizeDiff(Uri uri) {
        long length = FileUtil.INSTANCE.getImageSize(this, uri);
        return length - this.mMaxFileSize;
    }

    public final void compress(Uri uri) {
        Intrinsics.checkNotNullParameter(uri, "uri");
        startCompressionWorker(uri);
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [com.example.ardrawingsketch.imagepicker.provider.CompressionProvider$startCompressionWorker$1] */
    private final void startCompressionWorker(Uri uri) {
        new AsyncTask<Uri, Void, File>() { // from class: com.example.ardrawingsketch.imagepicker.provider.CompressionProvider$startCompressionWorker$1
            /* JADX INFO: Access modifiers changed from: protected */
            @Override // android.os.AsyncTask
            public File doInBackground(Uri... params) {
                File startCompression;
                Intrinsics.checkNotNullParameter(params, "params");
                File file = FileUtil.INSTANCE.getTempFile(CompressionProvider.this, params[0]);
                if (file == null) {
                    return null;
                }
                startCompression = CompressionProvider.this.startCompression(file);
                return startCompression;
            }

            /* JADX INFO: Access modifiers changed from: protected */
            @Override // android.os.AsyncTask
            public void onPostExecute(File file) {
                super.onPostExecute((CompressionProvider$startCompressionWorker$1) file);
                if (file != null) {
                    CompressionProvider.this.handleResult(file);
                } else {
                    CompressionProvider.this.setError(R.string.error_failed_to_compress_image);
                }
            }
        }.execute(uri);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final File startCompression(File file) {
        int i;
        File newFile = null;
        int attempt = 0;
        int lastAttempt = 0;
        do {
            if (newFile != null) {
                newFile.delete();
            }
            newFile = applyCompression(file, attempt);
            if (newFile == null) {
                if (attempt > 0) {
                    return applyCompression(file, lastAttempt);
                }
                return null;
            }
            lastAttempt = attempt;
            if (this.mMaxFileSize > 0) {
                long diff = getSizeDiff(newFile);
                if (diff > 1048576) {
                    i = 3;
                } else {
                    i = diff > 512000 ? 2 : 1;
                }
                attempt += i;
            } else {
                attempt++;
            }
        } while (isCompressionRequired(newFile));
        ExifDataCopier.INSTANCE.copyExif(file, newFile);
        return newFile;
    }

    private final File applyCompression(File file, int attempt) {
        Bitmap.CompressFormat format;
        List resList = resolutionList();
        if (attempt >= resList.size()) {
            return null;
        }
        int[] resolution = resList.get(attempt);
        int maxWidth = resolution[0];
        int maxHeight = resolution[1];
        if (this.mMaxWidth > 0 && this.mMaxHeight > 0 && (maxWidth > this.mMaxWidth || maxHeight > this.mMaxHeight)) {
            maxHeight = this.mMaxHeight;
            maxWidth = this.mMaxWidth;
        }
        Bitmap.CompressFormat format2 = Bitmap.CompressFormat.JPEG;
        String absolutePath = file.getAbsolutePath();
        Intrinsics.checkNotNullExpressionValue(absolutePath, "getAbsolutePath(...)");
        if (!StringsKt.endsWith$default(absolutePath, ".png", false, 2, (Object) null)) {
            format = format2;
        } else {
            Bitmap.CompressFormat format3 = Bitmap.CompressFormat.PNG;
            format = format3;
        }
        String extension = FileUtil.INSTANCE.getImageExtension(file);
        File compressFile = FileUtil.INSTANCE.getImageFile(this.mFileDir, extension);
        if (compressFile != null) {
            String absolutePath2 = compressFile.getAbsolutePath();
            Intrinsics.checkNotNullExpressionValue(absolutePath2, "getAbsolutePath(...)");
            return ImageUtil.INSTANCE.compressImage(file, maxWidth, maxHeight, format, absolutePath2);
        }
        return null;
    }

    private final List<int[]> resolutionList() {
        return CollectionsKt.listOf((Object[]) new int[][]{new int[]{2448, 3264}, new int[]{2008, 3032}, new int[]{1944, 2580}, new int[]{1680, 2240}, new int[]{1536, 2048}, new int[]{1200, 1600}, new int[]{1024, 1392}, new int[]{960, 1280}, new int[]{ViewUtils.EDGE_TO_EDGE_FLAGS, 1024}, new int[]{600, 800}, new int[]{480, 640}, new int[]{240, 320}, new int[]{120, 160}, new int[]{60, 80}, new int[]{30, 40}});
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final void handleResult(File file) {
        ImagePickerActivity activity = getActivity();
        Uri fromFile = Uri.fromFile(file);
        Intrinsics.checkNotNullExpressionValue(fromFile, "fromFile(...)");
        activity.setCompressedImage(fromFile);
    }
}
