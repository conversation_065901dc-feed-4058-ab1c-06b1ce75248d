package com.example.ardrawingsketch.imagepicker.provider;

import android.graphics.Bitmap;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.util.Log;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.imagepicker.ImagePicker;
import com.example.ardrawingsketch.imagepicker.ImagePickerActivity;
import com.example.ardrawingsketch.imagepicker.util.ExifDataCopier;
import com.example.ardrawingsketch.imagepicker.util.FileUtil;
import com.example.ardrawingsketch.imagepicker.util.ImageUtil;
import java.io.File;
import java.util.Arrays;
import java.util.List;

/**
 * مزود ضغط الصور - يقوم بضغط وتحسين الصور المختارة
 * يوفر وظائف متقدمة لضغط الصور مع الحفاظ على الجودة والحد من حجم الملف
 *
 * الميزات الرئيسية:
 * - ضغط الصور بناءً على الحد الأقصى للعرض والارتفاع
 * - تحديد الحد الأقصى لحجم الملف
 * - الحفاظ على بيانات EXIF المهمة
 * - ضغط تدريجي للحصول على أفضل نتيجة
 * - معالجة غير متزامنة لتجنب تجميد واجهة المستخدم
 *
 * يستخدم خوارزميات ضغط متقدمة لتحقيق التوازن بين الجودة وحجم الملف
 */
public final class CompressionProvider extends BaseProvider {

    /** علامة التسجيل لهذه الفئة */
    private static final String TAG = "CompressionProvider";

    // ===== إعدادات الضغط =====
    /** مجلد حفظ الصور المضغوطة */
    private final File mFileDir;
    /** الحد الأقصى لحجم الملف بالبايت */
    private final long mMaxFileSize;
    /** الحد الأقصى لارتفاع الصورة بالبكسل */
    private final int mMaxHeight;
    /** الحد الأقصى لعرض الصورة بالبكسل */
    private final int mMaxWidth;

    // ===== ثوابت الضغط =====
    /** جودة الضغط الافتراضية (85% - توازن جيد بين الجودة والحجم) */
    private static final int DEFAULT_COMPRESSION_QUALITY = 85;
    /** جودة الضغط العالية (95% - جودة عالية مع ضغط خفيف) */
    private static final int HIGH_COMPRESSION_QUALITY = 95;
    /** جودة الضغط المنخفضة (60% - ضغط قوي لتوفير المساحة) */
    private static final int LOW_COMPRESSION_QUALITY = 60;

    /**
     * منشئ مزود الضغط
     * يقوم بتهيئة إعدادات الضغط من البيانات المرسلة مع النشاط
     *
     * @param activity نشاط منتقي الصور الذي يحتوي على إعدادات الضغط
     */
    public CompressionProvider(ImagePickerActivity activity) {
        super(activity);

        // فحص صحة النشاط المدخل
        if (activity == null) {
            throw new IllegalArgumentException("نشاط منتقي الصور لا يمكن أن يكون null");
        }

        // استخراج إعدادات الضغط من البيانات المرسلة
        Bundle bundle = activity.getIntent().getExtras();
        if (bundle == null) {
            bundle = new Bundle();
            Log.w(TAG, "لم يتم العثور على إعدادات ضغط، سيتم استخدام الإعدادات الافتراضية");
        }

        // تهيئة إعدادات الأبعاد
        this.mMaxWidth = bundle.getInt(ImagePicker.EXTRA_MAX_WIDTH, 0);
        this.mMaxHeight = bundle.getInt(ImagePicker.EXTRA_MAX_HEIGHT, 0);

        // تهيئة إعدادات حجم الملف
        this.mMaxFileSize = bundle.getLong(ImagePicker.EXTRA_IMAGE_MAX_SIZE, 0L);

        // تهيئة مجلد الحفظ
        String fileDir = bundle.getString(ImagePicker.EXTRA_SAVE_DIRECTORY);
        this.mFileDir = getFileDir(fileDir);

        // تسجيل إعدادات الضغط المطبقة
        Log.d(TAG, "تم تهيئة مزود الضغط:");
        Log.d(TAG, "- الحد الأقصى للعرض: " + (mMaxWidth > 0 ? mMaxWidth + " بكسل" : "غير محدد"));
        Log.d(TAG, "- الحد الأقصى للارتفاع: " + (mMaxHeight > 0 ? mMaxHeight + " بكسل" : "غير محدد"));
        Log.d(TAG, "- الحد الأقصى لحجم الملف: " + (mMaxFileSize > 0 ? mMaxFileSize + " بايت" : "غير محدد"));
        Log.d(TAG, "- مجلد الحفظ: " + mFileDir.getAbsolutePath());
    }

    /**
     * دالة فحص إذا كان الضغط مفعلاً
     * تتحقق من وجود حد أقصى لحجم الملف
     *
     * @return true إذا كان الضغط مفعلاً، false إذا لم يكن
     */
    private boolean isCompressEnabled() {
        boolean enabled = this.mMaxFileSize > 0;
        Log.v(TAG, "فحص حالة الضغط: " + (enabled ? "مفعل" : "معطل"));
        return enabled;
    }

    /**
     * دالة فحص إذا كان الضغط مطلوباً لملف محدد
     * تتحقق من حجم الملف وأبعاد الصورة
     *
     * @param file ملف الصورة المراد فحصه
     * @return true إذا كان الضغط مطلوباً، false إذا لم يكن
     */
    private boolean isCompressionRequired(File file) {
        // فحص إذا كان حجم الملف يتجاوز الحد المسموح
        boolean sizeExceeded = isCompressEnabled() && getSizeDiff(file) > 0;

        // إذا لم يتجاوز الحجم، فحص إذا كانت الأبعاد تتجاوز الحد المسموح
        if (!sizeExceeded && this.mMaxWidth > 0 && this.mMaxHeight > 0) {
            try {
                // الحصول على أبعاد الصورة
                int[] resolution = FileUtil.INSTANCE.getImageResolution(file);
                int width = resolution[0];
                int height = resolution[1];

                boolean dimensionsExceeded = width > this.mMaxWidth || height > this.mMaxHeight;

                Log.d(TAG, "فحص أبعاد الصورة: " + width + "x" + height +
                        " (الحد المسموح: " + mMaxWidth + "x" + mMaxHeight + ")");

                return dimensionsExceeded;
            } catch (Exception e) {
                Log.e(TAG, "خطأ في قراءة أبعاد الصورة: " + e.getMessage());
                return false;
            }
        }

        Log.d(TAG, "فحص ضرورة الضغط للملف " + file.getName() + ": " +
                (sizeExceeded ? "مطلوب (حجم زائد)" : "غير مطلوب"));

        return sizeExceeded;
    }

    /**
     * دالة فحص إذا كان الضغط مطلوباً لصورة من URI
     * تتحقق من حجم الملف وأبعاد الصورة
     *
     * @param uri عنوان URI للصورة المراد فحصها
     * @return true إذا كان الضغط مطلوباً، false إذا لم يكن
     */
    public final boolean isCompressionRequired(Uri uri) {
        // فحص صحة URI
        if (uri == null) {
            throw new IllegalArgumentException("عنوان URI لا يمكن أن يكون null");
        }

        // فحص إذا كان حجم الملف يتجاوز الحد المسموح
        boolean sizeExceeded = isCompressEnabled() && getSizeDiff(uri) > 0;

        // إذا لم يتجاوز الحجم، فحص إذا كانت الأبعاد تتجاوز الحد المسموح
        if (!sizeExceeded && this.mMaxWidth > 0 && this.mMaxHeight > 0) {
            try {
                // الحصول على أبعاد الصورة
                int[] resolution = FileUtil.INSTANCE.getImageResolution(this, uri);
                int width = resolution[0];
                int height = resolution[1];

                boolean dimensionsExceeded = width > this.mMaxWidth || height > this.mMaxHeight;

                Log.d(TAG, "فحص أبعاد الصورة من URI: " + width + "x" + height +
                        " (الحد المسموح: " + mMaxWidth + "x" + mMaxHeight + ")");

                return dimensionsExceeded;
            } catch (Exception e) {
                Log.e(TAG, "خطأ في قراءة أبعاد الصورة من URI: " + e.getMessage());
                return false;
            }
        }

        Log.d(TAG, "فحص ضرورة الضغط لـ URI: " +
                (sizeExceeded ? "مطلوب (حجم زائد)" : "غير مطلوب"));

        return sizeExceeded;
    }

    /**
     * دالة حساب الفرق بين حجم الملف والحد الأقصى المسموح
     * إذا كانت النتيجة موجبة، فهذا يعني أن الملف يحتاج ضغط
     *
     * @param file ملف الصورة
     * @return الفرق بالبايت (موجب = يحتاج ضغط، سالب = لا يحتاج)
     */
    private long getSizeDiff(File file) {
        long currentSize = file.length();
        long difference = currentSize - this.mMaxFileSize;

        Log.v(TAG, "حجم الملف الحالي: " + currentSize + " بايت، الحد الأقصى: " +
                mMaxFileSize + " بايت، الفرق: " + difference + " بايت");

        return difference;
    }

    /**
     * دالة حساب الفرق بين حجم الملف من URI والحد الأقصى
     *
     * @param uri عنوان URI للصورة
     * @return الفرق بالبايت (موجب = يحتاج ضغط، سالب = لا يحتاج)
     */
    private long getSizeDiff(Uri uri) {
        long currentSize = FileUtil.INSTANCE.getImageSize(this, uri);
        long difference = currentSize - this.mMaxFileSize;

        Log.v(TAG, "حجم الملف من URI: " + currentSize + " بايت، الحد الأقصى: " +
                mMaxFileSize + " بايت، الفرق: " + difference + " بايت");

        return difference;
    }

    public final void compress(Uri uri) {
        Intrinsics.checkNotNullParameter(uri, "uri");
        startCompressionWorker(uri);
    }

    /*
     * JADX WARN: Type inference failed for: r0v0, types:
     * [com.example.ardrawingsketch.imagepicker.provider.
     * CompressionProvider$startCompressionWorker$1]
     */
    private final void startCompressionWorker(Uri uri) {
        new AsyncTask<Uri, Void, File>() { // from class:
                                           // com.example.ardrawingsketch.imagepicker.provider.CompressionProvider$startCompressionWorker$1
            /* JADX INFO: Access modifiers changed from: protected */
            @Override // android.os.AsyncTask
            public File doInBackground(Uri... params) {
                File startCompression;
                Intrinsics.checkNotNullParameter(params, "params");
                File file = FileUtil.INSTANCE.getTempFile(CompressionProvider.this, params[0]);
                if (file == null) {
                    return null;
                }
                startCompression = CompressionProvider.this.startCompression(file);
                return startCompression;
            }

            /* JADX INFO: Access modifiers changed from: protected */
            @Override // android.os.AsyncTask
            public void onPostExecute(File file) {
                super.onPostExecute((CompressionProvider$startCompressionWorker$1) file);
                if (file != null) {
                    CompressionProvider.this.handleResult(file);
                } else {
                    CompressionProvider.this.setError(R.string.error_failed_to_compress_image);
                }
            }
        }.execute(uri);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final File startCompression(File file) {
        int i;
        File newFile = null;
        int attempt = 0;
        int lastAttempt = 0;
        do {
            if (newFile != null) {
                newFile.delete();
            }
            newFile = applyCompression(file, attempt);
            if (newFile == null) {
                if (attempt > 0) {
                    return applyCompression(file, lastAttempt);
                }
                return null;
            }
            lastAttempt = attempt;
            if (this.mMaxFileSize > 0) {
                long diff = getSizeDiff(newFile);
                if (diff > 1048576) {
                    i = 3;
                } else {
                    i = diff > 512000 ? 2 : 1;
                }
                attempt += i;
            } else {
                attempt++;
            }
        } while (isCompressionRequired(newFile));
        ExifDataCopier.INSTANCE.copyExif(file, newFile);
        return newFile;
    }

    private final File applyCompression(File file, int attempt) {
        Bitmap.CompressFormat format;
        List resList = resolutionList();
        if (attempt >= resList.size()) {
            return null;
        }
        int[] resolution = resList.get(attempt);
        int maxWidth = resolution[0];
        int maxHeight = resolution[1];
        if (this.mMaxWidth > 0 && this.mMaxHeight > 0 && (maxWidth > this.mMaxWidth || maxHeight > this.mMaxHeight)) {
            maxHeight = this.mMaxHeight;
            maxWidth = this.mMaxWidth;
        }
        Bitmap.CompressFormat format2 = Bitmap.CompressFormat.JPEG;
        String absolutePath = file.getAbsolutePath();
        Intrinsics.checkNotNullExpressionValue(absolutePath, "getAbsolutePath(...)");
        if (!StringsKt.endsWith$default(absolutePath, ".png", false, 2, (Object) null)) {
            format = format2;
        } else {
            Bitmap.CompressFormat format3 = Bitmap.CompressFormat.PNG;
            format = format3;
        }
        String extension = FileUtil.INSTANCE.getImageExtension(file);
        File compressFile = FileUtil.INSTANCE.getImageFile(this.mFileDir, extension);
        if (compressFile != null) {
            String absolutePath2 = compressFile.getAbsolutePath();
            Intrinsics.checkNotNullExpressionValue(absolutePath2, "getAbsolutePath(...)");
            return ImageUtil.INSTANCE.compressImage(file, maxWidth, maxHeight, format, absolutePath2);
        }
        return null;
    }

    private final List<int[]> resolutionList() {
        return CollectionsKt.listOf((Object[]) new int[][] { new int[] { 2448, 3264 }, new int[] { 2008, 3032 },
                new int[] { 1944, 2580 }, new int[] { 1680, 2240 }, new int[] { 1536, 2048 }, new int[] { 1200, 1600 },
                new int[] { 1024, 1392 }, new int[] { 960, 1280 }, new int[] { ViewUtils.EDGE_TO_EDGE_FLAGS, 1024 },
                new int[] { 600, 800 }, new int[] { 480, 640 }, new int[] { 240, 320 }, new int[] { 120, 160 },
                new int[] { 60, 80 }, new int[] { 30, 40 } });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final void handleResult(File file) {
        ImagePickerActivity activity = getActivity();
        Uri fromFile = Uri.fromFile(file);
        Intrinsics.checkNotNullExpressionValue(fromFile, "fromFile(...)");
        activity.setCompressedImage(fromFile);
    }
}
