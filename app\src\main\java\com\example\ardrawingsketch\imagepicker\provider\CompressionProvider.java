package com.example.ardrawingsketch.imagepicker.provider;

import android.graphics.Bitmap;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.imagepicker.ImagePicker;
import com.example.ardrawingsketch.imagepicker.ImagePickerActivity;
import com.example.ardrawingsketch.imagepicker.util.ExifDataCopier;
import com.example.ardrawingsketch.imagepicker.util.FileUtil;
import com.example.ardrawingsketch.imagepicker.util.ImageUtil;
import com.google.android.material.internal.ViewUtils;
import java.io.File;
import java.util.List;
import kotlin.Metadata;
import kotlin.Pair;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.StringsKt;

/* compiled from: CompressionProvider.kt */
@Metadata(d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\u0010\u0015\n\u0002\b\u0003\u0018\u0000 \u001e2\u00020\u0001:\u0001\u001eB\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003¢\u0006\u0004\b\u0004\u0010\u0005J\b\u0010\r\u001a\u00020\u000eH\u0002J\u0010\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\fH\u0002J\u000e\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u0012J\u0010\u0010\u0013\u001a\u00020\n2\u0006\u0010\u0010\u001a\u00020\fH\u0002J\u0010\u0010\u0013\u001a\u00020\n2\u0006\u0010\u0011\u001a\u00020\u0012H\u0002J\u000e\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0011\u001a\u00020\u0012J\u0010\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0011\u001a\u00020\u0012H\u0003J\u0012\u0010\u0017\u001a\u0004\u0018\u00010\f2\u0006\u0010\u0010\u001a\u00020\fH\u0002J\u001a\u0010\u0018\u001a\u0004\u0018\u00010\f2\u0006\u0010\u0010\u001a\u00020\f2\u0006\u0010\u0019\u001a\u00020\u0007H\u0002J\u000e\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u001c0\u001bH\u0002J\u0010\u0010\u001d\u001a\u00020\u00152\u0006\u0010\u0010\u001a\u00020\fH\u0002R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004¢\u0006\u0002\n\u0000¨\u0006\u001f"}, d2 = {"Lcom/example/ardrawsketch/sketch/imagepicker/provider/CompressionProvider;", "Lcom/example/ardrawsketch/sketch/imagepicker/provider/BaseProvider;", "activity", "Lcom/example/ardrawsketch/sketch/imagepicker/ImagePickerActivity;", "<init>", "(Lcom/example/ardrawsketch/sketch/imagepicker/ImagePickerActivity;)V", "mMaxWidth", "", "mMaxHeight", "mMaxFileSize", "", "mFileDir", "Ljava/io/File;", "isCompressEnabled", "", "isCompressionRequired", "file", "uri", "Landroid/net/Uri;", "getSizeDiff", "compress", "", "startCompressionWorker", "startCompression", "applyCompression", "attempt", "resolutionList", "", "", "handleResult", "Companion", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes7.dex */
public final class CompressionProvider extends BaseProvider {
    public static final Companion Companion = new Companion(null);
    private static final String TAG = CompressionProvider.class.getSimpleName();
    private final File mFileDir;
    private final long mMaxFileSize;
    private final int mMaxHeight;
    private final int mMaxWidth;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public CompressionProvider(ImagePickerActivity activity) {
        super(activity);
        Intrinsics.checkNotNullParameter(activity, "activity");
        Bundle bundle = activity.getIntent().getExtras();
        bundle = bundle == null ? new Bundle() : bundle;
        this.mMaxWidth = bundle.getInt(ImagePicker.EXTRA_MAX_WIDTH, 0);
        this.mMaxHeight = bundle.getInt(ImagePicker.EXTRA_MAX_HEIGHT, 0);
        this.mMaxFileSize = bundle.getLong(ImagePicker.EXTRA_IMAGE_MAX_SIZE, 0L);
        String fileDir = bundle.getString(ImagePicker.EXTRA_SAVE_DIRECTORY);
        this.mFileDir = getFileDir(fileDir);
    }

    /* compiled from: CompressionProvider.kt */
    @Metadata(d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003R\u0016\u0010\u0004\u001a\n \u0006*\u0004\u0018\u00010\u00050\u0005X\u0082\u0004¢\u0006\u0002\n\u0000¨\u0006\u0007"}, d2 = {"Lcom/example/ardrawsketch/sketch/imagepicker/provider/CompressionProvider$Companion;", "", "<init>", "()V", "TAG", "", "kotlin.jvm.PlatformType", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
    /* loaded from: classes7.dex */
    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        private Companion() {
        }
    }

    private final boolean isCompressEnabled() {
        return this.mMaxFileSize > 0;
    }

    private final boolean isCompressionRequired(File file) {
        boolean status = isCompressEnabled() && getSizeDiff(file) > 0;
        if (!status && this.mMaxWidth > 0 && this.mMaxHeight > 0) {
            Pair resolution = FileUtil.INSTANCE.getImageResolution(file);
            return resolution.getFirst().intValue() > this.mMaxWidth || resolution.getSecond().intValue() > this.mMaxHeight;
        }
        return status;
    }

    public final boolean isCompressionRequired(Uri uri) {
        Intrinsics.checkNotNullParameter(uri, "uri");
        boolean status = isCompressEnabled() && getSizeDiff(uri) > 0;
        if (!status && this.mMaxWidth > 0 && this.mMaxHeight > 0) {
            Pair resolution = FileUtil.INSTANCE.getImageResolution(this, uri);
            return resolution.getFirst().intValue() > this.mMaxWidth || resolution.getSecond().intValue() > this.mMaxHeight;
        }
        return status;
    }

    private final long getSizeDiff(File file) {
        return file.length() - this.mMaxFileSize;
    }

    private final long getSizeDiff(Uri uri) {
        long length = FileUtil.INSTANCE.getImageSize(this, uri);
        return length - this.mMaxFileSize;
    }

    public final void compress(Uri uri) {
        Intrinsics.checkNotNullParameter(uri, "uri");
        startCompressionWorker(uri);
    }

    /* JADX WARN: Type inference failed for: r0v0, types: [com.example.ardrawingsketch.imagepicker.provider.CompressionProvider$startCompressionWorker$1] */
    private final void startCompressionWorker(Uri uri) {
        new AsyncTask<Uri, Void, File>() { // from class: com.example.ardrawingsketch.imagepicker.provider.CompressionProvider$startCompressionWorker$1
            /* JADX INFO: Access modifiers changed from: protected */
            @Override // android.os.AsyncTask
            public File doInBackground(Uri... params) {
                File startCompression;
                Intrinsics.checkNotNullParameter(params, "params");
                File file = FileUtil.INSTANCE.getTempFile(CompressionProvider.this, params[0]);
                if (file == null) {
                    return null;
                }
                startCompression = CompressionProvider.this.startCompression(file);
                return startCompression;
            }

            /* JADX INFO: Access modifiers changed from: protected */
            @Override // android.os.AsyncTask
            public void onPostExecute(File file) {
                super.onPostExecute((CompressionProvider$startCompressionWorker$1) file);
                if (file != null) {
                    CompressionProvider.this.handleResult(file);
                } else {
                    CompressionProvider.this.setError(R.string.error_failed_to_compress_image);
                }
            }
        }.execute(uri);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final File startCompression(File file) {
        int i;
        File newFile = null;
        int attempt = 0;
        int lastAttempt = 0;
        do {
            if (newFile != null) {
                newFile.delete();
            }
            newFile = applyCompression(file, attempt);
            if (newFile == null) {
                if (attempt > 0) {
                    return applyCompression(file, lastAttempt);
                }
                return null;
            }
            lastAttempt = attempt;
            if (this.mMaxFileSize > 0) {
                long diff = getSizeDiff(newFile);
                if (diff > 1048576) {
                    i = 3;
                } else {
                    i = diff > 512000 ? 2 : 1;
                }
                attempt += i;
            } else {
                attempt++;
            }
        } while (isCompressionRequired(newFile));
        ExifDataCopier.INSTANCE.copyExif(file, newFile);
        return newFile;
    }

    private final File applyCompression(File file, int attempt) {
        Bitmap.CompressFormat format;
        List resList = resolutionList();
        if (attempt >= resList.size()) {
            return null;
        }
        int[] resolution = resList.get(attempt);
        int maxWidth = resolution[0];
        int maxHeight = resolution[1];
        if (this.mMaxWidth > 0 && this.mMaxHeight > 0 && (maxWidth > this.mMaxWidth || maxHeight > this.mMaxHeight)) {
            maxHeight = this.mMaxHeight;
            maxWidth = this.mMaxWidth;
        }
        Bitmap.CompressFormat format2 = Bitmap.CompressFormat.JPEG;
        String absolutePath = file.getAbsolutePath();
        Intrinsics.checkNotNullExpressionValue(absolutePath, "getAbsolutePath(...)");
        if (!StringsKt.endsWith$default(absolutePath, ".png", false, 2, (Object) null)) {
            format = format2;
        } else {
            Bitmap.CompressFormat format3 = Bitmap.CompressFormat.PNG;
            format = format3;
        }
        String extension = FileUtil.INSTANCE.getImageExtension(file);
        File compressFile = FileUtil.INSTANCE.getImageFile(this.mFileDir, extension);
        if (compressFile != null) {
            String absolutePath2 = compressFile.getAbsolutePath();
            Intrinsics.checkNotNullExpressionValue(absolutePath2, "getAbsolutePath(...)");
            return ImageUtil.INSTANCE.compressImage(file, maxWidth, maxHeight, format, absolutePath2);
        }
        return null;
    }

    private final List<int[]> resolutionList() {
        return CollectionsKt.listOf((Object[]) new int[][]{new int[]{2448, 3264}, new int[]{2008, 3032}, new int[]{1944, 2580}, new int[]{1680, 2240}, new int[]{1536, 2048}, new int[]{1200, 1600}, new int[]{1024, 1392}, new int[]{960, 1280}, new int[]{ViewUtils.EDGE_TO_EDGE_FLAGS, 1024}, new int[]{600, 800}, new int[]{480, 640}, new int[]{240, 320}, new int[]{120, 160}, new int[]{60, 80}, new int[]{30, 40}});
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final void handleResult(File file) {
        ImagePickerActivity activity = getActivity();
        Uri fromFile = Uri.fromFile(file);
        Intrinsics.checkNotNullExpressionValue(fromFile, "fromFile(...)");
        activity.setCompressedImage(fromFile);
    }
}
