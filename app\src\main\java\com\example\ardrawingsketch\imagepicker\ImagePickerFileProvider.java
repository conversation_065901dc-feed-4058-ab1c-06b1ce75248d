package com.example.ardrawingsketch.imagepicker;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.FileProvider;
import kotlin.Metadata;

/* compiled from: ImagePickerFileProvider.kt */
@Metadata(d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u0007¢\u0006\u0004\b\u0002\u0010\u0003¨\u0006\u0004"}, d2 = {"Lcom/example/ardrawsketch/sketch/imagepicker/ImagePickerFileProvider;", "Landroidx/core/content/FileProvider;", "<init>", "()V", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes9.dex */
public final class ImagePickerFileProvider extends FileProvider {
}
