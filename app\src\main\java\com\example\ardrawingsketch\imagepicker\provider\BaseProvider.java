package com.example.ardrawingsketch.imagepicker.provider;

import android.content.ContextWrapper;
import android.os.Bundle;
import android.os.Environment;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.example.ardrawingsketch.imagepicker.ImagePickerActivity;
import java.io.File;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;


public abstract class BaseProvider extends ContextWrapper {
    private final ImagePickerActivity activity;

    /* JADX INFO: Access modifiers changed from: protected */
    public final ImagePickerActivity getActivity() {
        return this.activity;
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public BaseProvider(ImagePickerActivity activity) {
        super(activity);
        Intrinsics.checkNotNullParameter(activity, "activity");
        this.activity = activity;
    }

    public final File getFileDir(String path) {
        if (path != null) {
            return new File(path);
        }
        File externalFilesDir = getExternalFilesDir(Environment.DIRECTORY_DCIM);
        if (externalFilesDir == null) {
            externalFilesDir = this.activity.getFilesDir();
        }
        Intrinsics.checkNotNull(externalFilesDir);
        return externalFilesDir;
    }

    /* JADX INFO: Access modifiers changed from: protected */
    public final void setError(String error) {
        Intrinsics.checkNotNullParameter(error, "error");
        onFailure();
        this.activity.setError(error);
    }

    /* JADX INFO: Access modifiers changed from: protected */
    public final void setError(int errorRes) {
        String string = getString(errorRes);
        Intrinsics.checkNotNullExpressionValue(string, "getString(...)");
        setError(string);
    }

    /* JADX INFO: Access modifiers changed from: protected */
    public final void setResultCancel() {
        onFailure();
        this.activity.setResultCancel();
    }

    protected void onFailure() {
    }

    public void onSaveInstanceState(Bundle outState) {
        Intrinsics.checkNotNullParameter(outState, "outState");
    }

    public void onRestoreInstanceState(Bundle savedInstanceState) {
    }
}
