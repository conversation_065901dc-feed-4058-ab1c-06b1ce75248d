package com.example.ardrawingsketch.imagepicker.provider;

import android.content.ContextWrapper;
import android.os.Bundle;
import android.os.Environment;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.example.ardrawingsketch.imagepicker.ImagePickerActivity;
import java.io.File;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: BaseProvider.kt */
@Metadata(d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b&\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003¢\u0006\u0004\b\u0004\u0010\u0005J\u0010\u0010\b\u001a\u00020\t2\b\u0010\n\u001a\u0004\u0018\u00010\u000bJ\u0010\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000bH\u0004J\u0010\u0010\f\u001a\u00020\r2\u0006\u0010\u000f\u001a\u00020\u0010H\u0004J\b\u0010\u0011\u001a\u00020\rH\u0004J\b\u0010\u0012\u001a\u00020\rH\u0014J\u0010\u0010\u0013\u001a\u00020\r2\u0006\u0010\u0014\u001a\u00020\u0015H\u0016J\u0012\u0010\u0016\u001a\u00020\r2\b\u0010\u0017\u001a\u0004\u0018\u00010\u0015H\u0016R\u0014\u0010\u0002\u001a\u00020\u0003X\u0084\u0004¢\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007¨\u0006\u0018"}, d2 = {"Lcom/example/ardrawsketch/sketch/imagepicker/provider/BaseProvider;", "Landroid/content/ContextWrapper;", "activity", "Lcom/example/ardrawsketch/sketch/imagepicker/ImagePickerActivity;", "<init>", "(Lcom/example/ardrawsketch/sketch/imagepicker/ImagePickerActivity;)V", "getActivity", "()Lcom/example/ardrawsketch/sketch/imagepicker/ImagePickerActivity;", "getFileDir", "Ljava/io/File;", "path", "", "setError", "", "error", "errorRes", "", "setResultCancel", "onFailure", "onSaveInstanceState", "outState", "Landroid/os/Bundle;", "onRestoreInstanceState", "savedInstanceState", "app_debug"}, k = 1, mv = {2, 1, 0}, xi = ConstraintLayout.LayoutParams.Table.LAYOUT_CONSTRAINT_VERTICAL_CHAINSTYLE)
/* loaded from: classes7.dex */
public abstract class BaseProvider extends ContextWrapper {
    private final ImagePickerActivity activity;

    /* JADX INFO: Access modifiers changed from: protected */
    public final ImagePickerActivity getActivity() {
        return this.activity;
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public BaseProvider(ImagePickerActivity activity) {
        super(activity);
        Intrinsics.checkNotNullParameter(activity, "activity");
        this.activity = activity;
    }

    public final File getFileDir(String path) {
        if (path != null) {
            return new File(path);
        }
        File externalFilesDir = getExternalFilesDir(Environment.DIRECTORY_DCIM);
        if (externalFilesDir == null) {
            externalFilesDir = this.activity.getFilesDir();
        }
        Intrinsics.checkNotNull(externalFilesDir);
        return externalFilesDir;
    }

    /* JADX INFO: Access modifiers changed from: protected */
    public final void setError(String error) {
        Intrinsics.checkNotNullParameter(error, "error");
        onFailure();
        this.activity.setError(error);
    }

    /* JADX INFO: Access modifiers changed from: protected */
    public final void setError(int errorRes) {
        String string = getString(errorRes);
        Intrinsics.checkNotNullExpressionValue(string, "getString(...)");
        setError(string);
    }

    /* JADX INFO: Access modifiers changed from: protected */
    public final void setResultCancel() {
        onFailure();
        this.activity.setResultCancel();
    }

    protected void onFailure() {
    }

    public void onSaveInstanceState(Bundle outState) {
        Intrinsics.checkNotNullParameter(outState, "outState");
    }

    public void onRestoreInstanceState(Bundle savedInstanceState) {
    }
}
