package com.example.ardrawingsketch.activities;

import android.app.ProgressDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.provider.MediaStore;
import android.util.Log;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;
import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.location.LocationRequestCompat;
import androidx.core.view.OnApplyWindowInsetsListener;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import com.example.ardrawingsketch.GlobalConstant;
import com.example.ardrawingsketch.R;
import com.example.ardrawingsketch.Utils;
import com.example.ardrawingsketch.databinding.ActivityCameraBinding;
import com.example.ardrawingsketch.imagepicker.ImagePicker;
import com.otaliastudios.cameraview.CameraException;
import com.otaliastudios.cameraview.CameraListener;
import com.otaliastudios.cameraview.CameraOptions;
import com.otaliastudios.cameraview.controls.Flash;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import jp.co.cyberagent.android.gpuimage.GPUImage;

/* loaded from: classes6.dex */
public class CameraActivity extends AppCompatActivity {
    String[] PERMISSIONS;
    ActivityCameraBinding binding;
    Bitmap bmOriginal;
    ExecutorService executorService;
    boolean fromGallery;
    String imagePath;
    private boolean isFlashSupported;
    Animation pushAnim;
    ProgressDialog ringProgressDialog;
    Bitmap ConvertedBitmap = null;
    boolean getPhotoFromGallery = false;
    private boolean isTorchOn = false;
    boolean is_edit_sketch = false;
    boolean is_lock = false;

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        this.binding = ActivityCameraBinding.inflate(getLayoutInflater());
        setContentView(this.binding.getRoot());
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), new OnApplyWindowInsetsListener() { // from class: com.example.ardrawingsketch.activities.CameraActivity$$ExternalSyntheticLambda0
            @Override // androidx.core.view.OnApplyWindowInsetsListener
            public final WindowInsetsCompat onApplyWindowInsets(View view, WindowInsetsCompat windowInsetsCompat) {
                return CameraActivity.lambda$onCreate$0(view, windowInsetsCompat);
            }
        });
        this.executorService = Executors.newSingleThreadExecutor();
        checkPermission();
        this.pushAnim = AnimationUtils.loadAnimation(this, R.anim.view_push);
        this.binding.objImage.setEnabled(true);
        this.is_lock = false;
        this.binding.relLock.setImageResource(R.drawable.unlock);
        getData();
        initListener();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    public static /* synthetic */ WindowInsetsCompat lambda$onCreate$0(View v, WindowInsetsCompat insets) {
        Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
        v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
        return insets;
    }

    private void checkPermission() {
        if (Build.VERSION.SDK_INT >= 33) {
            this.PERMISSIONS = new String[]{"android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.READ_MEDIA_IMAGES"};
        } else {
            this.PERMISSIONS = new String[]{"android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"};
        }
    }

    private void initListener() {
        this.binding.animationView.setVisibility(View.VISIBLE);
        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() { // from class: com.example.ardrawingsketch.activities.CameraActivity$$ExternalSyntheticLambda5
            @Override // java.lang.Runnable
            public final void run() {
                CameraActivity.this.m127x560cb83a();
            }
        }, 5000L);
        this.binding.relCamera.setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.activities.CameraActivity$$ExternalSyntheticLambda6
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                CameraActivity.this.m128x66c284fb(view);
            }
        });
        this.binding.relGallery.setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.activities.CameraActivity$$ExternalSyntheticLambda7
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                CameraActivity.this.m129x777851bc(view);
            }
        });
        this.binding.relFlip.setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.activities.CameraActivity$$ExternalSyntheticLambda8
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                CameraActivity.this.m130x882e1e7d(view);
            }
        });
        this.binding.relEditRound.setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.activities.CameraActivity$$ExternalSyntheticLambda9
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                CameraActivity.this.m131x98e3eb3e(view);
            }
        });
        this.binding.relLock.setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.activities.CameraActivity$$ExternalSyntheticLambda10
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                CameraActivity.this.m132xa999b7ff(view);
            }
        });
        this.binding.alphaSeek.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() { // from class: com.example.ardrawingsketch.activities.CameraActivity.1
            @Override // android.widget.SeekBar.OnSeekBarChangeListener
            public void onStartTrackingTouch(SeekBar seekBar) {
            }

            @Override // android.widget.SeekBar.OnSeekBarChangeListener
            public void onStopTrackingTouch(SeekBar seekBar) {
            }

            @Override // android.widget.SeekBar.OnSeekBarChangeListener
            public void onProgressChanged(SeekBar seekBar, int i, boolean z) {
                CameraActivity.this.binding.objImage.setAlpha((CameraActivity.this.binding.alphaSeek.getMax() - i) / 10.0f);
                TextView textView = CameraActivity.this.binding.opacityText;
                textView.setText((i * 10) + " %");
            }
        });
        this.binding.relFlash.setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.activities.CameraActivity$$ExternalSyntheticLambda11
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                CameraActivity.this.m133xba4f84c0(view);
            }
        });
        this.binding.backCamera.setOnClickListener(new View.OnClickListener() { // from class: com.example.ardrawingsketch.activities.CameraActivity$$ExternalSyntheticLambda12
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                CameraActivity.this.m134xcb055181(view);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$initListener$1$com-example-ardrawsketch-sketch-activities-CameraActivity  reason: not valid java name */
    public /* synthetic */ void m127x560cb83a() {
        this.binding.animationView.setVisibility(View.GONE);
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$initListener$2$com-example-ardrawsketch-sketch-activities-CameraActivity  reason: not valid java name */
    public /* synthetic */ void m128x66c284fb(View view) {
        choiceCamera();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$initListener$3$com-example-ardrawsketch-sketch-activities-CameraActivity  reason: not valid java name */
    public /* synthetic */ void m129x777851bc(View view) {
        choiceGallery();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$initListener$4$com-example-ardrawsketch-sketch-activities-CameraActivity  reason: not valid java name */
    public /* synthetic */ void m130x882e1e7d(View view) {
        choiceFlip();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$initListener$5$com-example-ardrawsketch-sketch-activities-CameraActivity  reason: not valid java name */
    public /* synthetic */ void m131x98e3eb3e(View view) {
        ConvertBorderBitmap();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$initListener$6$com-example-ardrawsketch-sketch-activities-CameraActivity  reason: not valid java name */
    public /* synthetic */ void m132xa999b7ff(View view) {
        choiceLock();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$initListener$7$com-example-ardrawsketch-sketch-activities-CameraActivity  reason: not valid java name */
    public /* synthetic */ void m133xba4f84c0(View view) {
        switchFlash();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$initListener$8$com-example-ardrawsketch-sketch-activities-CameraActivity  reason: not valid java name */
    public /* synthetic */ void m134xcb055181(View v) {
        finish();
    }

    private void getData() {
        this.fromGallery = getIntent().getBooleanExtra(GlobalConstant.FROM_GALLERY, false);
        if (this.fromGallery) {
            this.imagePath = getIntent().getExtras().getString(GlobalConstant.IMAGE_PATH);
            this.bmOriginal = Utils.getBitmap(this.imagePath);
        } else {
            int drawable = getIntent().getExtras().getInt(GlobalConstant.IMAGE_PATH);
            this.bmOriginal = Utils.getBitmapFromDrawable(this, drawable);
        }
        if (this.bmOriginal != null) {
            this.binding.objImage.setImageBitmap(this.bmOriginal);
            this.is_edit_sketch = false;
            this.binding.relEditRound.setImageResource(R.drawable.sketch_off);
        } else {
            Toast.makeText(this, "Some issue with this image try another one.", Toast.LENGTH_LONG).show();
        }
        this.binding.objImage.setAlpha(0.6f);
        this.binding.alphaSeek.setProgress(4);
        TextView textView = this.binding.opacityText;
        textView.setText((this.binding.alphaSeek.getProgress() * 10) + " %");
    }

    public void ConvertBorderBitmap() {
        final GPUImage gPUImage = new GPUImage(this);
        ProgressDialog progressDialog = new ProgressDialog(this);
        this.ringProgressDialog = progressDialog;
        progressDialog.setMessage("Convert Bitmap");
        this.ringProgressDialog.setCancelable(false);
        runOnUiThread(new Runnable() { // from class: com.example.ardrawingsketch.activities.CameraActivity$$ExternalSyntheticLambda13
            @Override // java.lang.Runnable
            public final void run() {
                CameraActivity.this.m124xb810dbf2();
            }
        });
        this.executorService.execute(new Runnable() { // from class: com.example.ardrawingsketch.activities.CameraActivity$$ExternalSyntheticLambda1
            @Override // java.lang.Runnable
            public final void run() {
                CameraActivity.this.m122x96a48d9e(gPUImage);
            }
        });
        this.ringProgressDialog.setOnDismissListener(new DialogInterface.OnDismissListener() { // from class: com.example.ardrawingsketch.activities.CameraActivity$$ExternalSyntheticLambda2
            @Override // android.content.DialogInterface.OnDismissListener
            public final void onDismiss(DialogInterface dialogInterface) {
                CameraActivity.this.m123xa75a5a5f(dialogInterface);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$ConvertBorderBitmap$9$com-example-ardrawsketch-sketch-activities-CameraActivity  reason: not valid java name */
    public /* synthetic */ void m124xb810dbf2() {
        this.ringProgressDialog.show();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$ConvertBorderBitmap$11$com-example-ardrawsketch-sketch-activities-CameraActivity  reason: not valid java name */
    public /* synthetic */ void m123xa75a5a5f(DialogInterface dialogInterface) {
        choiceDismissDialog();
    }

    public void choiceDismissDialog() {
        if (!this.is_edit_sketch) {
            if (this.ConvertedBitmap != null) {
                this.is_edit_sketch = true;
                this.binding.objImage.setImageBitmap(this.ConvertedBitmap);
                this.binding.relEditRound.setImageResource(R.drawable.sketch_on);
                return;
            }
            Toast.makeText(this, "Can't Convert this image try with another",Toast.LENGTH_LONG).show();
        } else if (this.bmOriginal != null) {
            this.is_edit_sketch = false;
            this.binding.objImage.setImageBitmap(this.bmOriginal);
            this.binding.relEditRound.setImageResource(R.drawable.sketch_off);
        } else {
            Toast.makeText(this, "Can't Convert this image try with another",Toast.LENGTH_LONG).show();
        }
    }

    /* renamed from: convertImageBitmap */
    public void m122x96a48d9e(GPUImage gPUImage) {
        try {
            if (!this.is_edit_sketch) {
                gPUImage.setImage(this.bmOriginal);
                Bitmap bitmapWithFilterApplied = gPUImage.getBitmapWithFilterApplied();
                if (bitmapWithFilterApplied != null) {
                    this.ConvertedBitmap = Utils.getBitmapWithTransparentBG(bitmapWithFilterApplied, -1);
                } else {
                    runOnUiThread(new Runnable() { // from class: com.example.ardrawingsketch.activities.CameraActivity$$ExternalSyntheticLambda3
                        @Override // java.lang.Runnable
                        public final void run() {
                            CameraActivity.this.m125x12b3f49d();
                        }
                    });
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        runOnUiThread(new Runnable() { // from class: com.example.ardrawingsketch.activities.CameraActivity$$ExternalSyntheticLambda4
            @Override // java.lang.Runnable
            public final void run() {
                CameraActivity.this.m126x2369c15e();
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$convertImageBitmap$12$com-example-ardrawsketch-sketch-activities-CameraActivity  reason: not valid java name */
    public /* synthetic */ void m125x12b3f49d() {
        Toast.makeText(this, "Can't Convert this image try with another",Toast.LENGTH_LONG).show();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* renamed from: lambda$convertImageBitmap$13$com-example-ardrawsketch-sketch-activities-CameraActivity  reason: not valid java name */
    public /* synthetic */ void m126x2369c15e() {
        this.ringProgressDialog.dismiss();
    }

    public void choiceFlip() {
        Bitmap flip = Utils.flip(this.bmOriginal, 2);
        this.bmOriginal = flip;
        if (flip != null) {
            this.binding.objImage.setImageBitmap(this.bmOriginal);
        }
    }

    public void choiceLock() {
        if (!this.is_lock) {
            this.binding.objImage.setEnabled(false);
            this.is_lock = true;
            this.binding.relLock.setImageResource(R.drawable.lock);
            return;
        }
        this.binding.objImage.setEnabled(true);
        this.is_lock = false;
        this.binding.relLock.setImageResource(R.drawable.unlock);
    }

    public void choiceGallery() {
        this.getPhotoFromGallery = true;
        if (Utils.checkPermission(this, this.PERMISSIONS)) {
            startActivityForResult(new Intent("android.intent.action.PICK", MediaStore.Images.Media.EXTERNAL_CONTENT_URI), 1);
        } else {
            Utils.getPermissions(this, this.PERMISSIONS, 1200);
        }
    }

    public void switchFlash() {
        if (this.isTorchOn) {
            this.isTorchOn = false;
            this.binding.relFlash.setImageResource(R.drawable.flash_off);
            this.binding.cameraView.setFlash(Flash.OFF);
            return;
        }
        this.isTorchOn = true;
        this.binding.relFlash.setImageResource(R.drawable.flash_on);
        this.binding.cameraView.setFlash(Flash.TORCH);
    }

    public void choiceCamera() {
        this.getPhotoFromGallery = false;
        if (Utils.checkPermission(this, this.PERMISSIONS)) {
            ImagePicker.with(this).cameraOnly().saveDir(getExternalFilesDir(Environment.DIRECTORY_DCIM)).start(103);
        } else {
            Utils.getPermissions(this, this.PERMISSIONS, 1200);
        }
    }

    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, android.app.Activity
    public void onRequestPermissionsResult(int i, String[] strArr, int[] iArr) {
        super.onRequestPermissionsResult(i, strArr, iArr);
        if (i == 1200) {
            if (!Utils.checkPermission(this, this.PERMISSIONS)) {
                Toast.makeText(this, "Please grant the permission to proceed",Toast.LENGTH_LONG).show();
            } else if (this.getPhotoFromGallery) {
                ImagePicker.with(this).galleryOnly().start(LocationRequestCompat.QUALITY_BALANCED_POWER_ACCURACY);
            } else {
                ImagePicker.with(this).cameraOnly().saveDir(getExternalFilesDir(Environment.DIRECTORY_DCIM)).start(103);
            }
        }
        if (i != 2200) {
            return;
        }
        if (Utils.checkPermission(this, this.PERMISSIONS)) {
            this.binding.cameraView.open();
            this.binding.cameraView.clearFocus();
            setupCameraCallbacks();
            return;
        }
        Toast.makeText(this, "Please grant permissions to proceed ",Toast.LENGTH_LONG).show();
        finish();
    }

    public void setupFlashButton() {
        if (this.isFlashSupported) {
            this.binding.relFlash.setVisibility(View.VISIBLE);
            if (this.binding.cameraView.getFlash() != Flash.TORCH) {
                this.binding.relFlash.setImageResource(R.drawable.flash_off);
                return;
            } else {
                this.binding.relFlash.setImageResource(R.drawable.flash_on);
                return;
            }
        }
        this.binding.relFlash.setVisibility(View.GONE);
    }

    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, android.app.Activity
    public void onActivityResult(int i, int i2, Intent intent) {
        super.onActivityResult(i, i2, intent);
        if (intent != null && i2 == -1 && intent.getData().getPath() != null) {
            Log.i("TAG11515", "onActivityResult: " + intent.getData().getPath());
            this.bmOriginal = Utils.getBitmap(intent.getData().getPath());
            Log.i("TAG11515", "onActivityResult: " + this.bmOriginal);
            if (this.bmOriginal != null) {
                this.binding.objImage.setImageBitmap(this.bmOriginal);
                this.is_edit_sketch = false;
                this.binding.relEditRound.setImageResource(R.drawable.sketch_off);
                return;
            }
            Toast.makeText(this, "Some issue with this image try another one.",Toast.LENGTH_LONG).show();
        }
    }

    @Override // androidx.fragment.app.FragmentActivity, android.app.Activity
    public void onResume() {
        super.onResume();
        if (Utils.checkPermission(this, this.PERMISSIONS)) {
            this.binding.cameraView.open();
            this.binding.cameraView.clearFocus();
            setupCameraCallbacks();
            return;
        }
        Utils.getPermissions(this, this.PERMISSIONS, GlobalConstant.PERMISSION_CODE_GALLERY);
    }

    @Override // androidx.fragment.app.FragmentActivity, android.app.Activity
    public void onPause() {
        super.onPause();
        this.binding.cameraView.close();
        ProgressDialog progressDialog = this.ringProgressDialog;
        if (progressDialog != null && progressDialog.isShowing()) {
            this.ringProgressDialog.dismiss();
        }
    }

    private void setupCameraCallbacks() {
        this.binding.cameraView.addCameraListener(new CameraListener() { // from class: com.example.ardrawingsketch.activities.CameraActivity.2
            @Override // com.otaliastudios.cameraview.CameraListener
            public void onCameraOpened(CameraOptions cameraOptions) {
                super.onCameraOpened(cameraOptions);
                CameraActivity.this.isFlashSupported = cameraOptions.getSupportedFlash().contains(Flash.TORCH);
                CameraActivity.this.setupFlashButton();
            }

            @Override // com.otaliastudios.cameraview.CameraListener
            public void onCameraClosed() {
                super.onCameraClosed();
            }

            @Override // com.otaliastudios.cameraview.CameraListener
            public void onCameraError(CameraException cameraException) {
                super.onCameraError(cameraException);
            }
        });
    }
}
