package com.example.ardrawingsketch.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airbnb.lottie.LottieAnimationView;
import com.example.ardrawingsketch.R;

/* loaded from: classes3.dex */
public final class ActivitySplashBinding implements ViewBinding {
    public final ConstraintLayout main;
    private final ConstraintLayout rootView;
    public final LottieAnimationView splashGif;
    public final TextView splashText;

    private ActivitySplashBinding(ConstraintLayout rootView, ConstraintLayout main, LottieAnimationView splashGif, TextView splashText) {
        this.rootView = rootView;
        this.main = main;
        this.splashGif = splashGif;
        this.splashText = splashText;
    }

    @Override // androidx.viewbinding.ViewBinding
    public ConstraintLayout getRoot() {
        return this.rootView;
    }

    public static ActivitySplashBinding inflate(LayoutInflater inflater) {
        return inflate(inflater, null, false);
    }

    public static ActivitySplashBinding inflate(LayoutInflater inflater, ViewGroup parent, boolean attachToParent) {
        View root = inflater.inflate(R.layout.activity_splash, parent, false);
        if (attachToParent) {
            parent.addView(root);
        }
        return bind(root);
    }

    public static ActivitySplashBinding bind(View rootView) {
        ConstraintLayout main = (ConstraintLayout) rootView;
        int id = R.id.splash_gif;
        LottieAnimationView splashGif = (LottieAnimationView) ViewBindings.findChildViewById(rootView, id);
        if (splashGif != null) {
            id = R.id.splash_text;
            TextView splashText = (TextView) ViewBindings.findChildViewById(rootView, id);
            if (splashText != null) {
                return new ActivitySplashBinding((ConstraintLayout) rootView, main, splashGif, splashText);
            }
        }
        String missingId = rootView.getResources().getResourceName(id);
        throw new NullPointerException("Missing required view with ID: ".concat(missingId));
    }
}
