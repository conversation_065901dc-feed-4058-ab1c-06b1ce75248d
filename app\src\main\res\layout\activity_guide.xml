<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:id="@+id/main" android:background="@color/appmaincolorm" android:layout_width="match_parent" android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/toolbar" android:background="@color/appmaincolorm" android:layout_width="match_parent" android:layout_height="?attr/actionBarSize" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0">
        <androidx.appcompat.widget.AppCompatImageView android:id="@+id/btn_back" android:padding="12dp" android:layout_width="50dp" android:layout_height="50dp" android:src="@drawable/ic_back" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
        <TextView android:textSize="18sp" android:textColor="@color/white" android:ellipsize="end" android:gravity="center" android:id="@+id/label_help" android:layout_width="wrap_content" android:layout_height="match_parent" android:text="@string/text_ar_draw_sketch_sketch_amp_paint" android:maxLines="1" android:maxEms="12" android:fontFamily="@font/poppins_bold" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout android:background="@color/white" android:layout_width="match_parent" android:layout_height="0dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toBottomOf="@+id/toolbar">
        <androidx.cardview.widget.CardView android:id="@+id/help_video_card" android:background="@drawable/splash_logo" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginLeft="15dp" android:layout_marginTop="10dp" android:layout_marginRight="15dp" android:layout_marginHorizontal="15dp" app:cardCornerRadius="20dp" app:cardElevation="10dp" app:layout_constraintBottom_toTopOf="@+id/text_title_helpe" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0">
            <androidx.appcompat.widget.AppCompatImageView android:id="@+id/img" android:layout_width="match_parent" android:layout_height="match_parent" android:src="@drawable/splash_logo" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
        </androidx.cardview.widget.CardView>
        <TextView android:textSize="16sp" android:textStyle="bold" android:textColor="@color/black" android:gravity="center" android:id="@+id/text_title_helpe" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginBottom="9dp" android:text="@string/text_draw_with_camera" android:fontFamily="@font/poppins_bold" app:layout_constraintBottom_toTopOf="@+id/btn_continue" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toBottomOf="@+id/help_video_card"/>
        <TextView android:textSize="14sp" android:textColor="@color/black" android:gravity="center" android:id="@+id/text_message_helpe" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginLeft="50dp" android:layout_marginTop="5dp" android:layout_marginRight="50dp" android:layout_marginBottom="8dp" android:text="@string/text_message_help" android:fontFamily="@font/poppins_regular" android:layout_marginHorizontal="50dp" app:layout_constraintBottom_toTopOf="@+id/btn_continue" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toBottomOf="@+id/text_title_helpe"/>
        <TextView android:textSize="16sp" android:textColor="@color/white" android:gravity="center" android:id="@+id/btn_continue" android:background="@drawable/bg_btn_positive" android:paddingLeft="24dp" android:paddingRight="24dp" android:layout_width="wrap_content" android:layout_height="56dp" android:layout_marginBottom="24dp" android:text="@string/str_continue" android:fontFamily="@font/poppins_bold" android:paddingHorizontal="24dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toBottomOf="@+id/text_message_helpe"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
